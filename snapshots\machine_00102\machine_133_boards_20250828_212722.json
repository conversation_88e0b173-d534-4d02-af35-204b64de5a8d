[{"id": 9458, "type": {"id": 5, "name": "Pump"}, "protocolId": 14, "forceUpdate": false, "lockVersion": false, "status": "Installed", "pcbMajor": 3, "pcbMinor": 1, "pcbPatch": 0, "pumpRating": 0, "machine": {"id": 133, "name": "Swig HQ Right Machine", "serialNumber": "00102", "active": true, "inDowntime": false, "menuId": 270, "usageType": 1, "lastUpdateDT": null, "addBy": 2, "addDT": "2025-04-16 19:52:33", "modBy": 1, "modDT": "2025-05-10 00:19:47", "location": {"id": 74, "name": "<PERSON><PERSON>", "address": "1351 E State St.", "city": "Le<PERSON>", "state": "Utah", "zip": "84043", "country": "USA", "phone": "", "active": true, "enableIQ": true, "timezone": "US/Pacific", "addBy": 1, "addDT": null, "modBy": 1, "modDT": null, "organization": {"id": 72, "name": "<PERSON><PERSON>", "address": "1351 E State St.", "city": "Le<PERSON>", "state": "Utah", "zip": "84043", "country": "USA", "phone": "", "active": true, "addBy": 1, "addDT": null, "modBy": 1, "modDT": null}}, "system": {"id": 3, "name": "Toast", "external": true}, "upperModel": {"id": 10, "name": "<PERSON><PERSON> w/ <PERSON><PERSON>", "type": 1, "rev": "A"}, "lowerModel": {"id": 8, "name": "Large", "type": 2, "rev": "B"}}, "application": {"id": 1928, "filePath": "https://s3.us-east-2.amazonaws.com/backbar.applications.stage/pump/pump-3_1_7-test-swig-accurate_timer.bin", "fwMajor": 3, "fwMinor": 1, "fwPatch": 7, "canMajor": 3, "canMinor": 9, "serialMajor": 9, "serialMinor": 10, "notes": "test-swig-accurate_timer", "type": {"id": 5, "name": "Pump"}, "organization": {"id": 1, "name": "BackBar", "address": "1101 Renaissance Way NE", "city": "Atlanta", "state": "GA", "zip": "30308", "country": "USA", "phone": "(*************", "active": true, "addBy": 1, "addDT": null, "modBy": 1, "modDT": null}, "version": ""}, "previous": {"id": 1882, "filePath": "https://s3.us-east-2.amazonaws.com/backbar.applications.stage/pump/pump-3_1_6-swig_beta_1.bin", "fwMajor": 3, "fwMinor": 1, "fwPatch": 6, "canMajor": 3, "canMinor": 8, "serialMajor": 9, "serialMinor": 9, "notes": "swig_beta_1", "type": {"id": 5, "name": "Pump"}, "organization": {"id": 1, "name": "BackBar", "address": "1101 Renaissance Way NE", "city": "Atlanta", "state": "GA", "zip": "30308", "country": "USA", "phone": "(*************", "active": true, "addBy": 1, "addDT": null, "modBy": 1, "modDT": null}, "version": ""}, "scheduled": null}, {"id": 9459, "type": {"id": 5, "name": "Pump"}, "protocolId": 13, "forceUpdate": false, "lockVersion": false, "status": "Installed", "pcbMajor": 3, "pcbMinor": 1, "pcbPatch": 0, "pumpRating": 0, "machine": {"id": 133, "name": "Swig HQ Right Machine", "serialNumber": "00102", "active": true, "inDowntime": false, "menuId": 270, "usageType": 1, "lastUpdateDT": null, "addBy": 2, "addDT": "2025-04-16 19:52:33", "modBy": 1, "modDT": "2025-05-10 00:19:47", "location": {"id": 74, "name": "<PERSON><PERSON>", "address": "1351 E State St.", "city": "Le<PERSON>", "state": "Utah", "zip": "84043", "country": "USA", "phone": "", "active": true, "enableIQ": true, "timezone": "US/Pacific", "addBy": 1, "addDT": null, "modBy": 1, "modDT": null, "organization": {"id": 72, "name": "<PERSON><PERSON>", "address": "1351 E State St.", "city": "Le<PERSON>", "state": "Utah", "zip": "84043", "country": "USA", "phone": "", "active": true, "addBy": 1, "addDT": null, "modBy": 1, "modDT": null}}, "system": {"id": 3, "name": "Toast", "external": true}, "upperModel": {"id": 10, "name": "<PERSON><PERSON> w/ <PERSON><PERSON>", "type": 1, "rev": "A"}, "lowerModel": {"id": 8, "name": "Large", "type": 2, "rev": "B"}}, "application": {"id": 1928, "filePath": "https://s3.us-east-2.amazonaws.com/backbar.applications.stage/pump/pump-3_1_7-test-swig-accurate_timer.bin", "fwMajor": 3, "fwMinor": 1, "fwPatch": 7, "canMajor": 3, "canMinor": 9, "serialMajor": 9, "serialMinor": 10, "notes": "test-swig-accurate_timer", "type": {"id": 5, "name": "Pump"}, "organization": {"id": 1, "name": "BackBar", "address": "1101 Renaissance Way NE", "city": "Atlanta", "state": "GA", "zip": "30308", "country": "USA", "phone": "(*************", "active": true, "addBy": 1, "addDT": null, "modBy": 1, "modDT": null}, "version": ""}, "previous": {"id": 1882, "filePath": "https://s3.us-east-2.amazonaws.com/backbar.applications.stage/pump/pump-3_1_6-swig_beta_1.bin", "fwMajor": 3, "fwMinor": 1, "fwPatch": 6, "canMajor": 3, "canMinor": 8, "serialMajor": 9, "serialMinor": 9, "notes": "swig_beta_1", "type": {"id": 5, "name": "Pump"}, "organization": {"id": 1, "name": "BackBar", "address": "1101 Renaissance Way NE", "city": "Atlanta", "state": "GA", "zip": "30308", "country": "USA", "phone": "(*************", "active": true, "addBy": 1, "addDT": null, "modBy": 1, "modDT": null}, "version": ""}, "scheduled": null}, {"id": 9460, "type": {"id": 11, "name": "QR Reader"}, "protocolId": 608, "forceUpdate": false, "lockVersion": false, "status": "Installed", "pcbMajor": 1, "pcbMinor": 0, "pcbPatch": 1, "pumpRating": 0, "machine": {"id": 133, "name": "Swig HQ Right Machine", "serialNumber": "00102", "active": true, "inDowntime": false, "menuId": 270, "usageType": 1, "lastUpdateDT": null, "addBy": 2, "addDT": "2025-04-16 19:52:33", "modBy": 1, "modDT": "2025-05-10 00:19:47", "location": {"id": 74, "name": "<PERSON><PERSON>", "address": "1351 E State St.", "city": "Le<PERSON>", "state": "Utah", "zip": "84043", "country": "USA", "phone": "", "active": true, "enableIQ": true, "timezone": "US/Pacific", "addBy": 1, "addDT": null, "modBy": 1, "modDT": null, "organization": {"id": 72, "name": "<PERSON><PERSON>", "address": "1351 E State St.", "city": "Le<PERSON>", "state": "Utah", "zip": "84043", "country": "USA", "phone": "", "active": true, "addBy": 1, "addDT": null, "modBy": 1, "modDT": null}}, "system": {"id": 3, "name": "Toast", "external": true}, "upperModel": {"id": 10, "name": "<PERSON><PERSON> w/ <PERSON><PERSON>", "type": 1, "rev": "A"}, "lowerModel": {"id": 8, "name": "Large", "type": 2, "rev": "B"}}, "application": {"id": 1884, "filePath": "https://s3.us-east-2.amazonaws.com/backbar.applications.stage/qr_reader/qr_reader-1_0_6-swig_beta_1.bin", "fwMajor": 1, "fwMinor": 0, "fwPatch": 6, "canMajor": 3, "canMinor": 8, "serialMajor": 9, "serialMinor": 9, "notes": "swig_beta_1", "type": {"id": 11, "name": "QR Reader"}, "organization": {"id": 1, "name": "BackBar", "address": "1101 Renaissance Way NE", "city": "Atlanta", "state": "GA", "zip": "30308", "country": "USA", "phone": "(*************", "active": true, "addBy": 1, "addDT": null, "modBy": 1, "modDT": null}, "version": ""}, "previous": {"id": 1844, "filePath": "https://s3.us-east-2.amazonaws.com/backbar.applications.stage/qr_reader/qr_reader-1_0_6-beta-test_r38.bin", "fwMajor": 1, "fwMinor": 0, "fwPatch": 6, "canMajor": 3, "canMinor": 8, "serialMajor": 9, "serialMinor": 9, "notes": "beta-test_r38", "type": {"id": 11, "name": "QR Reader"}, "organization": {"id": 1, "name": "BackBar", "address": "1101 Renaissance Way NE", "city": "Atlanta", "state": "GA", "zip": "30308", "country": "USA", "phone": "(*************", "active": true, "addBy": 1, "addDT": null, "modBy": 1, "modDT": null}, "version": ""}, "scheduled": null}, {"id": 9461, "type": {"id": 1, "name": "Main"}, "protocolId": 65535, "forceUpdate": false, "lockVersion": false, "status": "Installed", "pcbMajor": 2, "pcbMinor": 0, "pcbPatch": 2, "pumpRating": 0, "machine": {"id": 133, "name": "Swig HQ Right Machine", "serialNumber": "00102", "active": true, "inDowntime": false, "menuId": 270, "usageType": 1, "lastUpdateDT": null, "addBy": 2, "addDT": "2025-04-16 19:52:33", "modBy": 1, "modDT": "2025-05-10 00:19:47", "location": {"id": 74, "name": "<PERSON><PERSON>", "address": "1351 E State St.", "city": "Le<PERSON>", "state": "Utah", "zip": "84043", "country": "USA", "phone": "", "active": true, "enableIQ": true, "timezone": "US/Pacific", "addBy": 1, "addDT": null, "modBy": 1, "modDT": null, "organization": {"id": 72, "name": "<PERSON><PERSON>", "address": "1351 E State St.", "city": "Le<PERSON>", "state": "Utah", "zip": "84043", "country": "USA", "phone": "", "active": true, "addBy": 1, "addDT": null, "modBy": 1, "modDT": null}}, "system": {"id": 3, "name": "Toast", "external": true}, "upperModel": {"id": 10, "name": "<PERSON><PERSON> w/ <PERSON><PERSON>", "type": 1, "rev": "A"}, "lowerModel": {"id": 8, "name": "Large", "type": 2, "rev": "B"}}, "application": {"id": 1927, "filePath": "https://s3.us-east-2.amazonaws.com/backbar.applications.stage/main/main-2_3_16-test-swig-accurate_timer.bin", "fwMajor": 2, "fwMinor": 3, "fwPatch": 16, "canMajor": 3, "canMinor": 9, "serialMajor": 9, "serialMinor": 10, "notes": "test-swig-accurate_timer", "type": {"id": 1, "name": "Main"}, "organization": {"id": 1, "name": "BackBar", "address": "1101 Renaissance Way NE", "city": "Atlanta", "state": "GA", "zip": "30308", "country": "USA", "phone": "(*************", "active": true, "addBy": 1, "addDT": null, "modBy": 1, "modDT": null}, "version": ""}, "previous": {"id": 1880, "filePath": "https://s3.us-east-2.amazonaws.com/backbar.applications.stage/main/main-2_3_15-swig_beta_1.bin", "fwMajor": 2, "fwMinor": 3, "fwPatch": 15, "canMajor": 3, "canMinor": 8, "serialMajor": 9, "serialMinor": 9, "notes": "swig_beta_1", "type": {"id": 1, "name": "Main"}, "organization": {"id": 1, "name": "BackBar", "address": "1101 Renaissance Way NE", "city": "Atlanta", "state": "GA", "zip": "30308", "country": "USA", "phone": "(*************", "active": true, "addBy": 1, "addDT": null, "modBy": 1, "modDT": null}, "version": ""}, "scheduled": null}, {"id": 9462, "type": {"id": 6, "name": "Nozzle"}, "protocolId": 560, "forceUpdate": false, "lockVersion": false, "status": "Installed", "pcbMajor": 2, "pcbMinor": 1, "pcbPatch": 0, "pumpRating": 0, "machine": {"id": 133, "name": "Swig HQ Right Machine", "serialNumber": "00102", "active": true, "inDowntime": false, "menuId": 270, "usageType": 1, "lastUpdateDT": null, "addBy": 2, "addDT": "2025-04-16 19:52:33", "modBy": 1, "modDT": "2025-05-10 00:19:47", "location": {"id": 74, "name": "<PERSON><PERSON>", "address": "1351 E State St.", "city": "Le<PERSON>", "state": "Utah", "zip": "84043", "country": "USA", "phone": "", "active": true, "enableIQ": true, "timezone": "US/Pacific", "addBy": 1, "addDT": null, "modBy": 1, "modDT": null, "organization": {"id": 72, "name": "<PERSON><PERSON>", "address": "1351 E State St.", "city": "Le<PERSON>", "state": "Utah", "zip": "84043", "country": "USA", "phone": "", "active": true, "addBy": 1, "addDT": null, "modBy": 1, "modDT": null}}, "system": {"id": 3, "name": "Toast", "external": true}, "upperModel": {"id": 10, "name": "<PERSON><PERSON> w/ <PERSON><PERSON>", "type": 1, "rev": "A"}, "lowerModel": {"id": 8, "name": "Large", "type": 2, "rev": "B"}}, "application": {"id": 1881, "filePath": "https://s3.us-east-2.amazonaws.com/backbar.applications.stage/nozzle/nozzle-2_0_4-swig_beta_1.bin", "fwMajor": 2, "fwMinor": 0, "fwPatch": 4, "canMajor": 3, "canMinor": 8, "serialMajor": 9, "serialMinor": 9, "notes": "swig_beta_1", "type": {"id": 6, "name": "Nozzle"}, "organization": {"id": 1, "name": "BackBar", "address": "1101 Renaissance Way NE", "city": "Atlanta", "state": "GA", "zip": "30308", "country": "USA", "phone": "(*************", "active": true, "addBy": 1, "addDT": null, "modBy": 1, "modDT": null}, "version": ""}, "previous": {"id": 1854, "filePath": "https://s3.us-east-2.amazonaws.com/backbar.applications.stage/nozzle/nozzle-2_0_4-swig-longer-cal.bin", "fwMajor": 2, "fwMinor": 0, "fwPatch": 4, "canMajor": 3, "canMinor": 8, "serialMajor": 9, "serialMinor": 9, "notes": "swig-longer-cal", "type": {"id": 6, "name": "Nozzle"}, "organization": {"id": 1, "name": "BackBar", "address": "1101 Renaissance Way NE", "city": "Atlanta", "state": "GA", "zip": "30308", "country": "USA", "phone": "(*************", "active": true, "addBy": 1, "addDT": null, "modBy": 1, "modDT": null}, "version": ""}, "scheduled": null}, {"id": 9463, "type": {"id": 9, "name": "Cooling"}, "protocolId": 576, "forceUpdate": false, "lockVersion": false, "status": "Installed", "pcbMajor": 2, "pcbMinor": 1, "pcbPatch": 0, "pumpRating": 0, "machine": {"id": 133, "name": "Swig HQ Right Machine", "serialNumber": "00102", "active": true, "inDowntime": false, "menuId": 270, "usageType": 1, "lastUpdateDT": null, "addBy": 2, "addDT": "2025-04-16 19:52:33", "modBy": 1, "modDT": "2025-05-10 00:19:47", "location": {"id": 74, "name": "<PERSON><PERSON>", "address": "1351 E State St.", "city": "Le<PERSON>", "state": "Utah", "zip": "84043", "country": "USA", "phone": "", "active": true, "enableIQ": true, "timezone": "US/Pacific", "addBy": 1, "addDT": null, "modBy": 1, "modDT": null, "organization": {"id": 72, "name": "<PERSON><PERSON>", "address": "1351 E State St.", "city": "Le<PERSON>", "state": "Utah", "zip": "84043", "country": "USA", "phone": "", "active": true, "addBy": 1, "addDT": null, "modBy": 1, "modDT": null}}, "system": {"id": 3, "name": "Toast", "external": true}, "upperModel": {"id": 10, "name": "<PERSON><PERSON> w/ <PERSON><PERSON>", "type": 1, "rev": "A"}, "lowerModel": {"id": 8, "name": "Large", "type": 2, "rev": "B"}}, "application": {"id": 1878, "filePath": "https://s3.us-east-2.amazonaws.com/backbar.applications.stage/cooling/cooling-2_1_3-swig_beta_1.bin", "fwMajor": 2, "fwMinor": 1, "fwPatch": 3, "canMajor": 3, "canMinor": 8, "serialMajor": 9, "serialMinor": 9, "notes": "swig_beta_1", "type": {"id": 9, "name": "Cooling"}, "organization": {"id": 1, "name": "BackBar", "address": "1101 Renaissance Way NE", "city": "Atlanta", "state": "GA", "zip": "30308", "country": "USA", "phone": "(*************", "active": true, "addBy": 1, "addDT": null, "modBy": 1, "modDT": null}, "version": ""}, "previous": {"id": 1838, "filePath": "https://s3.us-east-2.amazonaws.com/backbar.applications.stage/cooling/cooling-2_1_3-beta-test_r38.bin", "fwMajor": 2, "fwMinor": 1, "fwPatch": 3, "canMajor": 3, "canMinor": 8, "serialMajor": 9, "serialMinor": 9, "notes": "beta-test_r38", "type": {"id": 9, "name": "Cooling"}, "organization": {"id": 1, "name": "BackBar", "address": "1101 Renaissance Way NE", "city": "Atlanta", "state": "GA", "zip": "30308", "country": "USA", "phone": "(*************", "active": true, "addBy": 1, "addDT": null, "modBy": 1, "modDT": null}, "version": ""}, "scheduled": null}, {"id": 9464, "type": {"id": 5, "name": "Pump"}, "protocolId": 9, "forceUpdate": false, "lockVersion": false, "status": "Installed", "pcbMajor": 3, "pcbMinor": 0, "pcbPatch": 0, "pumpRating": 0, "machine": {"id": 133, "name": "Swig HQ Right Machine", "serialNumber": "00102", "active": true, "inDowntime": false, "menuId": 270, "usageType": 1, "lastUpdateDT": null, "addBy": 2, "addDT": "2025-04-16 19:52:33", "modBy": 1, "modDT": "2025-05-10 00:19:47", "location": {"id": 74, "name": "<PERSON><PERSON>", "address": "1351 E State St.", "city": "Le<PERSON>", "state": "Utah", "zip": "84043", "country": "USA", "phone": "", "active": true, "enableIQ": true, "timezone": "US/Pacific", "addBy": 1, "addDT": null, "modBy": 1, "modDT": null, "organization": {"id": 72, "name": "<PERSON><PERSON>", "address": "1351 E State St.", "city": "Le<PERSON>", "state": "Utah", "zip": "84043", "country": "USA", "phone": "", "active": true, "addBy": 1, "addDT": null, "modBy": 1, "modDT": null}}, "system": {"id": 3, "name": "Toast", "external": true}, "upperModel": {"id": 10, "name": "<PERSON><PERSON> w/ <PERSON><PERSON>", "type": 1, "rev": "A"}, "lowerModel": {"id": 8, "name": "Large", "type": 2, "rev": "B"}}, "application": {"id": 1928, "filePath": "https://s3.us-east-2.amazonaws.com/backbar.applications.stage/pump/pump-3_1_7-test-swig-accurate_timer.bin", "fwMajor": 3, "fwMinor": 1, "fwPatch": 7, "canMajor": 3, "canMinor": 9, "serialMajor": 9, "serialMinor": 10, "notes": "test-swig-accurate_timer", "type": {"id": 5, "name": "Pump"}, "organization": {"id": 1, "name": "BackBar", "address": "1101 Renaissance Way NE", "city": "Atlanta", "state": "GA", "zip": "30308", "country": "USA", "phone": "(*************", "active": true, "addBy": 1, "addDT": null, "modBy": 1, "modDT": null}, "version": ""}, "previous": {"id": 1882, "filePath": "https://s3.us-east-2.amazonaws.com/backbar.applications.stage/pump/pump-3_1_6-swig_beta_1.bin", "fwMajor": 3, "fwMinor": 1, "fwPatch": 6, "canMajor": 3, "canMinor": 8, "serialMajor": 9, "serialMinor": 9, "notes": "swig_beta_1", "type": {"id": 5, "name": "Pump"}, "organization": {"id": 1, "name": "BackBar", "address": "1101 Renaissance Way NE", "city": "Atlanta", "state": "GA", "zip": "30308", "country": "USA", "phone": "(*************", "active": true, "addBy": 1, "addDT": null, "modBy": 1, "modDT": null}, "version": ""}, "scheduled": null}, {"id": 9465, "type": {"id": 5, "name": "Pump"}, "protocolId": 8, "forceUpdate": false, "lockVersion": false, "status": "Installed", "pcbMajor": 3, "pcbMinor": 0, "pcbPatch": 0, "pumpRating": 0, "machine": {"id": 133, "name": "Swig HQ Right Machine", "serialNumber": "00102", "active": true, "inDowntime": false, "menuId": 270, "usageType": 1, "lastUpdateDT": null, "addBy": 2, "addDT": "2025-04-16 19:52:33", "modBy": 1, "modDT": "2025-05-10 00:19:47", "location": {"id": 74, "name": "<PERSON><PERSON>", "address": "1351 E State St.", "city": "Le<PERSON>", "state": "Utah", "zip": "84043", "country": "USA", "phone": "", "active": true, "enableIQ": true, "timezone": "US/Pacific", "addBy": 1, "addDT": null, "modBy": 1, "modDT": null, "organization": {"id": 72, "name": "<PERSON><PERSON>", "address": "1351 E State St.", "city": "Le<PERSON>", "state": "Utah", "zip": "84043", "country": "USA", "phone": "", "active": true, "addBy": 1, "addDT": null, "modBy": 1, "modDT": null}}, "system": {"id": 3, "name": "Toast", "external": true}, "upperModel": {"id": 10, "name": "<PERSON><PERSON> w/ <PERSON><PERSON>", "type": 1, "rev": "A"}, "lowerModel": {"id": 8, "name": "Large", "type": 2, "rev": "B"}}, "application": {"id": 1928, "filePath": "https://s3.us-east-2.amazonaws.com/backbar.applications.stage/pump/pump-3_1_7-test-swig-accurate_timer.bin", "fwMajor": 3, "fwMinor": 1, "fwPatch": 7, "canMajor": 3, "canMinor": 9, "serialMajor": 9, "serialMinor": 10, "notes": "test-swig-accurate_timer", "type": {"id": 5, "name": "Pump"}, "organization": {"id": 1, "name": "BackBar", "address": "1101 Renaissance Way NE", "city": "Atlanta", "state": "GA", "zip": "30308", "country": "USA", "phone": "(*************", "active": true, "addBy": 1, "addDT": null, "modBy": 1, "modDT": null}, "version": ""}, "previous": {"id": 1882, "filePath": "https://s3.us-east-2.amazonaws.com/backbar.applications.stage/pump/pump-3_1_6-swig_beta_1.bin", "fwMajor": 3, "fwMinor": 1, "fwPatch": 6, "canMajor": 3, "canMinor": 8, "serialMajor": 9, "serialMinor": 9, "notes": "swig_beta_1", "type": {"id": 5, "name": "Pump"}, "organization": {"id": 1, "name": "BackBar", "address": "1101 Renaissance Way NE", "city": "Atlanta", "state": "GA", "zip": "30308", "country": "USA", "phone": "(*************", "active": true, "addBy": 1, "addDT": null, "modBy": 1, "modDT": null}, "version": ""}, "scheduled": null}, {"id": 9466, "type": {"id": 5, "name": "Pump"}, "protocolId": 7, "forceUpdate": false, "lockVersion": false, "status": "Installed", "pcbMajor": 3, "pcbMinor": 0, "pcbPatch": 0, "pumpRating": 0, "machine": {"id": 133, "name": "Swig HQ Right Machine", "serialNumber": "00102", "active": true, "inDowntime": false, "menuId": 270, "usageType": 1, "lastUpdateDT": null, "addBy": 2, "addDT": "2025-04-16 19:52:33", "modBy": 1, "modDT": "2025-05-10 00:19:47", "location": {"id": 74, "name": "<PERSON><PERSON>", "address": "1351 E State St.", "city": "Le<PERSON>", "state": "Utah", "zip": "84043", "country": "USA", "phone": "", "active": true, "enableIQ": true, "timezone": "US/Pacific", "addBy": 1, "addDT": null, "modBy": 1, "modDT": null, "organization": {"id": 72, "name": "<PERSON><PERSON>", "address": "1351 E State St.", "city": "Le<PERSON>", "state": "Utah", "zip": "84043", "country": "USA", "phone": "", "active": true, "addBy": 1, "addDT": null, "modBy": 1, "modDT": null}}, "system": {"id": 3, "name": "Toast", "external": true}, "upperModel": {"id": 10, "name": "<PERSON><PERSON> w/ <PERSON><PERSON>", "type": 1, "rev": "A"}, "lowerModel": {"id": 8, "name": "Large", "type": 2, "rev": "B"}}, "application": {"id": 1928, "filePath": "https://s3.us-east-2.amazonaws.com/backbar.applications.stage/pump/pump-3_1_7-test-swig-accurate_timer.bin", "fwMajor": 3, "fwMinor": 1, "fwPatch": 7, "canMajor": 3, "canMinor": 9, "serialMajor": 9, "serialMinor": 10, "notes": "test-swig-accurate_timer", "type": {"id": 5, "name": "Pump"}, "organization": {"id": 1, "name": "BackBar", "address": "1101 Renaissance Way NE", "city": "Atlanta", "state": "GA", "zip": "30308", "country": "USA", "phone": "(*************", "active": true, "addBy": 1, "addDT": null, "modBy": 1, "modDT": null}, "version": ""}, "previous": {"id": 1882, "filePath": "https://s3.us-east-2.amazonaws.com/backbar.applications.stage/pump/pump-3_1_6-swig_beta_1.bin", "fwMajor": 3, "fwMinor": 1, "fwPatch": 6, "canMajor": 3, "canMinor": 8, "serialMajor": 9, "serialMinor": 9, "notes": "swig_beta_1", "type": {"id": 5, "name": "Pump"}, "organization": {"id": 1, "name": "BackBar", "address": "1101 Renaissance Way NE", "city": "Atlanta", "state": "GA", "zip": "30308", "country": "USA", "phone": "(*************", "active": true, "addBy": 1, "addDT": null, "modBy": 1, "modDT": null}, "version": ""}, "scheduled": null}, {"id": 9467, "type": {"id": 5, "name": "Pump"}, "protocolId": 5, "forceUpdate": false, "lockVersion": false, "status": "Installed", "pcbMajor": 3, "pcbMinor": 0, "pcbPatch": 0, "pumpRating": 0, "machine": {"id": 133, "name": "Swig HQ Right Machine", "serialNumber": "00102", "active": true, "inDowntime": false, "menuId": 270, "usageType": 1, "lastUpdateDT": null, "addBy": 2, "addDT": "2025-04-16 19:52:33", "modBy": 1, "modDT": "2025-05-10 00:19:47", "location": {"id": 74, "name": "<PERSON><PERSON>", "address": "1351 E State St.", "city": "Le<PERSON>", "state": "Utah", "zip": "84043", "country": "USA", "phone": "", "active": true, "enableIQ": true, "timezone": "US/Pacific", "addBy": 1, "addDT": null, "modBy": 1, "modDT": null, "organization": {"id": 72, "name": "<PERSON><PERSON>", "address": "1351 E State St.", "city": "Le<PERSON>", "state": "Utah", "zip": "84043", "country": "USA", "phone": "", "active": true, "addBy": 1, "addDT": null, "modBy": 1, "modDT": null}}, "system": {"id": 3, "name": "Toast", "external": true}, "upperModel": {"id": 10, "name": "<PERSON><PERSON> w/ <PERSON><PERSON>", "type": 1, "rev": "A"}, "lowerModel": {"id": 8, "name": "Large", "type": 2, "rev": "B"}}, "application": {"id": 1928, "filePath": "https://s3.us-east-2.amazonaws.com/backbar.applications.stage/pump/pump-3_1_7-test-swig-accurate_timer.bin", "fwMajor": 3, "fwMinor": 1, "fwPatch": 7, "canMajor": 3, "canMinor": 9, "serialMajor": 9, "serialMinor": 10, "notes": "test-swig-accurate_timer", "type": {"id": 5, "name": "Pump"}, "organization": {"id": 1, "name": "BackBar", "address": "1101 Renaissance Way NE", "city": "Atlanta", "state": "GA", "zip": "30308", "country": "USA", "phone": "(*************", "active": true, "addBy": 1, "addDT": null, "modBy": 1, "modDT": null}, "version": ""}, "previous": {"id": 1882, "filePath": "https://s3.us-east-2.amazonaws.com/backbar.applications.stage/pump/pump-3_1_6-swig_beta_1.bin", "fwMajor": 3, "fwMinor": 1, "fwPatch": 6, "canMajor": 3, "canMinor": 8, "serialMajor": 9, "serialMinor": 9, "notes": "swig_beta_1", "type": {"id": 5, "name": "Pump"}, "organization": {"id": 1, "name": "BackBar", "address": "1101 Renaissance Way NE", "city": "Atlanta", "state": "GA", "zip": "30308", "country": "USA", "phone": "(*************", "active": true, "addBy": 1, "addDT": null, "modBy": 1, "modDT": null}, "version": ""}, "scheduled": null}, {"id": 9468, "type": {"id": 5, "name": "Pump"}, "protocolId": 4, "forceUpdate": false, "lockVersion": false, "status": "Installed", "pcbMajor": 3, "pcbMinor": 0, "pcbPatch": 0, "pumpRating": 0, "machine": {"id": 133, "name": "Swig HQ Right Machine", "serialNumber": "00102", "active": true, "inDowntime": false, "menuId": 270, "usageType": 1, "lastUpdateDT": null, "addBy": 2, "addDT": "2025-04-16 19:52:33", "modBy": 1, "modDT": "2025-05-10 00:19:47", "location": {"id": 74, "name": "<PERSON><PERSON>", "address": "1351 E State St.", "city": "Le<PERSON>", "state": "Utah", "zip": "84043", "country": "USA", "phone": "", "active": true, "enableIQ": true, "timezone": "US/Pacific", "addBy": 1, "addDT": null, "modBy": 1, "modDT": null, "organization": {"id": 72, "name": "<PERSON><PERSON>", "address": "1351 E State St.", "city": "Le<PERSON>", "state": "Utah", "zip": "84043", "country": "USA", "phone": "", "active": true, "addBy": 1, "addDT": null, "modBy": 1, "modDT": null}}, "system": {"id": 3, "name": "Toast", "external": true}, "upperModel": {"id": 10, "name": "<PERSON><PERSON> w/ <PERSON><PERSON>", "type": 1, "rev": "A"}, "lowerModel": {"id": 8, "name": "Large", "type": 2, "rev": "B"}}, "application": {"id": 1928, "filePath": "https://s3.us-east-2.amazonaws.com/backbar.applications.stage/pump/pump-3_1_7-test-swig-accurate_timer.bin", "fwMajor": 3, "fwMinor": 1, "fwPatch": 7, "canMajor": 3, "canMinor": 9, "serialMajor": 9, "serialMinor": 10, "notes": "test-swig-accurate_timer", "type": {"id": 5, "name": "Pump"}, "organization": {"id": 1, "name": "BackBar", "address": "1101 Renaissance Way NE", "city": "Atlanta", "state": "GA", "zip": "30308", "country": "USA", "phone": "(*************", "active": true, "addBy": 1, "addDT": null, "modBy": 1, "modDT": null}, "version": ""}, "previous": {"id": 1882, "filePath": "https://s3.us-east-2.amazonaws.com/backbar.applications.stage/pump/pump-3_1_6-swig_beta_1.bin", "fwMajor": 3, "fwMinor": 1, "fwPatch": 6, "canMajor": 3, "canMinor": 8, "serialMajor": 9, "serialMinor": 9, "notes": "swig_beta_1", "type": {"id": 5, "name": "Pump"}, "organization": {"id": 1, "name": "BackBar", "address": "1101 Renaissance Way NE", "city": "Atlanta", "state": "GA", "zip": "30308", "country": "USA", "phone": "(*************", "active": true, "addBy": 1, "addDT": null, "modBy": 1, "modDT": null}, "version": ""}, "scheduled": null}, {"id": 9469, "type": {"id": 5, "name": "Pump"}, "protocolId": 3, "forceUpdate": false, "lockVersion": false, "status": "Installed", "pcbMajor": 3, "pcbMinor": 0, "pcbPatch": 0, "pumpRating": 0, "machine": {"id": 133, "name": "Swig HQ Right Machine", "serialNumber": "00102", "active": true, "inDowntime": false, "menuId": 270, "usageType": 1, "lastUpdateDT": null, "addBy": 2, "addDT": "2025-04-16 19:52:33", "modBy": 1, "modDT": "2025-05-10 00:19:47", "location": {"id": 74, "name": "<PERSON><PERSON>", "address": "1351 E State St.", "city": "Le<PERSON>", "state": "Utah", "zip": "84043", "country": "USA", "phone": "", "active": true, "enableIQ": true, "timezone": "US/Pacific", "addBy": 1, "addDT": null, "modBy": 1, "modDT": null, "organization": {"id": 72, "name": "<PERSON><PERSON>", "address": "1351 E State St.", "city": "Le<PERSON>", "state": "Utah", "zip": "84043", "country": "USA", "phone": "", "active": true, "addBy": 1, "addDT": null, "modBy": 1, "modDT": null}}, "system": {"id": 3, "name": "Toast", "external": true}, "upperModel": {"id": 10, "name": "<PERSON><PERSON> w/ <PERSON><PERSON>", "type": 1, "rev": "A"}, "lowerModel": {"id": 8, "name": "Large", "type": 2, "rev": "B"}}, "application": {"id": 1928, "filePath": "https://s3.us-east-2.amazonaws.com/backbar.applications.stage/pump/pump-3_1_7-test-swig-accurate_timer.bin", "fwMajor": 3, "fwMinor": 1, "fwPatch": 7, "canMajor": 3, "canMinor": 9, "serialMajor": 9, "serialMinor": 10, "notes": "test-swig-accurate_timer", "type": {"id": 5, "name": "Pump"}, "organization": {"id": 1, "name": "BackBar", "address": "1101 Renaissance Way NE", "city": "Atlanta", "state": "GA", "zip": "30308", "country": "USA", "phone": "(*************", "active": true, "addBy": 1, "addDT": null, "modBy": 1, "modDT": null}, "version": ""}, "previous": {"id": 1882, "filePath": "https://s3.us-east-2.amazonaws.com/backbar.applications.stage/pump/pump-3_1_6-swig_beta_1.bin", "fwMajor": 3, "fwMinor": 1, "fwPatch": 6, "canMajor": 3, "canMinor": 8, "serialMajor": 9, "serialMinor": 9, "notes": "swig_beta_1", "type": {"id": 5, "name": "Pump"}, "organization": {"id": 1, "name": "BackBar", "address": "1101 Renaissance Way NE", "city": "Atlanta", "state": "GA", "zip": "30308", "country": "USA", "phone": "(*************", "active": true, "addBy": 1, "addDT": null, "modBy": 1, "modDT": null}, "version": ""}, "scheduled": null}, {"id": 9470, "type": {"id": 5, "name": "Pump"}, "protocolId": 2, "forceUpdate": false, "lockVersion": false, "status": "Installed", "pcbMajor": 3, "pcbMinor": 0, "pcbPatch": 0, "pumpRating": 0, "machine": {"id": 133, "name": "Swig HQ Right Machine", "serialNumber": "00102", "active": true, "inDowntime": false, "menuId": 270, "usageType": 1, "lastUpdateDT": null, "addBy": 2, "addDT": "2025-04-16 19:52:33", "modBy": 1, "modDT": "2025-05-10 00:19:47", "location": {"id": 74, "name": "<PERSON><PERSON>", "address": "1351 E State St.", "city": "Le<PERSON>", "state": "Utah", "zip": "84043", "country": "USA", "phone": "", "active": true, "enableIQ": true, "timezone": "US/Pacific", "addBy": 1, "addDT": null, "modBy": 1, "modDT": null, "organization": {"id": 72, "name": "<PERSON><PERSON>", "address": "1351 E State St.", "city": "Le<PERSON>", "state": "Utah", "zip": "84043", "country": "USA", "phone": "", "active": true, "addBy": 1, "addDT": null, "modBy": 1, "modDT": null}}, "system": {"id": 3, "name": "Toast", "external": true}, "upperModel": {"id": 10, "name": "<PERSON><PERSON> w/ <PERSON><PERSON>", "type": 1, "rev": "A"}, "lowerModel": {"id": 8, "name": "Large", "type": 2, "rev": "B"}}, "application": {"id": 1928, "filePath": "https://s3.us-east-2.amazonaws.com/backbar.applications.stage/pump/pump-3_1_7-test-swig-accurate_timer.bin", "fwMajor": 3, "fwMinor": 1, "fwPatch": 7, "canMajor": 3, "canMinor": 9, "serialMajor": 9, "serialMinor": 10, "notes": "test-swig-accurate_timer", "type": {"id": 5, "name": "Pump"}, "organization": {"id": 1, "name": "BackBar", "address": "1101 Renaissance Way NE", "city": "Atlanta", "state": "GA", "zip": "30308", "country": "USA", "phone": "(*************", "active": true, "addBy": 1, "addDT": null, "modBy": 1, "modDT": null}, "version": ""}, "previous": {"id": 1882, "filePath": "https://s3.us-east-2.amazonaws.com/backbar.applications.stage/pump/pump-3_1_6-swig_beta_1.bin", "fwMajor": 3, "fwMinor": 1, "fwPatch": 6, "canMajor": 3, "canMinor": 8, "serialMajor": 9, "serialMinor": 9, "notes": "swig_beta_1", "type": {"id": 5, "name": "Pump"}, "organization": {"id": 1, "name": "BackBar", "address": "1101 Renaissance Way NE", "city": "Atlanta", "state": "GA", "zip": "30308", "country": "USA", "phone": "(*************", "active": true, "addBy": 1, "addDT": null, "modBy": 1, "modDT": null}, "version": ""}, "scheduled": null}, {"id": 9471, "type": {"id": 5, "name": "Pump"}, "protocolId": 1, "forceUpdate": false, "lockVersion": false, "status": "Installed", "pcbMajor": 3, "pcbMinor": 0, "pcbPatch": 0, "pumpRating": 1, "machine": {"id": 133, "name": "Swig HQ Right Machine", "serialNumber": "00102", "active": true, "inDowntime": false, "menuId": 270, "usageType": 1, "lastUpdateDT": null, "addBy": 2, "addDT": "2025-04-16 19:52:33", "modBy": 1, "modDT": "2025-05-10 00:19:47", "location": {"id": 74, "name": "<PERSON><PERSON>", "address": "1351 E State St.", "city": "Le<PERSON>", "state": "Utah", "zip": "84043", "country": "USA", "phone": "", "active": true, "enableIQ": true, "timezone": "US/Pacific", "addBy": 1, "addDT": null, "modBy": 1, "modDT": null, "organization": {"id": 72, "name": "<PERSON><PERSON>", "address": "1351 E State St.", "city": "Le<PERSON>", "state": "Utah", "zip": "84043", "country": "USA", "phone": "", "active": true, "addBy": 1, "addDT": null, "modBy": 1, "modDT": null}}, "system": {"id": 3, "name": "Toast", "external": true}, "upperModel": {"id": 10, "name": "<PERSON><PERSON> w/ <PERSON><PERSON>", "type": 1, "rev": "A"}, "lowerModel": {"id": 8, "name": "Large", "type": 2, "rev": "B"}}, "application": {"id": 1928, "filePath": "https://s3.us-east-2.amazonaws.com/backbar.applications.stage/pump/pump-3_1_7-test-swig-accurate_timer.bin", "fwMajor": 3, "fwMinor": 1, "fwPatch": 7, "canMajor": 3, "canMinor": 9, "serialMajor": 9, "serialMinor": 10, "notes": "test-swig-accurate_timer", "type": {"id": 5, "name": "Pump"}, "organization": {"id": 1, "name": "BackBar", "address": "1101 Renaissance Way NE", "city": "Atlanta", "state": "GA", "zip": "30308", "country": "USA", "phone": "(*************", "active": true, "addBy": 1, "addDT": null, "modBy": 1, "modDT": null}, "version": ""}, "previous": {"id": 1882, "filePath": "https://s3.us-east-2.amazonaws.com/backbar.applications.stage/pump/pump-3_1_6-swig_beta_1.bin", "fwMajor": 3, "fwMinor": 1, "fwPatch": 6, "canMajor": 3, "canMinor": 8, "serialMajor": 9, "serialMinor": 9, "notes": "swig_beta_1", "type": {"id": 5, "name": "Pump"}, "organization": {"id": 1, "name": "BackBar", "address": "1101 Renaissance Way NE", "city": "Atlanta", "state": "GA", "zip": "30308", "country": "USA", "phone": "(*************", "active": true, "addBy": 1, "addDT": null, "modBy": 1, "modDT": null}, "version": ""}, "scheduled": null}, {"id": 9472, "type": {"id": 5, "name": "Pump"}, "protocolId": 0, "forceUpdate": false, "lockVersion": false, "status": "Installed", "pcbMajor": 3, "pcbMinor": 1, "pcbPatch": 0, "pumpRating": 3, "machine": {"id": 133, "name": "Swig HQ Right Machine", "serialNumber": "00102", "active": true, "inDowntime": false, "menuId": 270, "usageType": 1, "lastUpdateDT": null, "addBy": 2, "addDT": "2025-04-16 19:52:33", "modBy": 1, "modDT": "2025-05-10 00:19:47", "location": {"id": 74, "name": "<PERSON><PERSON>", "address": "1351 E State St.", "city": "Le<PERSON>", "state": "Utah", "zip": "84043", "country": "USA", "phone": "", "active": true, "enableIQ": true, "timezone": "US/Pacific", "addBy": 1, "addDT": null, "modBy": 1, "modDT": null, "organization": {"id": 72, "name": "<PERSON><PERSON>", "address": "1351 E State St.", "city": "Le<PERSON>", "state": "Utah", "zip": "84043", "country": "USA", "phone": "", "active": true, "addBy": 1, "addDT": null, "modBy": 1, "modDT": null}}, "system": {"id": 3, "name": "Toast", "external": true}, "upperModel": {"id": 10, "name": "<PERSON><PERSON> w/ <PERSON><PERSON>", "type": 1, "rev": "A"}, "lowerModel": {"id": 8, "name": "Large", "type": 2, "rev": "B"}}, "application": {"id": 1928, "filePath": "https://s3.us-east-2.amazonaws.com/backbar.applications.stage/pump/pump-3_1_7-test-swig-accurate_timer.bin", "fwMajor": 3, "fwMinor": 1, "fwPatch": 7, "canMajor": 3, "canMinor": 9, "serialMajor": 9, "serialMinor": 10, "notes": "test-swig-accurate_timer", "type": {"id": 5, "name": "Pump"}, "organization": {"id": 1, "name": "BackBar", "address": "1101 Renaissance Way NE", "city": "Atlanta", "state": "GA", "zip": "30308", "country": "USA", "phone": "(*************", "active": true, "addBy": 1, "addDT": null, "modBy": 1, "modDT": null}, "version": ""}, "previous": {"id": 1882, "filePath": "https://s3.us-east-2.amazonaws.com/backbar.applications.stage/pump/pump-3_1_6-swig_beta_1.bin", "fwMajor": 3, "fwMinor": 1, "fwPatch": 6, "canMajor": 3, "canMinor": 8, "serialMajor": 9, "serialMinor": 9, "notes": "swig_beta_1", "type": {"id": 5, "name": "Pump"}, "organization": {"id": 1, "name": "BackBar", "address": "1101 Renaissance Way NE", "city": "Atlanta", "state": "GA", "zip": "30308", "country": "USA", "phone": "(*************", "active": true, "addBy": 1, "addDT": null, "modBy": 1, "modDT": null}, "version": ""}, "scheduled": null}, {"id": 9514, "type": {"id": 2, "name": "Solenoid"}, "protocolId": 12, "forceUpdate": false, "lockVersion": false, "status": "Installed", "pcbMajor": 1, "pcbMinor": 0, "pcbPatch": 1, "pumpRating": 0, "machine": {"id": 133, "name": "Swig HQ Right Machine", "serialNumber": "00102", "active": true, "inDowntime": false, "menuId": 270, "usageType": 1, "lastUpdateDT": null, "addBy": 2, "addDT": "2025-04-16 19:52:33", "modBy": 1, "modDT": "2025-05-10 00:19:47", "location": {"id": 74, "name": "<PERSON><PERSON>", "address": "1351 E State St.", "city": "Le<PERSON>", "state": "Utah", "zip": "84043", "country": "USA", "phone": "", "active": true, "enableIQ": true, "timezone": "US/Pacific", "addBy": 1, "addDT": null, "modBy": 1, "modDT": null, "organization": {"id": 72, "name": "<PERSON><PERSON>", "address": "1351 E State St.", "city": "Le<PERSON>", "state": "Utah", "zip": "84043", "country": "USA", "phone": "", "active": true, "addBy": 1, "addDT": null, "modBy": 1, "modDT": null}}, "system": {"id": 3, "name": "Toast", "external": true}, "upperModel": {"id": 10, "name": "<PERSON><PERSON> w/ <PERSON><PERSON>", "type": 1, "rev": "A"}, "lowerModel": {"id": 8, "name": "Large", "type": 2, "rev": "B"}}, "application": {"id": 1883, "filePath": "https://s3.us-east-2.amazonaws.com/backbar.applications.stage/solenoid/solenoid-1_0_3-swig_beta_1.bin", "fwMajor": 1, "fwMinor": 0, "fwPatch": 3, "canMajor": 3, "canMinor": 8, "serialMajor": 9, "serialMinor": 9, "notes": "swig_beta_1", "type": {"id": 2, "name": "Solenoid"}, "organization": {"id": 1, "name": "BackBar", "address": "1101 Renaissance Way NE", "city": "Atlanta", "state": "GA", "zip": "30308", "country": "USA", "phone": "(*************", "active": true, "addBy": 1, "addDT": null, "modBy": 1, "modDT": null}, "version": ""}, "previous": {"id": 1855, "filePath": "https://s3.us-east-2.amazonaws.com/backbar.applications.stage/solenoid/solenoid-1_0_3-swig-longer-cal.bin", "fwMajor": 1, "fwMinor": 0, "fwPatch": 3, "canMajor": 3, "canMinor": 8, "serialMajor": 9, "serialMinor": 9, "notes": "swig-longer-cal", "type": {"id": 2, "name": "Solenoid"}, "organization": {"id": 1, "name": "BackBar", "address": "1101 Renaissance Way NE", "city": "Atlanta", "state": "GA", "zip": "30308", "country": "USA", "phone": "(*************", "active": true, "addBy": 1, "addDT": null, "modBy": 1, "modDT": null}, "version": ""}, "scheduled": null}]