[{"id": 56138, "name": "Drain Auto Mode Enable", "macro": "EEP_DRAIN_AUTO_ENABLE", "description": "Enable automatic drain pump cycling based on preset ON time and OFF time periods", "address": 112, "boardName": "Cooling", "boardId": 576, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 103, "isPrivate": false}, {"id": 56139, "name": "<PERSON><PERSON>ump OFF Time", "macro": "EEP_DRAIN_OFF_TIME", "description": "Time the drain pump is turned OFF (in seconds)", "address": 96, "boardName": "Cooling", "boardId": 576, "paramType": "WORD", "defaultParam": "540", "currentValue": "540", "newValue": null, "minParam": "10", "maxParam": "10800", "machineId": 103, "isPrivate": false}, {"id": 56140, "name": "<PERSON><PERSON>ump ON Time", "macro": "EEP_DRAIN_ON_TIME", "description": "Time the drain pump is turned ON (in seconds)", "address": 80, "boardName": "Cooling", "boardId": 576, "paramType": "WORD", "defaultParam": "60", "currentValue": "60", "newValue": null, "minParam": "10", "maxParam": "1800", "machineId": 103, "isPrivate": false}, {"id": 56141, "name": "Refrigeration Enable", "macro": "EEP_ENABLE_REFRIGERATION", "description": "Enable the compressor for cooling the fridge. See ", "address": 50, "boardName": "Cooling", "boardId": 576, "paramType": "BOOLEAN", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 103, "isPrivate": false}, {"id": 56142, "name": "Lower Temperature Threshold (Top Temp Sensor)", "macro": "EEP_LOWER_TEMP_THRESHOLD", "description": "Temperature of the top fridge sensor (in degrees Fahrenheit) below which the compressor is turned OFF", "address": 32, "boardName": "Cooling", "boardId": 576, "paramType": "SINGLE", "defaultParam": "36", "currentValue": "36", "newValue": null, "minParam": "32", "maxParam": "46", "machineId": 103, "isPrivate": false}, {"id": 56143, "name": "Upper Temperature Threshold (Top Temp Sensor)", "macro": "EEP_UPPER_TEMP_THRESHOLD", "description": "Temperature of the top fridge sensor (in degrees Fahrenheit) above which the compressor is turned ON", "address": 16, "boardName": "Cooling", "boardId": 576, "paramType": "SINGLE", "defaultParam": "44", "currentValue": "44", "newValue": null, "minParam": "32", "maxParam": "46", "machineId": 103, "isPrivate": false}, {"id": 56144, "name": "Cooling Drawer <PERSON><PERSON> <PERSON><PERSON>", "macro": "EEP_COOLER_DRAWER_OPEN_ALERT_TIME", "description": "Time the cooling drawer switch can remain open until an alert is sent from the cooling board (in seconds)", "address": 176, "boardName": "Cooling", "boardId": 576, "paramType": "WORD", "defaultParam": "600", "currentValue": "600", "newValue": null, "minParam": "300", "maxParam": "1200", "machineId": 103, "isPrivate": false}, {"id": 56145, "name": "Cooling Drawer Switch <PERSON><PERSON>", "macro": "EEP_COOLER_DRAWER_OPEN_ALERT_ENABLE", "description": "Enable alert for cooling drawer switch open", "address": 160, "boardName": "Cooling", "boardId": 576, "paramType": "BOOLEAN", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 103, "isPrivate": false}, {"id": 56146, "name": "Upper Temperature Threshold (Bottom Temp Sensor)", "macro": "EEP_UPPER_TEMP_THRESHOLD_BOT", "description": "Temperature of the bottom fridge sensor (in degrees Fahrenheit) above which the compressor is turned ON", "address": 224, "boardName": "Cooling", "boardId": 576, "paramType": "SINGLE", "defaultParam": "44", "currentValue": "44", "newValue": null, "minParam": "32", "maxParam": "46", "machineId": 103, "isPrivate": false}, {"id": 56147, "name": "Ingredient Mixing Enable - A1", "macro": "EEP_MIXING_ENABLE_28", "description": "Enable ingredient mixing for A1", "address": 124, "boardName": "Master", "boardId": 65535, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 103, "isPrivate": false}, {"id": 56148, "name": "Ingredient Mixing Enable - B1", "macro": "EEP_MIXING_ENABLE_29", "description": "Enable ingredient mixing for B1", "address": 125, "boardName": "Master", "boardId": 65535, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 103, "isPrivate": false}, {"id": 56149, "name": "Ingredient Mixing Enable - A2", "macro": "EEP_MIXING_ENABLE_30", "description": "Enable ingredient mixing for A2", "address": 126, "boardName": "Master", "boardId": 65535, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 103, "isPrivate": false}, {"id": 56150, "name": "Ingredient Mixing Enable - B2", "macro": "EEP_MIXING_ENABLE_31", "description": "Enable ingredient mixing for B2", "address": 127, "boardName": "Master", "boardId": 65535, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 103, "isPrivate": false}, {"id": 56151, "name": "Ingredient Mixing Period", "macro": "EEP_MIXING_PERIOD", "description": "Time (in seconds) between house mix mixing/agitation operations", "address": 32, "boardName": "Master", "boardId": 65535, "paramType": "WORD", "defaultParam": "600", "currentValue": "600", "newValue": null, "minParam": "60", "maxParam": "3600", "machineId": 103, "isPrivate": false}, {"id": 56152, "name": "Testing Mode Enable", "macro": "EEP_SILENT_MODE_ENABLE", "description": "Testing and production ONLY. Stop all asynchronous machine actions, CAN frames, and serial messages for testing purposes", "address": 80, "boardName": "Master", "boardId": 65535, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 103, "isPrivate": false}, {"id": 56153, "name": "Ingredient Mixing Enable - A3", "macro": "EEP_MIXING_ENABLE_32", "description": "Enable ingredient mixing for A3", "address": 128, "boardName": "Master", "boardId": 65535, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 103, "isPrivate": false}, {"id": 56154, "name": "Ingredient Mixing Enable - B3", "macro": "EEP_MIXING_ENABLE_33", "description": "Enable ingredient mixing for B3", "address": 129, "boardName": "Master", "boardId": 65535, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 103, "isPrivate": false}, {"id": 56155, "name": "Ingredient Mixing Enable - A4", "macro": "EEP_MIXING_ENABLE_34", "description": "Enable ingredient mixing for A4", "address": 130, "boardName": "Master", "boardId": 65535, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 103, "isPrivate": false}, {"id": 56156, "name": "Ingredient Mixing Enable - B4", "macro": "EEP_MIXING_ENABLE_35", "description": "Enable ingredient mixing for B4", "address": 131, "boardName": "Master", "boardId": 65535, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 103, "isPrivate": false}, {"id": 56157, "name": "Ingredient Mixing Enable - A5", "macro": "EEP_MIXING_ENABLE_36", "description": "Enable ingredient mixing for A5", "address": 132, "boardName": "Master", "boardId": 65535, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 103, "isPrivate": false}, {"id": 56158, "name": "Ingredient Mixing Enable - B5", "macro": "EEP_MIXING_ENABLE_37", "description": "Enable ingredient mixing for B5", "address": 133, "boardName": "Master", "boardId": 65535, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 103, "isPrivate": false}, {"id": 56159, "name": "Ingredient Mixing Enable - A6", "macro": "EEP_MIXING_ENABLE_38", "description": "Enable ingredient mixing for A6", "address": 134, "boardName": "Master", "boardId": 65535, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 103, "isPrivate": false}, {"id": 56160, "name": "Ingredient Mixing Enable - B6", "macro": "EEP_MIXING_ENABLE_39", "description": "Enable ingredient mixing for B6", "address": 135, "boardName": "Master", "boardId": 65535, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 103, "isPrivate": false}, {"id": 56161, "name": "Refresh Lines Check Period", "macro": "EEP_REFRESH_CHECK_PERIOD", "description": "Time (in seconds) between refresh line operations, if there are any to perform", "address": 320, "boardName": "Master", "boardId": 65535, "paramType": "WORD", "defaultParam": "3600", "currentValue": "600", "newValue": null, "minParam": "300", "maxParam": "5400", "machineId": 103, "isPrivate": false}, {"id": 56163, "name": "Oz/Min Ingredient Speed - B6", "macro": "EEP_OZMIN_INGR_SPEED_3", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 412, "boardName": "Pump Board 9", "boardId": 9, "paramType": "SINGLE", "defaultParam": "20", "currentValue": "20", "newValue": "0", "minParam": "7.5", "maxParam": "37.5", "machineId": 103, "isPrivate": false}, {"id": 56164, "name": "Oz/Min Ingredient Speed - B4", "macro": "EEP_OZMIN_INGR_SPEED_3", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 412, "boardName": "Pump Board 8", "boardId": 8, "paramType": "SINGLE", "defaultParam": "20", "currentValue": "20", "newValue": null, "minParam": "7.5", "maxParam": "37.5", "machineId": 103, "isPrivate": false}, {"id": 56165, "name": "Oz/Min Ingredient Speed - B2", "macro": "EEP_OZMIN_INGR_SPEED_3", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 412, "boardName": "Pump Board 7", "boardId": 7, "paramType": "SINGLE", "defaultParam": "20", "currentValue": "20", "newValue": null, "minParam": "7.5", "maxParam": "37.5", "machineId": 103, "isPrivate": false}, {"id": 56166, "name": "Oz/Min Ingredient Speed - F6", "macro": "EEP_OZMIN_INGR_SPEED_3", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 412, "boardName": "Pump Board 5", "boardId": 5, "paramType": "SINGLE", "defaultParam": "20", "currentValue": "20", "newValue": null, "minParam": "7.5", "maxParam": "37.5", "machineId": 103, "isPrivate": false}, {"id": 56167, "name": "Oz/Min Ingredient Speed - F5", "macro": "EEP_OZMIN_INGR_SPEED_3", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 412, "boardName": "Pump Board 4", "boardId": 4, "paramType": "SINGLE", "defaultParam": "20", "currentValue": "20", "newValue": "0", "minParam": "7.5", "maxParam": "37.5", "machineId": 103, "isPrivate": false}, {"id": 56168, "name": "Oz/Min Ingredient Speed - F4", "macro": "EEP_OZMIN_INGR_SPEED_3", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 412, "boardName": "Pump Board 3", "boardId": 3, "paramType": "SINGLE", "defaultParam": "20", "currentValue": "20", "newValue": null, "minParam": "7.5", "maxParam": "37.5", "machineId": 103, "isPrivate": false}, {"id": 56169, "name": "Oz/Min Ingredient Speed - F3", "macro": "EEP_OZMIN_INGR_SPEED_3", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 412, "boardName": "Pump Board 2", "boardId": 2, "paramType": "SINGLE", "defaultParam": "20", "currentValue": "20", "newValue": "0", "minParam": "7.5", "maxParam": "37.5", "machineId": 103, "isPrivate": false}, {"id": 56170, "name": "Oz/Min Ingredient Speed - F2", "macro": "EEP_OZMIN_INGR_SPEED_3", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 412, "boardName": "Pump Board 1", "boardId": 1, "paramType": "SINGLE", "defaultParam": "20", "currentValue": "20", "newValue": null, "minParam": "7.5", "maxParam": "37.5", "machineId": 103, "isPrivate": false}, {"id": 56171, "name": "<PERSON>/<PERSON> Ingredient Speed - F1", "macro": "EEP_OZMIN_INGR_SPEED_3", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 412, "boardName": "Pump Board 0", "boardId": 0, "paramType": "SINGLE", "defaultParam": "7.5", "currentValue": "15", "newValue": "0", "minParam": "7.5", "maxParam": "37.5", "machineId": 103, "isPrivate": false}, {"id": 56172, "name": "Oz/Min Ingredient Speed - A5", "macro": "EEP_OZMIN_INGR_SPEED_0", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 400, "boardName": "Pump Board 9", "boardId": 9, "paramType": "SINGLE", "defaultParam": "20", "currentValue": "20", "newValue": null, "minParam": "7.5", "maxParam": "37.5", "machineId": 103, "isPrivate": false}, {"id": 56173, "name": "Oz/Min Ingredient Speed - A3", "macro": "EEP_OZMIN_INGR_SPEED_0", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 400, "boardName": "Pump Board 8", "boardId": 8, "paramType": "SINGLE", "defaultParam": "20", "currentValue": "20", "newValue": "15", "minParam": "7.5", "maxParam": "37.5", "machineId": 103, "isPrivate": false}, {"id": 56174, "name": "<PERSON>/<PERSON> Ingredient Speed - A1", "macro": "EEP_OZMIN_INGR_SPEED_0", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 400, "boardName": "Pump Board 7", "boardId": 7, "paramType": "SINGLE", "defaultParam": "20", "currentValue": "20", "newValue": null, "minParam": "7.5", "maxParam": "37.5", "machineId": 103, "isPrivate": false}, {"id": 56175, "name": "Oz/Min Ingredient Speed - C6", "macro": "EEP_OZMIN_INGR_SPEED_0", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 400, "boardName": "Pump Board 5", "boardId": 5, "paramType": "SINGLE", "defaultParam": "20", "currentValue": "20", "newValue": "15", "minParam": "7.5", "maxParam": "37.5", "machineId": 103, "isPrivate": false}, {"id": 56176, "name": "Oz/Min Ingredient Speed - C5", "macro": "EEP_OZMIN_INGR_SPEED_0", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 400, "boardName": "Pump Board 4", "boardId": 4, "paramType": "SINGLE", "defaultParam": "20", "currentValue": "20", "newValue": null, "minParam": "7.5", "maxParam": "37.5", "machineId": 103, "isPrivate": false}, {"id": 56177, "name": "Oz/Min Ingredient Speed - C4", "macro": "EEP_OZMIN_INGR_SPEED_0", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 400, "boardName": "Pump Board 3", "boardId": 3, "paramType": "SINGLE", "defaultParam": "20", "currentValue": "20", "newValue": null, "minParam": "7.5", "maxParam": "37.5", "machineId": 103, "isPrivate": false}, {"id": 56178, "name": "Oz/Min Ingredient Speed - C3", "macro": "EEP_OZMIN_INGR_SPEED_0", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 400, "boardName": "Pump Board 2", "boardId": 2, "paramType": "SINGLE", "defaultParam": "20", "currentValue": "20", "newValue": null, "minParam": "7.5", "maxParam": "37.5", "machineId": 103, "isPrivate": false}, {"id": 56179, "name": "Oz/Min Ingredient Speed - C2", "macro": "EEP_OZMIN_INGR_SPEED_0", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 400, "boardName": "Pump Board 1", "boardId": 1, "paramType": "SINGLE", "defaultParam": "20", "currentValue": "20", "newValue": "15", "minParam": "7.5", "maxParam": "37.5", "machineId": 103, "isPrivate": false}, {"id": 56180, "name": "Oz/Min Ingredient Speed - C1", "macro": "EEP_OZMIN_INGR_SPEED_0", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 400, "boardName": "Pump Board 0", "boardId": 0, "paramType": "SINGLE", "defaultParam": "7.5", "currentValue": "15", "newValue": null, "minParam": "7.5", "maxParam": "37.5", "machineId": 103, "isPrivate": false}, {"id": 56181, "name": "Ingredient Line Status Enable (A5, B5, A6, B6)", "macro": "EEP_INGR_LINE_STATUS_EN", "description": "Enable ingredient prime status of pumped ingredients", "address": 436, "boardName": "Pump Board 9", "boardId": 9, "paramType": "BOOLEAN", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 103, "isPrivate": false}, {"id": 56182, "name": "Ingredient Line Status Enable (A3, B3, A4, B4)", "macro": "EEP_INGR_LINE_STATUS_EN", "description": "Enable ingredient prime status of pumped ingredients", "address": 436, "boardName": "Pump Board 8", "boardId": 8, "paramType": "BOOLEAN", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 103, "isPrivate": false}, {"id": 56183, "name": "Ingredient Line Status Enable (A1, B1, A2, B2)", "macro": "EEP_INGR_LINE_STATUS_EN", "description": "Enable ingredient prime status of pumped ingredients", "address": 436, "boardName": "Pump Board 7", "boardId": 7, "paramType": "BOOLEAN", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 103, "isPrivate": false}, {"id": 56184, "name": "Ingredient Line Status Enable (C6, D6, E6, F6)", "macro": "EEP_INGR_LINE_STATUS_EN", "description": "Enable ingredient prime status of pumped ingredients", "address": 436, "boardName": "Pump Board 5", "boardId": 5, "paramType": "BOOLEAN", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 103, "isPrivate": false}, {"id": 56185, "name": "Ingredient Line Status Enable (C5, D5, E5, F5)", "macro": "EEP_INGR_LINE_STATUS_EN", "description": "Enable ingredient prime status of pumped ingredients", "address": 436, "boardName": "Pump Board 4", "boardId": 4, "paramType": "BOOLEAN", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 103, "isPrivate": false}, {"id": 56186, "name": "Ingredient Line Status Enable (C4, D4, E4, F4)", "macro": "EEP_INGR_LINE_STATUS_EN", "description": "Enable ingredient prime status of pumped ingredients", "address": 436, "boardName": "Pump Board 3", "boardId": 3, "paramType": "BOOLEAN", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 103, "isPrivate": false}, {"id": 56187, "name": "Ingredient Line Status Enable (C3, D3, E3, F3)", "macro": "EEP_INGR_LINE_STATUS_EN", "description": "Enable ingredient prime status of pumped ingredients", "address": 436, "boardName": "Pump Board 2", "boardId": 2, "paramType": "BOOLEAN", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 103, "isPrivate": false}, {"id": 56188, "name": "Ingredient Line Status Enable (C2, D2, E2, F2)", "macro": "EEP_INGR_LINE_STATUS_EN", "description": "Enable ingredient prime status of pumped ingredients", "address": 436, "boardName": "Pump Board 1", "boardId": 1, "paramType": "BOOLEAN", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 103, "isPrivate": false}, {"id": 56189, "name": "Ingredient Line Status Enable (C1, D1, E1, F1)", "macro": "EEP_INGR_LINE_STATUS_EN", "description": "Enable ingredient prime status of pumped ingredients", "address": 436, "boardName": "Pump Board 0", "boardId": 0, "paramType": "BOOLEAN", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 103, "isPrivate": false}, {"id": 56190, "name": "Oz/Min Ingredient Speed - B5", "macro": "EEP_OZMIN_INGR_SPEED_1", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 404, "boardName": "Pump Board 9", "boardId": 9, "paramType": "SINGLE", "defaultParam": "20", "currentValue": "20", "newValue": null, "minParam": "7.5", "maxParam": "37.5", "machineId": 103, "isPrivate": false}, {"id": 56191, "name": "Oz/Min Ingredient Speed - B3", "macro": "EEP_OZMIN_INGR_SPEED_1", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 404, "boardName": "Pump Board 8", "boardId": 8, "paramType": "SINGLE", "defaultParam": "20", "currentValue": "20", "newValue": null, "minParam": "7.5", "maxParam": "37.5", "machineId": 103, "isPrivate": false}, {"id": 56192, "name": "Oz/Min Ingredient Speed - B1", "macro": "EEP_OZMIN_INGR_SPEED_1", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 404, "boardName": "Pump Board 7", "boardId": 7, "paramType": "SINGLE", "defaultParam": "20", "currentValue": "20", "newValue": null, "minParam": "7.5", "maxParam": "37.5", "machineId": 103, "isPrivate": false}, {"id": 56193, "name": "Oz/Min Ingredient Speed - D6", "macro": "EEP_OZMIN_INGR_SPEED_1", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 404, "boardName": "Pump Board 5", "boardId": 5, "paramType": "SINGLE", "defaultParam": "20", "currentValue": "20", "newValue": "0", "minParam": "7.5", "maxParam": "37.5", "machineId": 103, "isPrivate": false}, {"id": 56194, "name": "Oz/Min Ingredient Speed - D5", "macro": "EEP_OZMIN_INGR_SPEED_1", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 404, "boardName": "Pump Board 4", "boardId": 4, "paramType": "SINGLE", "defaultParam": "20", "currentValue": "20", "newValue": "0", "minParam": "7.5", "maxParam": "37.5", "machineId": 103, "isPrivate": false}, {"id": 56195, "name": "Oz/Min Ingredient Speed - D4", "macro": "EEP_OZMIN_INGR_SPEED_1", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 404, "boardName": "Pump Board 3", "boardId": 3, "paramType": "SINGLE", "defaultParam": "20", "currentValue": "20", "newValue": "15", "minParam": "7.5", "maxParam": "37.5", "machineId": 103, "isPrivate": false}, {"id": 56196, "name": "Oz/Min Ingredient Speed - D3", "macro": "EEP_OZMIN_INGR_SPEED_1", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 404, "boardName": "Pump Board 2", "boardId": 2, "paramType": "SINGLE", "defaultParam": "20", "currentValue": "20", "newValue": "0", "minParam": "7.5", "maxParam": "37.5", "machineId": 103, "isPrivate": false}, {"id": 56197, "name": "Oz/Min Ingredient Speed - D2", "macro": "EEP_OZMIN_INGR_SPEED_1", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 404, "boardName": "Pump Board 1", "boardId": 1, "paramType": "SINGLE", "defaultParam": "20", "currentValue": "20", "newValue": null, "minParam": "7.5", "maxParam": "37.5", "machineId": 103, "isPrivate": false}, {"id": 56198, "name": "Oz/Min Ingredient Speed - D1", "macro": "EEP_OZMIN_INGR_SPEED_1", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 404, "boardName": "Pump Board 0", "boardId": 0, "paramType": "SINGLE", "defaultParam": "7.5", "currentValue": "15", "newValue": null, "minParam": "7.5", "maxParam": "37.5", "machineId": 103, "isPrivate": false}, {"id": 56199, "name": "Oz/Min Ingredient Speed - A6", "macro": "EEP_OZMIN_INGR_SPEED_2", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 408, "boardName": "Pump Board 9", "boardId": 9, "paramType": "SINGLE", "defaultParam": "20", "currentValue": "20", "newValue": "15", "minParam": "7.5", "maxParam": "37.5", "machineId": 103, "isPrivate": false}, {"id": 56200, "name": "Oz/Min Ingredient Speed - A4", "macro": "EEP_OZMIN_INGR_SPEED_2", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 408, "boardName": "Pump Board 8", "boardId": 8, "paramType": "SINGLE", "defaultParam": "20", "currentValue": "20", "newValue": "0", "minParam": "7.5", "maxParam": "37.5", "machineId": 103, "isPrivate": false}, {"id": 56201, "name": "<PERSON>/Min Ingredient Speed - A2", "macro": "EEP_OZMIN_INGR_SPEED_2", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 408, "boardName": "Pump Board 7", "boardId": 7, "paramType": "SINGLE", "defaultParam": "20", "currentValue": "20", "newValue": "0", "minParam": "7.5", "maxParam": "37.5", "machineId": 103, "isPrivate": false}, {"id": 56202, "name": "Oz/Min Ingredient Speed - E6", "macro": "EEP_OZMIN_INGR_SPEED_2", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 408, "boardName": "Pump Board 5", "boardId": 5, "paramType": "SINGLE", "defaultParam": "20", "currentValue": "20", "newValue": "0", "minParam": "7.5", "maxParam": "37.5", "machineId": 103, "isPrivate": false}, {"id": 56203, "name": "Oz/Min Ingredient Speed - E5", "macro": "EEP_OZMIN_INGR_SPEED_2", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 408, "boardName": "Pump Board 4", "boardId": 4, "paramType": "SINGLE", "defaultParam": "20", "currentValue": "20", "newValue": "0", "minParam": "7.5", "maxParam": "37.5", "machineId": 103, "isPrivate": false}, {"id": 56204, "name": "Oz/Min Ingredient Speed - E4", "macro": "EEP_OZMIN_INGR_SPEED_2", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 408, "boardName": "Pump Board 3", "boardId": 3, "paramType": "SINGLE", "defaultParam": "20", "currentValue": "20", "newValue": null, "minParam": "7.5", "maxParam": "37.5", "machineId": 103, "isPrivate": false}, {"id": 56205, "name": "Oz/Min Ingredient Speed - E3", "macro": "EEP_OZMIN_INGR_SPEED_2", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 408, "boardName": "Pump Board 2", "boardId": 2, "paramType": "SINGLE", "defaultParam": "20", "currentValue": "20", "newValue": "0", "minParam": "7.5", "maxParam": "37.5", "machineId": 103, "isPrivate": false}, {"id": 56206, "name": "Oz/Min Ingredient Speed - E2", "macro": "EEP_OZMIN_INGR_SPEED_2", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 408, "boardName": "Pump Board 1", "boardId": 1, "paramType": "SINGLE", "defaultParam": "20", "currentValue": "20", "newValue": null, "minParam": "7.5", "maxParam": "37.5", "machineId": 103, "isPrivate": false}, {"id": 56207, "name": "Oz/Min Ingredient Speed - E1", "macro": "EEP_OZMIN_INGR_SPEED_2", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 408, "boardName": "Pump Board 0", "boardId": 0, "paramType": "SINGLE", "defaultParam": "7.5", "currentValue": "15", "newValue": null, "minParam": "7.5", "maxParam": "37.5", "machineId": 103, "isPrivate": false}, {"id": 56209, "name": "Ingredient Sensor Enable - C1,D1,E1,F1", "macro": "EEP_BUBBLE_DETECT_ENABLE", "description": "Enable the use of ingredient detection sensors for an entire column of ingredients", "address": 160, "boardName": "Pump Board 0", "boardId": 0, "paramType": "BOOLEAN", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 103, "isPrivate": false}, {"id": 56210, "name": "Bubble Detection Threshold - C1", "macro": "EEP_BUBBLE_ROC_THRESHOLD_0", "description": "Rate of change value required to signify a runout. (Default Oat milk = 30, almond/dairy = 50, all other 75+)", "address": 368, "boardName": "Pump Board 0", "boardId": 0, "paramType": "BYTE", "defaultParam": "50", "currentValue": "50", "newValue": null, "minParam": "15", "maxParam": "150", "machineId": 103, "isPrivate": false}, {"id": 56211, "name": "Bubble Detection Threshold - F1", "macro": "EEP_BUBBLE_ROC_THRESHOLD_3", "description": "Rate of change value required to signify a runout. (Default Oat milk = 30, almond/dairy = 50, all other 75+)", "address": 371, "boardName": "Pump Board 0", "boardId": 0, "paramType": "BYTE", "defaultParam": "50", "currentValue": "50", "newValue": "0", "minParam": "15", "maxParam": "150", "machineId": 103, "isPrivate": false}, {"id": 56212, "name": "Bubble Detection Threshold - E1", "macro": "EEP_BUBBLE_ROC_THRESHOLD_2", "description": "Rate of change value required to signify a runout. (Default Oat milk = 30, almond/dairy = 50, all other 75+)", "address": 370, "boardName": "Pump Board 0", "boardId": 0, "paramType": "BYTE", "defaultParam": "50", "currentValue": "50", "newValue": null, "minParam": "15", "maxParam": "150", "machineId": 103, "isPrivate": false}, {"id": 56213, "name": "Bubble Detection Threshold - D1", "macro": "EEP_BUBBLE_ROC_THRESHOLD_1", "description": "Rate of change value required to signify a runout. (Default Oat milk = 30, almond/dairy = 50, all other 75+)", "address": 369, "boardName": "Pump Board 0", "boardId": 0, "paramType": "BYTE", "defaultParam": "50", "currentValue": "50", "newValue": null, "minParam": "15", "maxParam": "150", "machineId": 103, "isPrivate": false}, {"id": 56214, "name": "Ingredient Sensor Enable - C2, E2, D2, F2", "macro": "EEP_BUBBLE_DETECT_ENABLE", "description": "Enable the use of ingredient detection sensors for an entire column of ingredients", "address": 160, "boardName": "Pump Board 1", "boardId": 1, "paramType": "BOOLEAN", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 103, "isPrivate": false}, {"id": 56216, "name": "Bubble Detection Threshold - D2", "macro": "EEP_BUBBLE_ROC_THRESHOLD_1", "description": "Rate of change value required to signify a runout. (Default Oat milk = 30, almond/dairy = 50, all other 75+)", "address": 369, "boardName": "Pump Board 1", "boardId": 1, "paramType": "BYTE", "defaultParam": "50", "currentValue": "50", "newValue": null, "minParam": "15", "maxParam": "150", "machineId": 103, "isPrivate": false}, {"id": 56217, "name": "Bubble Detection Threshold - C2", "macro": "EEP_BUBBLE_ROC_THRESHOLD_0", "description": "Rate of change value required to signify a runout. (Default Oat milk = 30, almond/dairy = 50, all other 75+)", "address": 368, "boardName": "Pump Board 1", "boardId": 1, "paramType": "BYTE", "defaultParam": "50", "currentValue": "50", "newValue": "50", "minParam": "15", "maxParam": "150", "machineId": 103, "isPrivate": false}, {"id": 56218, "name": "Bubble Detection Threshold - F2", "macro": "EEP_BUBBLE_ROC_THRESHOLD_3", "description": "Rate of change value required to signify a runout. (Default Oat milk = 30, almond/dairy = 50, all other 75+)", "address": 371, "boardName": "Pump Board 1", "boardId": 1, "paramType": "BYTE", "defaultParam": "50", "currentValue": "50", "newValue": null, "minParam": "15", "maxParam": "150", "machineId": 103, "isPrivate": false}, {"id": 56219, "name": "Bubble Detection Threshold - E2", "macro": "EEP_BUBBLE_ROC_THRESHOLD_2", "description": "Rate of change value required to signify a runout. (Default Oat milk = 30, almond/dairy = 50, all other 75+)", "address": 370, "boardName": "Pump Board 1", "boardId": 1, "paramType": "BYTE", "defaultParam": "50", "currentValue": "50", "newValue": null, "minParam": "15", "maxParam": "150", "machineId": 103, "isPrivate": false}, {"id": 56221, "name": "Ingredient Sensor Enable - C3, D3, E3, F3", "macro": "EEP_BUBBLE_DETECT_ENABLE", "description": "Enable the use of ingredient detection sensors for an entire column of ingredients", "address": 160, "boardName": "Pump Board 2", "boardId": 2, "paramType": "BOOLEAN", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 103, "isPrivate": false}, {"id": 56222, "name": "Bubble Detection Threshold - C3", "macro": "EEP_BUBBLE_ROC_THRESHOLD_0", "description": "Rate of change value required to signify a runout. (Default Oat milk = 30, almond/dairy = 50, all other 75+)", "address": 368, "boardName": "Pump Board 2", "boardId": 2, "paramType": "BYTE", "defaultParam": "50", "currentValue": "50", "newValue": null, "minParam": "15", "maxParam": "150", "machineId": 103, "isPrivate": false}, {"id": 56223, "name": "Bubble Detection Threshold - D3", "macro": "EEP_BUBBLE_ROC_THRESHOLD_1", "description": "Rate of change value required to signify a runout. (Default Oat milk = 30, almond/dairy = 50, all other 75+)", "address": 369, "boardName": "Pump Board 2", "boardId": 2, "paramType": "BYTE", "defaultParam": "50", "currentValue": "50", "newValue": "0", "minParam": "15", "maxParam": "150", "machineId": 103, "isPrivate": false}, {"id": 56224, "name": "Bubble Detection Threshold - F3", "macro": "EEP_BUBBLE_ROC_THRESHOLD_3", "description": "Rate of change value required to signify a runout. (Default Oat milk = 30, almond/dairy = 50, all other 75+)", "address": 371, "boardName": "Pump Board 2", "boardId": 2, "paramType": "BYTE", "defaultParam": "50", "currentValue": "50", "newValue": "0", "minParam": "15", "maxParam": "150", "machineId": 103, "isPrivate": false}, {"id": 56225, "name": "Bubble Detection Threshold - E3", "macro": "EEP_BUBBLE_ROC_THRESHOLD_2", "description": "Rate of change value required to signify a runout. (Default Oat milk = 30, almond/dairy = 50, all other 75+)", "address": 370, "boardName": "Pump Board 2", "boardId": 2, "paramType": "BYTE", "defaultParam": "50", "currentValue": "50", "newValue": "0", "minParam": "15", "maxParam": "150", "machineId": 103, "isPrivate": false}, {"id": 56226, "name": "Ingredient Sensor Enable - C4, D4, E4, F4", "macro": "EEP_BUBBLE_DETECT_ENABLE", "description": "Enable the use of ingredient detection sensors for an entire column of ingredients", "address": 160, "boardName": "Pump Board 3", "boardId": 3, "paramType": "BOOLEAN", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 103, "isPrivate": false}, {"id": 56228, "name": "Bubble Detection Threshold - C4", "macro": "EEP_BUBBLE_ROC_THRESHOLD_0", "description": "Rate of change value required to signify a runout. (Default Oat milk = 30, almond/dairy = 50, all other 75+)", "address": 368, "boardName": "Pump Board 3", "boardId": 3, "paramType": "BYTE", "defaultParam": "50", "currentValue": "50", "newValue": null, "minParam": "15", "maxParam": "150", "machineId": 103, "isPrivate": false}, {"id": 56229, "name": "Bubble Detection Threshold - D4", "macro": "EEP_BUBBLE_ROC_THRESHOLD_1", "description": "Rate of change value required to signify a runout. (Default Oat milk = 30, almond/dairy = 50, all other 75+)", "address": 369, "boardName": "Pump Board 3", "boardId": 3, "paramType": "BYTE", "defaultParam": "50", "currentValue": "50", "newValue": "50", "minParam": "15", "maxParam": "150", "machineId": 103, "isPrivate": false}, {"id": 56230, "name": "Bubble Detection Threshold - E4", "macro": "EEP_BUBBLE_ROC_THRESHOLD_2", "description": "Rate of change value required to signify a runout. (Default Oat milk = 30, almond/dairy = 50, all other 75+)", "address": 370, "boardName": "Pump Board 3", "boardId": 3, "paramType": "BYTE", "defaultParam": "50", "currentValue": "50", "newValue": null, "minParam": "15", "maxParam": "150", "machineId": 103, "isPrivate": false}, {"id": 56231, "name": "Bubble Detection Threshold - F4", "macro": "EEP_BUBBLE_ROC_THRESHOLD_3", "description": "Rate of change value required to signify a runout. (Default Oat milk = 30, almond/dairy = 50, all other 75+)", "address": 371, "boardName": "Pump Board 3", "boardId": 3, "paramType": "BYTE", "defaultParam": "50", "currentValue": "50", "newValue": null, "minParam": "15", "maxParam": "150", "machineId": 103, "isPrivate": false}, {"id": 56232, "name": "Ingredient Sensor Enable - C5, D5, E5, F5", "macro": "EEP_BUBBLE_DETECT_ENABLE", "description": "Enable the use of ingredient detection sensors for an entire column of ingredients", "address": 160, "boardName": "Pump Board 4", "boardId": 4, "paramType": "BOOLEAN", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 103, "isPrivate": false}, {"id": 56234, "name": "Bubble Detection Threshold - C5", "macro": "EEP_BUBBLE_ROC_THRESHOLD_0", "description": "Rate of change value required to signify a runout. (Default Oat milk = 30, almond/dairy = 50, all other 75+)", "address": 368, "boardName": "Pump Board 4", "boardId": 4, "paramType": "BYTE", "defaultParam": "50", "currentValue": "50", "newValue": null, "minParam": "15", "maxParam": "150", "machineId": 103, "isPrivate": false}, {"id": 56235, "name": "Bubble Detection Threshold - D5", "macro": "EEP_BUBBLE_ROC_THRESHOLD_1", "description": "Rate of change value required to signify a runout. (Default Oat milk = 30, almond/dairy = 50, all other 75+)", "address": 369, "boardName": "Pump Board 4", "boardId": 4, "paramType": "BYTE", "defaultParam": "50", "currentValue": "50", "newValue": "0", "minParam": "15", "maxParam": "150", "machineId": 103, "isPrivate": false}, {"id": 56236, "name": "Bubble Detection Threshold - F5", "macro": "EEP_BUBBLE_ROC_THRESHOLD_3", "description": "Rate of change value required to signify a runout. (Default Oat milk = 30, almond/dairy = 50, all other 75+)", "address": 371, "boardName": "Pump Board 4", "boardId": 4, "paramType": "BYTE", "defaultParam": "50", "currentValue": "50", "newValue": "0", "minParam": "15", "maxParam": "150", "machineId": 103, "isPrivate": false}, {"id": 56237, "name": "Ingredient Sensor Enable - C6, D6, E6, F6", "macro": "EEP_BUBBLE_DETECT_ENABLE", "description": "Enable the use of ingredient detection sensors for an entire column of ingredients", "address": 160, "boardName": "Pump Board 5", "boardId": 5, "paramType": "BOOLEAN", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 103, "isPrivate": false}, {"id": 56239, "name": "Bubble Detection Threshold - C6", "macro": "EEP_BUBBLE_ROC_THRESHOLD_0", "description": "Rate of change value required to signify a runout. (Default Oat milk = 30, almond/dairy = 50, all other 75+)", "address": 368, "boardName": "Pump Board 5", "boardId": 5, "paramType": "BYTE", "defaultParam": "50", "currentValue": "50", "newValue": "50", "minParam": "15", "maxParam": "150", "machineId": 103, "isPrivate": false}, {"id": 56240, "name": "Bubble Detection Threshold - E6", "macro": "EEP_BUBBLE_ROC_THRESHOLD_2", "description": "Rate of change value required to signify a runout. (Default Oat milk = 30, almond/dairy = 50, all other 75+)", "address": 370, "boardName": "Pump Board 5", "boardId": 5, "paramType": "BYTE", "defaultParam": "50", "currentValue": "50", "newValue": "0", "minParam": "15", "maxParam": "150", "machineId": 103, "isPrivate": false}, {"id": 56241, "name": "Bubble Detection Threshold - D6", "macro": "EEP_BUBBLE_ROC_THRESHOLD_1", "description": "Rate of change value required to signify a runout. (Default Oat milk = 30, almond/dairy = 50, all other 75+)", "address": 369, "boardName": "Pump Board 5", "boardId": 5, "paramType": "BYTE", "defaultParam": "50", "currentValue": "50", "newValue": "0", "minParam": "15", "maxParam": "150", "machineId": 103, "isPrivate": false}, {"id": 56242, "name": "Bubble Detection Threshold - F6", "macro": "EEP_BUBBLE_ROC_THRESHOLD_3", "description": "Rate of change value required to signify a runout. (Default Oat milk = 30, almond/dairy = 50, all other 75+)", "address": 371, "boardName": "Pump Board 5", "boardId": 5, "paramType": "BYTE", "defaultParam": "50", "currentValue": "50", "newValue": null, "minParam": "15", "maxParam": "150", "machineId": 103, "isPrivate": false}, {"id": 56243, "name": "Ingredient Sensor Enable - A1, A2, B1, B2", "macro": "EEP_BUBBLE_DETECT_ENABLE", "description": "Enable the use of ingredient detection sensors for a group of 4 house mix ingredients", "address": 160, "boardName": "Pump Board 7", "boardId": 7, "paramType": "BOOLEAN", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 103, "isPrivate": false}, {"id": 56249, "name": "Bubble Detection Threshold - A1", "macro": "EEP_BUBBLE_ROC_THRESHOLD_0", "description": "Rate of change value required to signify a runout. (Default Oat milk = 30, almond/dairy = 50, all other 75+)", "address": 368, "boardName": "Pump Board 7", "boardId": 7, "paramType": "BYTE", "defaultParam": "50", "currentValue": "50", "newValue": null, "minParam": "15", "maxParam": "150", "machineId": 103, "isPrivate": false}, {"id": 56250, "name": "Bubble Detection Threshold - B2", "macro": "EEP_BUBBLE_ROC_THRESHOLD_3", "description": "Rate of change value required to signify a runout. (Default Oat milk = 30, almond/dairy = 50, all other 75+)", "address": 371, "boardName": "Pump Board 7", "boardId": 7, "paramType": "BYTE", "defaultParam": "50", "currentValue": "50", "newValue": null, "minParam": "15", "maxParam": "150", "machineId": 103, "isPrivate": false}, {"id": 56251, "name": "Bubble Detection Threshold - A2", "macro": "EEP_BUBBLE_ROC_THRESHOLD_2", "description": "Rate of change value required to signify a runout. (Default Oat milk = 30, almond/dairy = 50, all other 75+)", "address": 370, "boardName": "Pump Board 7", "boardId": 7, "paramType": "BYTE", "defaultParam": "50", "currentValue": "50", "newValue": "0", "minParam": "15", "maxParam": "150", "machineId": 103, "isPrivate": false}, {"id": 56252, "name": "Bubble Detection Threshold - B1", "macro": "EEP_BUBBLE_ROC_THRESHOLD_1", "description": "Rate of change value required to signify a runout. (Default Oat milk = 30, almond/dairy = 50, all other 75+)", "address": 369, "boardName": "Pump Board 7", "boardId": 7, "paramType": "BYTE", "defaultParam": "50", "currentValue": "50", "newValue": null, "minParam": "15", "maxParam": "150", "machineId": 103, "isPrivate": false}, {"id": 56253, "name": "Ingredient Sensor Enable - A3, A4, B3, B4", "macro": "EEP_BUBBLE_DETECT_ENABLE", "description": "Enable the use of ingredient detection sensors for a group of 4 house mix ingredients", "address": 160, "boardName": "Pump Board 8", "boardId": 8, "paramType": "BOOLEAN", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 103, "isPrivate": false}, {"id": 56259, "name": "Bubble Detection Threshold - B4", "macro": "EEP_BUBBLE_ROC_THRESHOLD_3", "description": "Rate of change value required to signify a runout. (Default Oat milk = 30, almond/dairy = 50, all other 75+)", "address": 371, "boardName": "Pump Board 8", "boardId": 8, "paramType": "BYTE", "defaultParam": "50", "currentValue": "50", "newValue": null, "minParam": "15", "maxParam": "150", "machineId": 103, "isPrivate": false}, {"id": 56260, "name": "Bubble Detection Threshold - B3", "macro": "EEP_BUBBLE_ROC_THRESHOLD_1", "description": "Rate of change value required to signify a runout. (Default Oat milk = 30, almond/dairy = 50, all other 75+)", "address": 369, "boardName": "Pump Board 8", "boardId": 8, "paramType": "BYTE", "defaultParam": "50", "currentValue": "50", "newValue": null, "minParam": "15", "maxParam": "150", "machineId": 103, "isPrivate": false}, {"id": 56261, "name": "Bubble Detection Threshold - A3", "macro": "EEP_BUBBLE_ROC_THRESHOLD_0", "description": "Rate of change value required to signify a runout. (Default Oat milk = 30, almond/dairy = 50, all other 75+)", "address": 368, "boardName": "Pump Board 8", "boardId": 8, "paramType": "BYTE", "defaultParam": "50", "currentValue": "50", "newValue": "50", "minParam": "15", "maxParam": "150", "machineId": 103, "isPrivate": false}, {"id": 56262, "name": "Bubble Detection Threshold - A4", "macro": "EEP_BUBBLE_ROC_THRESHOLD_2", "description": "Rate of change value required to signify a runout. (Default Oat milk = 30, almond/dairy = 50, all other 75+)", "address": 370, "boardName": "Pump Board 8", "boardId": 8, "paramType": "BYTE", "defaultParam": "50", "currentValue": "50", "newValue": "0", "minParam": "15", "maxParam": "150", "machineId": 103, "isPrivate": false}, {"id": 56264, "name": "Ingredient Sensor Enable - A5, A6, B5, B6", "macro": "EEP_BUBBLE_DETECT_ENABLE", "description": "Enable the use of ingredient detection sensors for a group of 4 house mix ingredients", "address": 160, "boardName": "Pump Board 9", "boardId": 9, "paramType": "BOOLEAN", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 103, "isPrivate": false}, {"id": 56269, "name": "Bubble Detection Threshold - A5", "macro": "EEP_BUBBLE_ROC_THRESHOLD_0", "description": "Rate of change value required to signify a runout. (Default Oat milk = 30, almond/dairy = 50, all other 75+)", "address": 368, "boardName": "Pump Board 9", "boardId": 9, "paramType": "BYTE", "defaultParam": "50", "currentValue": "50", "newValue": null, "minParam": "15", "maxParam": "150", "machineId": 103, "isPrivate": false}, {"id": 56270, "name": "Bubble Detection Threshold - B5", "macro": "EEP_BUBBLE_ROC_THRESHOLD_1", "description": "Rate of change value required to signify a runout. (Default Oat milk = 30, almond/dairy = 50, all other 75+)", "address": 369, "boardName": "Pump Board 9", "boardId": 9, "paramType": "BYTE", "defaultParam": "50", "currentValue": "50", "newValue": null, "minParam": "15", "maxParam": "150", "machineId": 103, "isPrivate": false}, {"id": 56271, "name": "Bubble Detection Threshold - B6", "macro": "EEP_BUBBLE_ROC_THRESHOLD_3", "description": "Rate of change value required to signify a runout. (Default Oat milk = 30, almond/dairy = 50, all other 75+)", "address": 371, "boardName": "Pump Board 9", "boardId": 9, "paramType": "BYTE", "defaultParam": "50", "currentValue": "50", "newValue": "0", "minParam": "15", "maxParam": "150", "machineId": 103, "isPrivate": false}, {"id": 56272, "name": "Bubble Detection Threshold - A6", "macro": "EEP_BUBBLE_ROC_THRESHOLD_2", "description": "Rate of change value required to signify a runout. (Default Oat milk = 30, almond/dairy = 50, all other 75+)", "address": 370, "boardName": "Pump Board 9", "boardId": 9, "paramType": "BYTE", "defaultParam": "50", "currentValue": "50", "newValue": "50", "minParam": "15", "maxParam": "150", "machineId": 103, "isPrivate": false}, {"id": 56273, "name": "Lower Temperature Threshold (Bottom Temp Sensor)", "macro": "EEP_LOWER_TEMP_THRESHOLD_BOT", "description": "Temperature of the bottom fridge sensor (in degrees Fahrenheit) below which the compressor is turned OFF", "address": 228, "boardName": "Cooling", "boardId": 576, "paramType": "SINGLE", "defaultParam": "36", "currentValue": "36", "newValue": null, "minParam": "32", "maxParam": "46", "machineId": 103, "isPrivate": false}, {"id": 56274, "name": "<PERSON><PERSON> Defrost Timeout", "macro": "EEP_DEFROST_TIMEOUT", "description": "Time that the compressor is forced to stay off during a defrost cycle (in seconds)", "address": 240, "boardName": "Cooling", "boardId": 576, "paramType": "WORD", "defaultParam": "3600", "currentValue": "3600", "newValue": null, "minParam": "1800", "maxParam": "43200", "machineId": 103, "isPrivate": false}, {"id": 56275, "name": "Refresh Batching Enable", "macro": "EEP_ENABLE_REFRESH_BATCHING ", "description": "Refreshes happen max once/hr", "address": 368, "boardName": "Master", "boardId": 65535, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 103, "isPrivate": false}, {"id": 56276, "name": "Bib Cleaning Quantity (every drink)", "macro": "EEP_CLEAN_BIB_QTY", "description": "Amount (in 1/20 fl oz increments) of water to dispense after a drink is dispensed to clean the bib and diffuser. This is also 1/4 of the amount of water used to clean the bib during refresh cycles if ", "address": 336, "boardName": "Master", "boardId": 65535, "paramType": "WORD", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "20", "machineId": 103, "isPrivate": false}, {"id": 56301, "name": "Refresh Sync Enable", "macro": "EEP_ENABLE_REFRESH_SYNC", "description": "Attempt to set refresh schedule to the top of the hour every hour. If the top of the next hour is unknown, continue to refresh every hour", "address": 369, "boardName": "Master", "boardId": 65535, "paramType": "BOOLEAN", "defaultParam": "1", "currentValue": "true", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 103, "isPrivate": false}, {"id": 56302, "name": "Bubble Detection Threshold - E5", "macro": "EEP_BUBBLE_ROC_THRESHOLD_2", "description": "Rate of change value required to signify a runout. (Default Oat milk = 30, almond/dairy = 50, all other 75+)", "address": 370, "boardName": "Pump Board 4", "boardId": 4, "paramType": "BYTE", "defaultParam": "50", "currentValue": "50", "newValue": "0", "minParam": "15", "maxParam": "150", "machineId": 103, "isPrivate": false}, {"id": 56303, "name": "Compressor Normal Runtime Max", "macro": "EEP_COMPRESSOR_NORMAL_RT_MAX", "description": "Continuous time that the compressor can be enabled before sending a runtime alert", "address": 208, "boardName": "Cooling", "boardId": 576, "paramType": "WORD", "defaultParam": "43200", "currentValue": "43200", "newValue": null, "minParam": "900", "maxParam": "10800", "machineId": 103, "isPrivate": false}, {"id": 56305, "name": "Enable Water Rinse", "macro": "EEP_ENABLE_WATER_RINSE", "description": "Spray water into the bib during a refresh to clean out the nozzle", "address": 371, "boardName": "Master", "boardId": 65535, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 103, "isPrivate": false}, {"id": 56306, "name": "Refrigeration Scheduled OFF Time", "macro": "EEP_COMPRESSOR_STATIC_OFF", "description": "Time the compressor is turned OFF for static, scheduled compressor operation (in seconds)", "address": 144, "boardName": "Cooling", "boardId": 576, "paramType": "WORD", "defaultParam": "1800", "currentValue": "1800", "newValue": null, "minParam": "300", "maxParam": "7200", "machineId": 103, "isPrivate": false}, {"id": 56307, "name": "Refrigeration Scheduled ON Time", "macro": "EEP_COMPRESSOR_STATIC_ON", "description": "Time the compressor is turned ON for static, scheduled compressor operation (in seconds)", "address": 128, "boardName": "Cooling", "boardId": 576, "paramType": "WORD", "defaultParam": "1800", "currentValue": "1800", "newValue": null, "minParam": "300", "maxParam": "7200", "machineId": 103, "isPrivate": false}, {"id": 59810, "name": "Enable Cooling Board Current Measurements", "macro": "EEP_ENABLE_COOLING_CURRENT_CHECK", "description": "Enable Cooling Board Current Measurements for fans and compressor", "address": 272, "boardName": "Cooling", "boardId": 576, "paramType": "BOOLEAN", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 103, "isPrivate": false}, {"id": 59811, "name": "Global Cleaner Rinse Speed", "macro": "EEP_GLOBAL_CLEANER_RINSE_SPEED", "description": "The cleaning rate (in oz/min) that cleaner is rinsed during a self clean", "address": 388, "boardName": "Master", "boardId": 65535, "paramType": "SINGLE", "defaultParam": "15.0", "currentValue": "15.0", "newValue": null, "minParam": "5", "maxParam": "37.5", "machineId": 103, "isPrivate": false}, {"id": 59812, "name": "Global Ingredient Sensor Active Threshold", "macro": "EEP_LEVEL_THRESHOLD_GLOABL", "description": "Ingredient level (expressed in 1/20 fl. oz. increments) below which runout detection becomes active for ALL ingredients", "address": 338, "boardName": "Master", "boardId": 65535, "paramType": "WORD", "defaultParam": "100", "currentValue": "100", "newValue": null, "minParam": "10", "maxParam": "3500", "machineId": 103, "isPrivate": false}, {"id": 59813, "name": "Global Water Rinse Speed", "macro": "EEP_GLOBAL_WATER_RINSE_SPEED", "description": "The cleaning rate (in oz/min) that water is rinsed during a self clean", "address": 384, "boardName": "Master", "boardId": 65535, "paramType": "SINGLE", "defaultParam": "35.0", "currentValue": "35.0", "newValue": null, "minParam": "5", "maxParam": "37.5", "machineId": 103, "isPrivate": false}, {"id": 59814, "name": "Static Cycle Enable", "macro": "EEP_ENABLE_STATIC_COMPRESSOR", "description": "Enable static cycling of the compressor regardless of the state of temperature sensors. (Note that if ", "address": 49, "boardName": "Cooling", "boardId": 576, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 103, "isPrivate": false}, {"id": 63439, "name": "Cleaning Concentrate Ratio", "macro": "EEP_CLEANER_RATIO", "description": "The ratio of water to cleaner for internal self cleaning. A value of “0” means that pre-diluted cleaner is being used in place of cleaner concentrate, and there will be no water used for dilution ", "address": 393, "boardName": "Master", "boardId": 65535, "paramType": "BYTE", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "30", "machineId": 103, "isPrivate": false}, {"id": 66527, "name": "Allow Runout A1, A2, B1, B2", "macro": "EEP_ALLOW_RUNOUT", "description": "Allow runout errors for this board's ingredients during drink requests", "address": 277, "boardName": "Pump Board 7", "boardId": 7, "paramType": "BOOLEAN", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 103, "isPrivate": false}, {"id": 66528, "name": "Allow Runout A3, A4, B3, B4", "macro": "EEP_ALLOW_RUNOUT", "description": "Allow runout errors for this board's ingredients during drink requests", "address": 277, "boardName": "Pump Board 8", "boardId": 8, "paramType": "BOOLEAN", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 103, "isPrivate": false}, {"id": 66529, "name": "Allow Runout A5, A6, B5, B6", "macro": "EEP_ALLOW_RUNOUT", "description": "Allow runout errors for this board's ingredients during drink requests", "address": 277, "boardName": "Pump Board 9", "boardId": 9, "paramType": "BOOLEAN", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 103, "isPrivate": false}, {"id": 66530, "name": "Allow Runout C1, D1, E1, F1", "macro": "EEP_ALLOW_RUNOUT", "description": "Allow runout errors for this board's ingredients during drink requests", "address": 277, "boardName": "Pump Board 0", "boardId": 0, "paramType": "BOOLEAN", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 103, "isPrivate": false}, {"id": 66531, "name": "Allow Runout C2, E2, D2, F2", "macro": "EEP_ALLOW_RUNOUT", "description": "Allow runout errors for this board's ingredients during drink requests", "address": 277, "boardName": "Pump Board 1", "boardId": 1, "paramType": "BOOLEAN", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 103, "isPrivate": false}, {"id": 66532, "name": "Allow Runout C3, D3, E3, F3", "macro": "EEP_ALLOW_RUNOUT", "description": "Allow runout errors for this board's ingredients during drink requests", "address": 277, "boardName": "Pump Board 2", "boardId": 2, "paramType": "BOOLEAN", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 103, "isPrivate": false}, {"id": 66533, "name": "Allow Runout C4, D4, E4, F4", "macro": "EEP_ALLOW_RUNOUT", "description": "Allow runout errors for this board's ingredients during drink requests", "address": 277, "boardName": "Pump Board 3", "boardId": 3, "paramType": "BOOLEAN", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 103, "isPrivate": false}, {"id": 66534, "name": "Allow Runout C5, D5, E5, F5", "macro": "EEP_ALLOW_RUNOUT", "description": "Allow runout errors for this board's ingredients during drink requests", "address": 277, "boardName": "Pump Board 4", "boardId": 4, "paramType": "BOOLEAN", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 103, "isPrivate": false}, {"id": 66535, "name": "Allow Runout C6, D6, E6, F6", "macro": "EEP_ALLOW_RUNOUT", "description": "Allow runout errors for this board's ingredients during drink requests", "address": 277, "boardName": "Pump Board 5", "boardId": 5, "paramType": "BOOLEAN", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 103, "isPrivate": false}, {"id": 66536, "name": "Compressor Normal Runtime Min", "macro": "EEP_COMPRESSOR_NORMAL_RT_MIN", "description": "Continuous time (in seconds) that the compressor can be disabled before cycling ", "address": 210, "boardName": "Cooling", "boardId": 576, "paramType": "WORD", "defaultParam": "420", "currentValue": "420", "newValue": null, "minParam": "60", "maxParam": "900", "machineId": 103, "isPrivate": false}, {"id": 66537, "name": "First Dispense Overpour Quantity - A1, A2, B1, B2", "macro": "EEP_STARTUP_QTY", "description": "Quantity (expressed in 1/20 fl. oz. increments) to overprime this board's ingredients beyond their priming lengths for the first pour of the day", "address": 305, "boardName": "Pump Board 7", "boardId": 7, "paramType": "BYTE", "defaultParam": "10", "currentValue": "10", "newValue": null, "minParam": "0", "maxParam": "100", "machineId": 103, "isPrivate": false}, {"id": 66538, "name": "First Dispense Overpour Quantity - A3, A4, B3, B4", "macro": "EEP_STARTUP_QTY", "description": "Quantity (expressed in 1/20 fl. oz. increments) to overprime this board's ingredients beyond their priming lengths for the first pour of the day", "address": 305, "boardName": "Pump Board 8", "boardId": 8, "paramType": "BYTE", "defaultParam": "10", "currentValue": "10", "newValue": null, "minParam": "0", "maxParam": "100", "machineId": 103, "isPrivate": false}, {"id": 66539, "name": "First Dispense Overpour Quantity - A5, A6, B5, B6", "macro": "EEP_STARTUP_QTY", "description": "Quantity (expressed in 1/20 fl. oz. increments) to overprime this board's ingredients beyond their priming lengths for the first pour of the day", "address": 305, "boardName": "Pump Board 9", "boardId": 9, "paramType": "BYTE", "defaultParam": "10", "currentValue": "10", "newValue": null, "minParam": "0", "maxParam": "100", "machineId": 103, "isPrivate": false}, {"id": 66540, "name": "First Dispense Overpour Quantity - C1, D1, E1, F1", "macro": "EEP_STARTUP_QTY", "description": "Quantity (expressed in 1/20 fl. oz. increments) to overprime this board's ingredients beyond their priming lengths for the first pour of the day", "address": 305, "boardName": "Pump Board 0", "boardId": 0, "paramType": "BYTE", "defaultParam": "10", "currentValue": "10", "newValue": null, "minParam": "0", "maxParam": "100", "machineId": 103, "isPrivate": false}, {"id": 66541, "name": "First Dispense Overpour Quantity - C2, E2, D2, F2", "macro": "EEP_STARTUP_QTY", "description": "Quantity (expressed in 1/20 fl. oz. increments) to overprime this board's ingredients beyond their priming lengths for the first pour of the day", "address": 305, "boardName": "Pump Board 1", "boardId": 1, "paramType": "BYTE", "defaultParam": "10", "currentValue": "10", "newValue": null, "minParam": "0", "maxParam": "100", "machineId": 103, "isPrivate": false}, {"id": 66542, "name": "First Dispense Overpour Quantity - C3, D3, E3, F3", "macro": "EEP_STARTUP_QTY", "description": "Quantity (expressed in 1/20 fl. oz. increments) to overprime this board's ingredients beyond their priming lengths for the first pour of the day", "address": 305, "boardName": "Pump Board 2", "boardId": 2, "paramType": "BYTE", "defaultParam": "10", "currentValue": "10", "newValue": null, "minParam": "0", "maxParam": "100", "machineId": 103, "isPrivate": false}, {"id": 66543, "name": "First Dispense Overpour Quantity - C4, D4, E4, F4", "macro": "EEP_STARTUP_QTY", "description": "Quantity (expressed in 1/20 fl. oz. increments) to overprime this board's ingredients beyond their priming lengths for the first pour of the day", "address": 305, "boardName": "Pump Board 3", "boardId": 3, "paramType": "BYTE", "defaultParam": "10", "currentValue": "10", "newValue": null, "minParam": "0", "maxParam": "100", "machineId": 103, "isPrivate": false}, {"id": 66544, "name": "First Dispense Overpour Quantity - C5, D5, E5, F5", "macro": "EEP_STARTUP_QTY", "description": "Quantity (expressed in 1/20 fl. oz. increments) to overprime this board's ingredients beyond their priming lengths for the first pour of the day", "address": 305, "boardName": "Pump Board 4", "boardId": 4, "paramType": "BYTE", "defaultParam": "10", "currentValue": "10", "newValue": null, "minParam": "0", "maxParam": "100", "machineId": 103, "isPrivate": false}, {"id": 66545, "name": "First Dispense Overpour Quantity - C6, D6, E6, F6", "macro": "EEP_STARTUP_QTY", "description": "Quantity (expressed in 1/20 fl. oz. increments) to overprime this board's ingredients beyond their priming lengths for the first pour of the day", "address": 305, "boardName": "Pump Board 5", "boardId": 5, "paramType": "BYTE", "defaultParam": "10", "currentValue": "10", "newValue": null, "minParam": "0", "maxParam": "100", "machineId": 103, "isPrivate": false}, {"id": 66546, "name": "Leave ALL Ingredients Unprimed at Startup", "macro": "EEP_ENABLE_UNPRIMED_STARTUP", "description": "At startup, leave ALL lines that were cleaned unprimed in order to save as much ingredient as possible throughout the day. This causes first dispenses of the day to be SLOW! Use with Caution.", "address": 370, "boardName": "Master", "boardId": 65535, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 103, "isPrivate": false}, {"id": 79049, "name": "Allow Retraction A1, A2, B1, B2", "macro": "EEP_ALLOW_RETRACTION", "description": "Allow user-configurable retraction for this board's ingredients", "address": 276, "boardName": "Pump Board 7", "boardId": 7, "paramType": "BOOLEAN", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 103, "isPrivate": false}, {"id": 79050, "name": "Allow Retraction A3, A4, B3, B4", "macro": "EEP_ALLOW_RETRACTION", "description": "Allow user-configurable retraction for this board's ingredients", "address": 276, "boardName": "Pump Board 8", "boardId": 8, "paramType": "BOOLEAN", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 103, "isPrivate": false}, {"id": 79051, "name": "Allow Retraction A5, A6, B5, B6", "macro": "EEP_ALLOW_RETRACTION", "description": "Allow user-configurable retraction for this board's ingredients", "address": 276, "boardName": "Pump Board 9", "boardId": 9, "paramType": "BOOLEAN", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 103, "isPrivate": false}, {"id": 79052, "name": "Allow Retraction C1, D1, E1, F1", "macro": "EEP_ALLOW_RETRACTION", "description": "Allow user-configurable retraction for this board's ingredients", "address": 276, "boardName": "Pump Board 0", "boardId": 0, "paramType": "BOOLEAN", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 103, "isPrivate": false}, {"id": 79053, "name": "Allow Retraction C2, E2, D2, F2", "macro": "EEP_ALLOW_RETRACTION", "description": "Allow user-configurable retraction for this board's ingredients", "address": 276, "boardName": "Pump Board 1", "boardId": 1, "paramType": "BOOLEAN", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 103, "isPrivate": false}, {"id": 79054, "name": "Allow Retraction C3, D3, E3, F3", "macro": "EEP_ALLOW_RETRACTION", "description": "Allow user-configurable retraction for this board's ingredients", "address": 276, "boardName": "Pump Board 2", "boardId": 2, "paramType": "BOOLEAN", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 103, "isPrivate": false}, {"id": 79055, "name": "Allow Retraction C4, D4, E4, F4", "macro": "EEP_ALLOW_RETRACTION", "description": "Allow user-configurable retraction for this board's ingredients", "address": 276, "boardName": "Pump Board 3", "boardId": 3, "paramType": "BOOLEAN", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 103, "isPrivate": false}, {"id": 79056, "name": "Allow Retraction C5, D5, E5, F5", "macro": "EEP_ALLOW_RETRACTION", "description": "Allow user-configurable retraction for this board's ingredients", "address": 276, "boardName": "Pump Board 4", "boardId": 4, "paramType": "BOOLEAN", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 103, "isPrivate": false}, {"id": 79057, "name": "Allow Retraction C6, D6, E6, F6", "macro": "EEP_ALLOW_RETRACTION", "description": "Allow user-configurable retraction for this board's ingredients", "address": 276, "boardName": "Pump Board 5", "boardId": 5, "paramType": "BOOLEAN", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 103, "isPrivate": false}, {"id": 79058, "name": "Bubble Detection - A1", "macro": "EEP_BUBBLE_DETECTION_0", "description": "Flag indicating whether a bubble/air is detected in A1's dispensing line", "address": 16, "boardName": "Pump Board 7", "boardId": 7, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 103, "isPrivate": true}, {"id": 79059, "name": "Bubble Detection - A2", "macro": "EEP_BUBBLE_DETECTION_2", "description": "Flag indicating whether a bubble/air is detected in A2's dispensing line", "address": 48, "boardName": "Pump Board 7", "boardId": 7, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 103, "isPrivate": true}, {"id": 79060, "name": "Bubble Detection - A3", "macro": "EEP_BUBBLE_DETECTION_0", "description": "Flag indicating whether a bubble/air is detected in A3's dispensing line", "address": 16, "boardName": "Pump Board 8", "boardId": 8, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 103, "isPrivate": true}, {"id": 79061, "name": "Bubble Detection - A4", "macro": "EEP_BUBBLE_DETECTION_2", "description": "Flag indicating whether a bubble/air is detected in A4's dispensing line", "address": 48, "boardName": "Pump Board 8", "boardId": 8, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 103, "isPrivate": true}, {"id": 79062, "name": "Bubble Detection - A5", "macro": "EEP_BUBBLE_DETECTION_0", "description": "Flag indicating whether a bubble/air is detected in A5's dispensing line", "address": 16, "boardName": "Pump Board 9", "boardId": 9, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 103, "isPrivate": true}, {"id": 79063, "name": "Bubble Detection - A6", "macro": "EEP_BUBBLE_DETECTION_2", "description": "Flag indicating whether a bubble/air is detected in A6's dispensing line", "address": 48, "boardName": "Pump Board 9", "boardId": 9, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 103, "isPrivate": true}, {"id": 79064, "name": "Bubble Detection - B1", "macro": "EEP_BUBBLE_DETECTION_1", "description": "Flag indicating whether a bubble/air is detected in B1's dispensing line", "address": 32, "boardName": "Pump Board 7", "boardId": 7, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 103, "isPrivate": true}, {"id": 79065, "name": "Bubble Detection - B2", "macro": "EEP_BUBBLE_DETECTION_3", "description": "Flag indicating whether a bubble/air is detected in B2's dispensing line", "address": 64, "boardName": "Pump Board 7", "boardId": 7, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 103, "isPrivate": true}, {"id": 79066, "name": "Bubble Detection - B3", "macro": "EEP_BUBBLE_DETECTION_1", "description": "Flag indicating whether a bubble/air is detected in B3's dispensing line", "address": 32, "boardName": "Pump Board 8", "boardId": 8, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 103, "isPrivate": true}, {"id": 79067, "name": "Bubble Detection - B4", "macro": "EEP_BUBBLE_DETECTION_3", "description": "Flag indicating whether a bubble/air is detected in B4's dispensing line", "address": 64, "boardName": "Pump Board 8", "boardId": 8, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 103, "isPrivate": true}, {"id": 79068, "name": "Bubble Detection - B5", "macro": "EEP_BUBBLE_DETECTION_1", "description": "Flag indicating whether a bubble/air is detected in B5's dispensing line", "address": 32, "boardName": "Pump Board 9", "boardId": 9, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 103, "isPrivate": true}, {"id": 79069, "name": "Bubble Detection - B6", "macro": "EEP_BUBBLE_DETECTION_3", "description": "Flag indicating whether a bubble/air is detected in B6's dispensing line", "address": 64, "boardName": "Pump Board 9", "boardId": 9, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 103, "isPrivate": true}, {"id": 79070, "name": "Bubble Detection - C1", "macro": "EEP_BUBBLE_DETECTION_0", "description": "Flag indicating whether a bubble/air is detected in C1's dispensing line", "address": 16, "boardName": "Pump Board 0", "boardId": 0, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 103, "isPrivate": true}, {"id": 79071, "name": "Bubble Detection - C2", "macro": "EEP_BUBBLE_DETECTION_0", "description": "Flag indicating whether a bubble/air is detected in C2's dispensing line", "address": 16, "boardName": "Pump Board 1", "boardId": 1, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 103, "isPrivate": true}, {"id": 79072, "name": "Bubble Detection - C3", "macro": "EEP_BUBBLE_DETECTION_0", "description": "Flag indicating whether a bubble/air is detected in C3's dispensing line", "address": 16, "boardName": "Pump Board 2", "boardId": 2, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 103, "isPrivate": true}, {"id": 79073, "name": "Bubble Detection - C4", "macro": "EEP_BUBBLE_DETECTION_0", "description": "Flag indicating whether a bubble/air is detected in C4's dispensing line", "address": 16, "boardName": "Pump Board 3", "boardId": 3, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 103, "isPrivate": true}, {"id": 79074, "name": "Bubble Detection - C5", "macro": "EEP_BUBBLE_DETECTION_0", "description": "Flag indicating whether a bubble/air is detected in C5's dispensing line", "address": 16, "boardName": "Pump Board 4", "boardId": 4, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 103, "isPrivate": true}, {"id": 79075, "name": "Bubble Detection - C6", "macro": "EEP_BUBBLE_DETECTION_0", "description": "Flag indicating whether a bubble/air is detected in C6's dispensing line", "address": 16, "boardName": "Pump Board 5", "boardId": 5, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 103, "isPrivate": true}, {"id": 79076, "name": "Bubble Detection - D1", "macro": "EEP_BUBBLE_DETECTION_1", "description": "Flag indicating whether a bubble/air is detected in D1's dispensing line", "address": 32, "boardName": "Pump Board 0", "boardId": 0, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 103, "isPrivate": true}, {"id": 79077, "name": "Bubble Detection - D2", "macro": "EEP_BUBBLE_DETECTION_1", "description": "Flag indicating whether a bubble/air is detected in D2's dispensing line", "address": 32, "boardName": "Pump Board 1", "boardId": 1, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 103, "isPrivate": true}, {"id": 79078, "name": "Bubble Detection - D3", "macro": "EEP_BUBBLE_DETECTION_1", "description": "Flag indicating whether a bubble/air is detected in D3's dispensing line", "address": 32, "boardName": "Pump Board 2", "boardId": 2, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 103, "isPrivate": true}, {"id": 79079, "name": "Bubble Detection - D4", "macro": "EEP_BUBBLE_DETECTION_1", "description": "Flag indicating whether a bubble/air is detected in D4's dispensing line", "address": 32, "boardName": "Pump Board 3", "boardId": 3, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 103, "isPrivate": true}, {"id": 79080, "name": "Bubble Detection - D5", "macro": "EEP_BUBBLE_DETECTION_1", "description": "Flag indicating whether a bubble/air is detected in D5's dispensing line", "address": 32, "boardName": "Pump Board 4", "boardId": 4, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 103, "isPrivate": true}, {"id": 79081, "name": "Bubble Detection - D6", "macro": "EEP_BUBBLE_DETECTION_1", "description": "Flag indicating whether a bubble/air is detected in D6's dispensing line", "address": 32, "boardName": "Pump Board 5", "boardId": 5, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 103, "isPrivate": true}, {"id": 79082, "name": "Bubble Detection - E1", "macro": "EEP_BUBBLE_DETECTION_2", "description": "Flag indicating whether a bubble/air is detected in E1's dispensing line", "address": 48, "boardName": "Pump Board 0", "boardId": 0, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 103, "isPrivate": true}, {"id": 79083, "name": "Bubble Detection - E2", "macro": "EEP_BUBBLE_DETECTION_2", "description": "Flag indicating whether a bubble/air is detected in E2's dispensing line", "address": 48, "boardName": "Pump Board 1", "boardId": 1, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 103, "isPrivate": true}, {"id": 79084, "name": "Bubble Detection - E3", "macro": "EEP_BUBBLE_DETECTION_2", "description": "Flag indicating whether a bubble/air is detected in E3's dispensing line", "address": 48, "boardName": "Pump Board 2", "boardId": 2, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 103, "isPrivate": true}, {"id": 79085, "name": "Bubble Detection - E4", "macro": "EEP_BUBBLE_DETECTION_2", "description": "Flag indicating whether a bubble/air is detected in E4's dispensing line", "address": 48, "boardName": "Pump Board 3", "boardId": 3, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 103, "isPrivate": true}, {"id": 79086, "name": "Bubble Detection - E5", "macro": "EEP_BUBBLE_DETECTION_2", "description": "Flag indicating whether a bubble/air is detected in E5's dispensing line", "address": 48, "boardName": "Pump Board 4", "boardId": 4, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 103, "isPrivate": true}, {"id": 79087, "name": "Bubble Detection - E6", "macro": "EEP_BUBBLE_DETECTION_2", "description": "Flag indicating whether a bubble/air is detected in E6's dispensing line", "address": 48, "boardName": "Pump Board 5", "boardId": 5, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 103, "isPrivate": true}, {"id": 79088, "name": "Bubble Detection - F1", "macro": "EEP_BUBBLE_DETECTION_3", "description": "Flag indicating whether a bubble/air is detected in F1's dispensing line", "address": 64, "boardName": "Pump Board 0", "boardId": 0, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 103, "isPrivate": true}, {"id": 79089, "name": "Bubble Detection - F2", "macro": "EEP_BUBBLE_DETECTION_3", "description": "Flag indicating whether a bubble/air is detected in F2's dispensing line", "address": 64, "boardName": "Pump Board 1", "boardId": 1, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 103, "isPrivate": true}, {"id": 79090, "name": "Bubble Detection - F3", "macro": "EEP_BUBBLE_DETECTION_3", "description": "Flag indicating whether a bubble/air is detected in F3's dispensing line", "address": 64, "boardName": "Pump Board 2", "boardId": 2, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 103, "isPrivate": true}, {"id": 79091, "name": "Bubble Detection - F4", "macro": "EEP_BUBBLE_DETECTION_3", "description": "Flag indicating whether a bubble/air is detected in F4's dispensing line", "address": 64, "boardName": "Pump Board 3", "boardId": 3, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 103, "isPrivate": true}, {"id": 79092, "name": "Bubble Detection - F5", "macro": "EEP_BUBBLE_DETECTION_3", "description": "Flag indicating whether a bubble/air is detected in F5's dispensing line", "address": 64, "boardName": "Pump Board 4", "boardId": 4, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 103, "isPrivate": true}, {"id": 79093, "name": "Bubble Detection - F6", "macro": "EEP_BUBBLE_DETECTION_3", "description": "Flag indicating whether a bubble/air is detected in F6's dispensing line", "address": 64, "boardName": "Pump Board 5", "boardId": 5, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 103, "isPrivate": true}, {"id": 79094, "name": "Calibration Factor - Bib 1", "macro": "EEP_BIB_0_CAL_VAL", "description": "Ingredient calibration factor (Number of ms per fluid oz)", "address": 16, "boardName": "Nozzle", "boardId": 560, "paramType": "WORD", "defaultParam": "2000", "currentValue": "65535", "newValue": null, "minParam": "100", "maxParam": "10000", "machineId": 103, "isPrivate": true}, {"id": 79095, "name": "Calibration Factor - Bib 2", "macro": "EEP_BIB_1_CAL_VAL", "description": "Ingredient calibration factor (Number of ms per fluid oz)", "address": 32, "boardName": "Nozzle", "boardId": 560, "paramType": "WORD", "defaultParam": "2000", "currentValue": "65535", "newValue": null, "minParam": "100", "maxParam": "10000", "machineId": 103, "isPrivate": true}, {"id": 79096, "name": "Calibration Factor - Bib 3", "macro": "EEP_BIB_2_CAL_VAL", "description": "Ingredient calibration factor (Number of ms per fluid oz)", "address": 48, "boardName": "Nozzle", "boardId": 560, "paramType": "WORD", "defaultParam": "2000", "currentValue": "65535", "newValue": null, "minParam": "100", "maxParam": "10000", "machineId": 103, "isPrivate": true}, {"id": 79097, "name": "Calibration Factor - Bib 4", "macro": "EEP_BIB_3_CAL_VAL", "description": "Ingredient calibration factor (Number of ms per fluid oz)", "address": 64, "boardName": "Nozzle", "boardId": 560, "paramType": "WORD", "defaultParam": "2000", "currentValue": "65535", "newValue": null, "minParam": "100", "maxParam": "10000", "machineId": 103, "isPrivate": true}, {"id": 79098, "name": "Calibration Factor - Bib 5", "macro": "EEP_BIB_0_CAL_VAL", "description": "Ingredient calibration factor (Number of ms per fluid oz)", "address": 16, "boardName": "Solenoid", "boardId": 12, "paramType": "WORD", "defaultParam": "2000", "currentValue": "65535", "newValue": null, "minParam": "100", "maxParam": "10000", "machineId": 103, "isPrivate": true}, {"id": 79099, "name": "Calibration Factor - Bib 6", "macro": "EEP_BIB_1_CAL_VAL", "description": "Ingredient calibration factor (Number of ms per fluid oz)", "address": 32, "boardName": "Solenoid", "boardId": 12, "paramType": "WORD", "defaultParam": "2000", "currentValue": "65535", "newValue": null, "minParam": "100", "maxParam": "10000", "machineId": 103, "isPrivate": true}, {"id": 79100, "name": "Calibration Factor - Bib 7", "macro": "EEP_BIB_2_CAL_VAL", "description": "Ingredient calibration factor (Number of ms per fluid oz)", "address": 48, "boardName": "Solenoid", "boardId": 12, "paramType": "WORD", "defaultParam": "2000", "currentValue": "641", "newValue": null, "minParam": "100", "maxParam": "10000", "machineId": 103, "isPrivate": true}, {"id": 79101, "name": "Calibration Factor - Bib 8", "macro": "EEP_BIB_3_CAL_VAL", "description": "Ingredient calibration factor (Number of ms per fluid oz)", "address": 64, "boardName": "Solenoid", "boardId": 12, "paramType": "WORD", "defaultParam": "2000", "currentValue": "65535", "newValue": null, "minParam": "100", "maxParam": "10000", "machineId": 103, "isPrivate": true}, {"id": 79102, "name": "Calibration Factor - Soda Bib", "macro": "EEP_SODA_CAL_VAL", "description": "Ingredient calibration factor (Number of ms per fluid oz)", "address": 80, "boardName": "Nozzle", "boardId": 560, "paramType": "WORD", "defaultParam": "2000", "currentValue": "65535", "newValue": null, "minParam": "100", "maxParam": "10000", "machineId": 103, "isPrivate": true}, {"id": 79103, "name": "Calibration Factor - Water Bib", "macro": "EEP_WATER_CAL_VAL", "description": "Ingredient calibration factor (Number of ms per fluid oz)", "address": 96, "boardName": "Nozzle", "boardId": 560, "paramType": "WORD", "defaultParam": "2000", "currentValue": "65535", "newValue": null, "minParam": "100", "maxParam": "10000", "machineId": 103, "isPrivate": true}, {"id": 79104, "name": "Cleaning Concentrate Ingredient Number", "macro": "EEP_CLEANER_INGR_NUM", "description": "The ingredient number of the cleaner. Currently, this MUST be pump 3 on a pump board. (I.E A6 on pump board board 9, E2 on pump board 2)", "address": 392, "boardName": "Master", "boardId": 65535, "paramType": "BYTE", "defaultParam": "46", "currentValue": "46", "newValue": null, "minParam": "0", "maxParam": "47", "machineId": 103, "isPrivate": false}, {"id": 79105, "name": "Dispensing Calibration Factor - A1", "macro": "EEP_INGR_CALIBRATION_FACTOR_0", "description": "Ingredient calibration factor (Number of pulses per fluid oz)", "address": 384, "boardName": "Pump Board 7", "boardId": 7, "paramType": "DWORD", "defaultParam": "48600", "currentValue": "48600", "newValue": null, "minParam": "9720", "maxParam": "194400", "machineId": 103, "isPrivate": true}, {"id": 79106, "name": "Dispensing Calibration Factor - A2", "macro": "EEP_INGR_CALIBRATION_FACTOR_2", "description": "Ingredient calibration factor (Number of pulses per fluid oz)", "address": 392, "boardName": "Pump Board 7", "boardId": 7, "paramType": "DWORD", "defaultParam": "48600", "currentValue": "48600", "newValue": "0", "minParam": "9720", "maxParam": "194400", "machineId": 103, "isPrivate": true}, {"id": 79107, "name": "Dispensing Calibration Factor - A3", "macro": "EEP_INGR_CALIBRATION_FACTOR_0", "description": "Ingredient calibration factor (Number of pulses per fluid oz)", "address": 384, "boardName": "Pump Board 8", "boardId": 8, "paramType": "DWORD", "defaultParam": "48600", "currentValue": "48600", "newValue": "48600", "minParam": "9720", "maxParam": "194400", "machineId": 103, "isPrivate": true}, {"id": 79108, "name": "Dispensing Calibration Factor - A4", "macro": "EEP_INGR_CALIBRATION_FACTOR_2", "description": "Ingredient calibration factor (Number of pulses per fluid oz)", "address": 392, "boardName": "Pump Board 8", "boardId": 8, "paramType": "DWORD", "defaultParam": "48600", "currentValue": "48600", "newValue": "0", "minParam": "9720", "maxParam": "194400", "machineId": 103, "isPrivate": true}, {"id": 79109, "name": "Dispensing Calibration Factor - A5", "macro": "EEP_INGR_CALIBRATION_FACTOR_0", "description": "Ingredient calibration factor (Number of pulses per fluid oz)", "address": 384, "boardName": "Pump Board 9", "boardId": 9, "paramType": "DWORD", "defaultParam": "48600", "currentValue": "64800", "newValue": null, "minParam": "9720", "maxParam": "194400", "machineId": 103, "isPrivate": true}, {"id": 79110, "name": "Dispensing Calibration Factor - A6", "macro": "EEP_INGR_CALIBRATION_FACTOR_2", "description": "Ingredient calibration factor (Number of pulses per fluid oz)", "address": 392, "boardName": "Pump Board 9", "boardId": 9, "paramType": "DWORD", "defaultParam": "48600", "currentValue": "48600", "newValue": "48600", "minParam": "9720", "maxParam": "194400", "machineId": 103, "isPrivate": true}, {"id": 79111, "name": "Dispensing Calibration Factor - B1", "macro": "EEP_INGR_CALIBRATION_FACTOR_1", "description": "Ingredient calibration factor (Number of pulses per fluid oz)", "address": 388, "boardName": "Pump Board 7", "boardId": 7, "paramType": "DWORD", "defaultParam": "48600", "currentValue": "48600", "newValue": null, "minParam": "9720", "maxParam": "194400", "machineId": 103, "isPrivate": true}, {"id": 79112, "name": "Dispensing Calibration Factor - B2", "macro": "EEP_INGR_CALIBRATION_FACTOR_3", "description": "Ingredient calibration factor (Number of pulses per fluid oz)", "address": 396, "boardName": "Pump Board 7", "boardId": 7, "paramType": "DWORD", "defaultParam": "48600", "currentValue": "48600", "newValue": null, "minParam": "9720", "maxParam": "194400", "machineId": 103, "isPrivate": true}, {"id": 79113, "name": "Dispensing Calibration Factor - B3", "macro": "EEP_INGR_CALIBRATION_FACTOR_1", "description": "Ingredient calibration factor (Number of pulses per fluid oz)", "address": 388, "boardName": "Pump Board 8", "boardId": 8, "paramType": "DWORD", "defaultParam": "48600", "currentValue": "48600", "newValue": null, "minParam": "9720", "maxParam": "194400", "machineId": 103, "isPrivate": true}, {"id": 79114, "name": "Dispensing Calibration Factor - B4", "macro": "EEP_INGR_CALIBRATION_FACTOR_3", "description": "Ingredient calibration factor (Number of pulses per fluid oz)", "address": 396, "boardName": "Pump Board 8", "boardId": 8, "paramType": "DWORD", "defaultParam": "48600", "currentValue": "48600", "newValue": null, "minParam": "9720", "maxParam": "194400", "machineId": 103, "isPrivate": true}, {"id": 79115, "name": "Dispensing Calibration Factor - B5", "macro": "EEP_INGR_CALIBRATION_FACTOR_1", "description": "Ingredient calibration factor (Number of pulses per fluid oz)", "address": 388, "boardName": "Pump Board 9", "boardId": 9, "paramType": "DWORD", "defaultParam": "48600", "currentValue": "48600", "newValue": null, "minParam": "9720", "maxParam": "194400", "machineId": 103, "isPrivate": true}, {"id": 79116, "name": "Dispensing Calibration Factor - B6", "macro": "EEP_INGR_CALIBRATION_FACTOR_3", "description": "Ingredient calibration factor (Number of pulses per fluid oz)", "address": 396, "boardName": "Pump Board 9", "boardId": 9, "paramType": "DWORD", "defaultParam": "48600", "currentValue": "48600", "newValue": "0", "minParam": "9720", "maxParam": "194400", "machineId": 103, "isPrivate": true}, {"id": 79117, "name": "Dispensing Calibration Factor - C1", "macro": "EEP_INGR_CALIBRATION_FACTOR_0", "description": "Ingredient calibration factor (Number of pulses per fluid oz)", "address": 384, "boardName": "Pump Board 0", "boardId": 0, "paramType": "DWORD", "defaultParam": "24300", "currentValue": "19040", "newValue": null, "minParam": "9720", "maxParam": "194400", "machineId": 103, "isPrivate": true}, {"id": 79118, "name": "Dispensing Calibration Factor - C2", "macro": "EEP_INGR_CALIBRATION_FACTOR_0", "description": "Ingredient calibration factor (Number of pulses per fluid oz)", "address": 384, "boardName": "Pump Board 1", "boardId": 1, "paramType": "DWORD", "defaultParam": "48600", "currentValue": "48600", "newValue": "48600", "minParam": "9720", "maxParam": "194400", "machineId": 103, "isPrivate": true}, {"id": 79119, "name": "Dispensing Calibration Factor - C3", "macro": "EEP_INGR_CALIBRATION_FACTOR_0", "description": "Ingredient calibration factor (Number of pulses per fluid oz)", "address": 384, "boardName": "Pump Board 2", "boardId": 2, "paramType": "DWORD", "defaultParam": "48600", "currentValue": "48600", "newValue": null, "minParam": "9720", "maxParam": "194400", "machineId": 103, "isPrivate": true}, {"id": 79120, "name": "Dispensing Calibration Factor - C4", "macro": "EEP_INGR_CALIBRATION_FACTOR_0", "description": "Ingredient calibration factor (Number of pulses per fluid oz)", "address": 384, "boardName": "Pump Board 3", "boardId": 3, "paramType": "DWORD", "defaultParam": "48600", "currentValue": "48600", "newValue": null, "minParam": "9720", "maxParam": "194400", "machineId": 103, "isPrivate": true}, {"id": 79121, "name": "Dispensing Calibration Factor - C5", "macro": "EEP_INGR_CALIBRATION_FACTOR_0", "description": "Ingredient calibration factor (Number of pulses per fluid oz)", "address": 384, "boardName": "Pump Board 4", "boardId": 4, "paramType": "DWORD", "defaultParam": "48600", "currentValue": "48600", "newValue": null, "minParam": "9720", "maxParam": "194400", "machineId": 103, "isPrivate": true}, {"id": 79122, "name": "Dispensing Calibration Factor - C6", "macro": "EEP_INGR_CALIBRATION_FACTOR_0", "description": "Ingredient calibration factor (Number of pulses per fluid oz)", "address": 384, "boardName": "Pump Board 5", "boardId": 5, "paramType": "DWORD", "defaultParam": "48600", "currentValue": "48600", "newValue": "48600", "minParam": "9720", "maxParam": "194400", "machineId": 103, "isPrivate": true}, {"id": 79123, "name": "Dispensing Calibration Factor - D1", "macro": "EEP_INGR_CALIBRATION_FACTOR_1", "description": "Ingredient calibration factor (Number of pulses per fluid oz)", "address": 388, "boardName": "Pump Board 0", "boardId": 0, "paramType": "DWORD", "defaultParam": "24300", "currentValue": "27000", "newValue": null, "minParam": "9720", "maxParam": "194400", "machineId": 103, "isPrivate": true}, {"id": 79124, "name": "Dispensing Calibration Factor - D2", "macro": "EEP_INGR_CALIBRATION_FACTOR_1", "description": "Ingredient calibration factor (Number of pulses per fluid oz)", "address": 388, "boardName": "Pump Board 1", "boardId": 1, "paramType": "DWORD", "defaultParam": "48600", "currentValue": "48600", "newValue": null, "minParam": "9720", "maxParam": "194400", "machineId": 103, "isPrivate": true}, {"id": 79125, "name": "Dispensing Calibration Factor - D3", "macro": "EEP_INGR_CALIBRATION_FACTOR_1", "description": "Ingredient calibration factor (Number of pulses per fluid oz)", "address": 388, "boardName": "Pump Board 2", "boardId": 2, "paramType": "DWORD", "defaultParam": "48600", "currentValue": "48600", "newValue": "0", "minParam": "9720", "maxParam": "194400", "machineId": 103, "isPrivate": true}, {"id": 79126, "name": "Dispensing Calibration Factor - D4", "macro": "EEP_INGR_CALIBRATION_FACTOR_1", "description": "Ingredient calibration factor (Number of pulses per fluid oz)", "address": 388, "boardName": "Pump Board 3", "boardId": 3, "paramType": "DWORD", "defaultParam": "48600", "currentValue": "48600", "newValue": "48600", "minParam": "9720", "maxParam": "194400", "machineId": 103, "isPrivate": true}, {"id": 79127, "name": "Dispensing Calibration Factor - D5", "macro": "EEP_INGR_CALIBRATION_FACTOR_1", "description": "Ingredient calibration factor (Number of pulses per fluid oz)", "address": 388, "boardName": "Pump Board 4", "boardId": 4, "paramType": "DWORD", "defaultParam": "48600", "currentValue": "48600", "newValue": "0", "minParam": "9720", "maxParam": "194400", "machineId": 103, "isPrivate": true}, {"id": 79128, "name": "Dispensing Calibration Factor - D6", "macro": "EEP_INGR_CALIBRATION_FACTOR_1", "description": "Ingredient calibration factor (Number of pulses per fluid oz)", "address": 388, "boardName": "Pump Board 5", "boardId": 5, "paramType": "DWORD", "defaultParam": "48600", "currentValue": "57160", "newValue": "0", "minParam": "9720", "maxParam": "194400", "machineId": 103, "isPrivate": true}, {"id": 79129, "name": "Dispensing Calibration Factor - E1", "macro": "EEP_INGR_CALIBRATION_FACTOR_2", "description": "Ingredient calibration factor (Number of pulses per fluid oz)", "address": 392, "boardName": "Pump Board 0", "boardId": 0, "paramType": "DWORD", "defaultParam": "24300", "currentValue": "26260", "newValue": null, "minParam": "9720", "maxParam": "194400", "machineId": 103, "isPrivate": true}, {"id": 79130, "name": "Dispensing Calibration Factor - E2", "macro": "EEP_INGR_CALIBRATION_FACTOR_2", "description": "Ingredient calibration factor (Number of pulses per fluid oz)", "address": 392, "boardName": "Pump Board 1", "boardId": 1, "paramType": "DWORD", "defaultParam": "48600", "currentValue": "48600", "newValue": null, "minParam": "9720", "maxParam": "194400", "machineId": 103, "isPrivate": true}, {"id": 79131, "name": "Dispensing Calibration Factor - E3", "macro": "EEP_INGR_CALIBRATION_FACTOR_2", "description": "Ingredient calibration factor (Number of pulses per fluid oz)", "address": 392, "boardName": "Pump Board 2", "boardId": 2, "paramType": "DWORD", "defaultParam": "48600", "currentValue": "48600", "newValue": "0", "minParam": "9720", "maxParam": "194400", "machineId": 103, "isPrivate": true}, {"id": 79132, "name": "Dispensing Calibration Factor - E4", "macro": "EEP_INGR_CALIBRATION_FACTOR_2", "description": "Ingredient calibration factor (Number of pulses per fluid oz)", "address": 392, "boardName": "Pump Board 3", "boardId": 3, "paramType": "DWORD", "defaultParam": "48600", "currentValue": "48600", "newValue": null, "minParam": "9720", "maxParam": "194400", "machineId": 103, "isPrivate": true}, {"id": 79133, "name": "Dispensing Calibration Factor - E5", "macro": "EEP_INGR_CALIBRATION_FACTOR_2", "description": "Ingredient calibration factor (Number of pulses per fluid oz)", "address": 392, "boardName": "Pump Board 4", "boardId": 4, "paramType": "DWORD", "defaultParam": "48600", "currentValue": "48600", "newValue": null, "minParam": "9720", "maxParam": "194400", "machineId": 103, "isPrivate": true}, {"id": 79134, "name": "Dispensing Calibration Factor - E6", "macro": "EEP_INGR_CALIBRATION_FACTOR_2", "description": "Ingredient calibration factor (Number of pulses per fluid oz)", "address": 392, "boardName": "Pump Board 5", "boardId": 5, "paramType": "DWORD", "defaultParam": "48600", "currentValue": "48600", "newValue": "0", "minParam": "9720", "maxParam": "194400", "machineId": 103, "isPrivate": true}, {"id": 79135, "name": "Dispensing Calibration Factor - F1", "macro": "EEP_INGR_CALIBRATION_FACTOR_3", "description": "Ingredient calibration factor (Number of pulses per fluid oz)", "address": 396, "boardName": "Pump Board 0", "boardId": 0, "paramType": "DWORD", "defaultParam": "24300", "currentValue": "26260", "newValue": "0", "minParam": "9720", "maxParam": "194400", "machineId": 103, "isPrivate": true}, {"id": 79136, "name": "Dispensing Calibration Factor - F2", "macro": "EEP_INGR_CALIBRATION_FACTOR_3", "description": "Ingredient calibration factor (Number of pulses per fluid oz)", "address": 396, "boardName": "Pump Board 1", "boardId": 1, "paramType": "DWORD", "defaultParam": "48600", "currentValue": "48600", "newValue": null, "minParam": "9720", "maxParam": "194400", "machineId": 103, "isPrivate": true}, {"id": 79137, "name": "Dispensing Calibration Factor - F3", "macro": "EEP_INGR_CALIBRATION_FACTOR_3", "description": "Ingredient calibration factor (Number of pulses per fluid oz)", "address": 396, "boardName": "Pump Board 2", "boardId": 2, "paramType": "DWORD", "defaultParam": "48600", "currentValue": "48600", "newValue": "0", "minParam": "9720", "maxParam": "194400", "machineId": 103, "isPrivate": true}, {"id": 79138, "name": "Dispensing Calibration Factor - F4", "macro": "EEP_INGR_CALIBRATION_FACTOR_3", "description": "Ingredient calibration factor (Number of pulses per fluid oz)", "address": 396, "boardName": "Pump Board 3", "boardId": 3, "paramType": "DWORD", "defaultParam": "48600", "currentValue": "48600", "newValue": null, "minParam": "9720", "maxParam": "194400", "machineId": 103, "isPrivate": true}, {"id": 79139, "name": "Dispensing Calibration Factor - F5", "macro": "EEP_INGR_CALIBRATION_FACTOR_3", "description": "Ingredient calibration factor (Number of pulses per fluid oz)", "address": 396, "boardName": "Pump Board 4", "boardId": 4, "paramType": "DWORD", "defaultParam": "48600", "currentValue": "48600", "newValue": "0", "minParam": "9720", "maxParam": "194400", "machineId": 103, "isPrivate": true}, {"id": 79140, "name": "Dispensing Calibration Factor - F6", "macro": "EEP_INGR_CALIBRATION_FACTOR_3", "description": "Ingredient calibration factor (Number of pulses per fluid oz)", "address": 396, "boardName": "Pump Board 5", "boardId": 5, "paramType": "DWORD", "defaultParam": "48600", "currentValue": "48600", "newValue": null, "minParam": "9720", "maxParam": "194400", "machineId": 103, "isPrivate": true}, {"id": 79141, "name": "Enable Bottom Temperature Sensor", "macro": "EEP_ENABLE_SENSOR_BOT", "description": "Enable the bottom temperature sensor reading to toggle the compressor ON and OFF. Set to false if the bottom temperature sensor is not working properly.", "address": 232, "boardName": "Cooling", "boardId": 576, "paramType": "BOOLEAN", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 103, "isPrivate": false}, {"id": 79142, "name": "Enable Top Temperature Sensor", "macro": "EEP_ENABLE_SENSOR_TOP", "description": "Enable the top temperature sensor reading to toggle the compressor ON and OFF. Set to false if the top temperature sensor is not working properly.", "address": 233, "boardName": "Cooling", "boardId": 576, "paramType": "BOOLEAN", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 103, "isPrivate": false}, {"id": 79143, "name": "Ingredient Line Status - A1", "macro": "EEP_INGR_LINE_STATUS_0", "description": "Bitfield for current ingredient prime status of pumped ingredients", "address": 432, "boardName": "Pump Board 7", "boardId": 7, "paramType": "BYTE", "defaultParam": "0", "currentValue": "5", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 103, "isPrivate": true}, {"id": 79144, "name": "Ingredient Line Status - A2", "macro": "EEP_INGR_LINE_STATUS_2", "description": "Bitfield for current ingredient prime status of pumped ingredients", "address": 434, "boardName": "Pump Board 7", "boardId": 7, "paramType": "BYTE", "defaultParam": "0", "currentValue": "5", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 103, "isPrivate": true}, {"id": 79145, "name": "Ingredient Line Status - A3", "macro": "EEP_INGR_LINE_STATUS_0", "description": "Bitfield for current ingredient prime status of pumped ingredients", "address": 432, "boardName": "Pump Board 8", "boardId": 8, "paramType": "BYTE", "defaultParam": "0", "currentValue": "5", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 103, "isPrivate": true}, {"id": 79146, "name": "Ingredient Line Status - A4", "macro": "EEP_INGR_LINE_STATUS_2", "description": "Bitfield for current ingredient prime status of pumped ingredients", "address": 434, "boardName": "Pump Board 8", "boardId": 8, "paramType": "BYTE", "defaultParam": "0", "currentValue": "5", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 103, "isPrivate": true}, {"id": 79147, "name": "Ingredient Line Status - A5", "macro": "EEP_INGR_LINE_STATUS_0", "description": "Bitfield for current ingredient prime status of pumped ingredients", "address": 432, "boardName": "Pump Board 9", "boardId": 9, "paramType": "BYTE", "defaultParam": "0", "currentValue": "9", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 103, "isPrivate": true}, {"id": 79148, "name": "Ingredient Line Status - B2", "macro": "EEP_INGR_LINE_STATUS_3", "description": "Bitfield for current ingredient prime status of pumped ingredients", "address": 435, "boardName": "Pump Board 7", "boardId": 7, "paramType": "BYTE", "defaultParam": "0", "currentValue": "5", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 103, "isPrivate": true}, {"id": 79149, "name": "Ingredient Line Status - B3", "macro": "EEP_INGR_LINE_STATUS_1", "description": "Bitfield for current ingredient prime status of pumped ingredients", "address": 433, "boardName": "Pump Board 8", "boardId": 8, "paramType": "BYTE", "defaultParam": "0", "currentValue": "5", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 103, "isPrivate": true}, {"id": 79150, "name": "Ingredient Line Status - B4", "macro": "EEP_INGR_LINE_STATUS_3", "description": "Bitfield for current ingredient prime status of pumped ingredients", "address": 435, "boardName": "Pump Board 8", "boardId": 8, "paramType": "BYTE", "defaultParam": "0", "currentValue": "5", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 103, "isPrivate": true}, {"id": 79151, "name": "Ingredient Line Status - B5", "macro": "EEP_INGR_LINE_STATUS_1", "description": "Bitfield for current ingredient prime status of pumped ingredients", "address": 433, "boardName": "Pump Board 9", "boardId": 9, "paramType": "BYTE", "defaultParam": "0", "currentValue": "5", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 103, "isPrivate": true}, {"id": 79152, "name": "Ingredient Line Status - B6", "macro": "EEP_INGR_LINE_STATUS_3", "description": "Bitfield for current ingredient prime status of pumped ingredients", "address": 435, "boardName": "Pump Board 9", "boardId": 9, "paramType": "BYTE", "defaultParam": "0", "currentValue": "5", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 103, "isPrivate": true}, {"id": 79153, "name": "Ingredient Line Status - C1", "macro": "EEP_INGR_LINE_STATUS_0", "description": "Bitfield for current ingredient prime status of pumped ingredients", "address": 432, "boardName": "Pump Board 0", "boardId": 0, "paramType": "BYTE", "defaultParam": "0", "currentValue": "5", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 103, "isPrivate": true}, {"id": 79154, "name": "Ingredient Line Status - C2", "macro": "EEP_INGR_LINE_STATUS_0", "description": "Bitfield for current ingredient prime status of pumped ingredients", "address": 432, "boardName": "Pump Board 1", "boardId": 1, "paramType": "BYTE", "defaultParam": "0", "currentValue": "9", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 103, "isPrivate": true}, {"id": 79155, "name": "Ingredient Line Status - C3", "macro": "EEP_INGR_LINE_STATUS_0", "description": "Bitfield for current ingredient prime status of pumped ingredients", "address": 432, "boardName": "Pump Board 2", "boardId": 2, "paramType": "BYTE", "defaultParam": "0", "currentValue": "5", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 103, "isPrivate": true}, {"id": 79156, "name": "Ingredient Line Status - C4", "macro": "EEP_INGR_LINE_STATUS_0", "description": "Bitfield for current ingredient prime status of pumped ingredients", "address": 432, "boardName": "Pump Board 3", "boardId": 3, "paramType": "BYTE", "defaultParam": "0", "currentValue": "5", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 103, "isPrivate": true}, {"id": 79157, "name": "Ingredient Line Status - C5", "macro": "EEP_INGR_LINE_STATUS_0", "description": "Bitfield for current ingredient prime status of pumped ingredients", "address": 432, "boardName": "Pump Board 4", "boardId": 4, "paramType": "BYTE", "defaultParam": "0", "currentValue": "5", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 103, "isPrivate": true}, {"id": 79158, "name": "Ingredient Line Status - C6", "macro": "EEP_INGR_LINE_STATUS_0", "description": "Bitfield for current ingredient prime status of pumped ingredients", "address": 432, "boardName": "Pump Board 5", "boardId": 5, "paramType": "BYTE", "defaultParam": "0", "currentValue": "5", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 103, "isPrivate": true}, {"id": 79159, "name": "Ingredient Line Status - D1", "macro": "EEP_INGR_LINE_STATUS_1", "description": "Bitfield for current ingredient prime status of pumped ingredients", "address": 433, "boardName": "Pump Board 0", "boardId": 0, "paramType": "BYTE", "defaultParam": "0", "currentValue": "5", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 103, "isPrivate": true}, {"id": 79160, "name": "Ingredient Line Status - D2", "macro": "EEP_INGR_LINE_STATUS_1", "description": "Bitfield for current ingredient prime status of pumped ingredients", "address": 433, "boardName": "Pump Board 1", "boardId": 1, "paramType": "BYTE", "defaultParam": "0", "currentValue": "5", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 103, "isPrivate": true}, {"id": 79161, "name": "Ingredient Line Status - D3", "macro": "EEP_INGR_LINE_STATUS_1", "description": "Bitfield for current ingredient prime status of pumped ingredients", "address": 433, "boardName": "Pump Board 2", "boardId": 2, "paramType": "BYTE", "defaultParam": "0", "currentValue": "9", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 103, "isPrivate": true}, {"id": 79162, "name": "Ingredient Line Status - D4", "macro": "EEP_INGR_LINE_STATUS_1", "description": "Bitfield for current ingredient prime status of pumped ingredients", "address": 433, "boardName": "Pump Board 3", "boardId": 3, "paramType": "BYTE", "defaultParam": "0", "currentValue": "9", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 103, "isPrivate": true}, {"id": 79163, "name": "Ingredient Line Status - D5", "macro": "EEP_INGR_LINE_STATUS_1", "description": "Bitfield for current ingredient prime status of pumped ingredients", "address": 433, "boardName": "Pump Board 4", "boardId": 4, "paramType": "BYTE", "defaultParam": "0", "currentValue": "5", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 103, "isPrivate": true}, {"id": 79164, "name": "Ingredient Line Status - D6", "macro": "EEP_INGR_LINE_STATUS_1", "description": "Bitfield for current ingredient prime status of pumped ingredients", "address": 433, "boardName": "Pump Board 5", "boardId": 5, "paramType": "BYTE", "defaultParam": "0", "currentValue": "5", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 103, "isPrivate": true}, {"id": 79165, "name": "Ingredient Line Status - E1", "macro": "EEP_INGR_LINE_STATUS_2", "description": "Bitfield for current ingredient prime status of pumped ingredients", "address": 434, "boardName": "Pump Board 0", "boardId": 0, "paramType": "BYTE", "defaultParam": "0", "currentValue": "9", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 103, "isPrivate": true}, {"id": 79166, "name": "Ingredient Line Status - E2", "macro": "EEP_INGR_LINE_STATUS_2", "description": "Bitfield for current ingredient prime status of pumped ingredients", "address": 434, "boardName": "Pump Board 1", "boardId": 1, "paramType": "BYTE", "defaultParam": "0", "currentValue": "5", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 103, "isPrivate": true}, {"id": 79167, "name": "Ingredient Line Status - E3", "macro": "EEP_INGR_LINE_STATUS_2", "description": "Bitfield for current ingredient prime status of pumped ingredients", "address": 434, "boardName": "Pump Board 2", "boardId": 2, "paramType": "BYTE", "defaultParam": "0", "currentValue": "5", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 103, "isPrivate": true}, {"id": 79168, "name": "Ingredient Line Status - E4", "macro": "EEP_INGR_LINE_STATUS_2", "description": "Bitfield for current ingredient prime status of pumped ingredients", "address": 434, "boardName": "Pump Board 3", "boardId": 3, "paramType": "BYTE", "defaultParam": "0", "currentValue": "9", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 103, "isPrivate": true}, {"id": 79169, "name": "Ingredient Line Status - E5", "macro": "EEP_INGR_LINE_STATUS_2", "description": "Bitfield for current ingredient prime status of pumped ingredients", "address": 434, "boardName": "Pump Board 4", "boardId": 4, "paramType": "BYTE", "defaultParam": "0", "currentValue": "5", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 103, "isPrivate": true}, {"id": 79170, "name": "Ingredient Line Status - E6", "macro": "EEP_INGR_LINE_STATUS_2", "description": "Bitfield for current ingredient prime status of pumped ingredients", "address": 434, "boardName": "Pump Board 5", "boardId": 5, "paramType": "BYTE", "defaultParam": "0", "currentValue": "9", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 103, "isPrivate": true}, {"id": 79171, "name": "Ingredient Line Status - F1", "macro": "EEP_INGR_LINE_STATUS_3", "description": "Bitfield for current ingredient prime status of pumped ingredients", "address": 435, "boardName": "Pump Board 0", "boardId": 0, "paramType": "BYTE", "defaultParam": "0", "currentValue": "5", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 103, "isPrivate": true}, {"id": 79172, "name": "Ingredient Line Status - F2", "macro": "EEP_INGR_LINE_STATUS_3", "description": "Bitfield for current ingredient prime status of pumped ingredients", "address": 435, "boardName": "Pump Board 1", "boardId": 1, "paramType": "BYTE", "defaultParam": "0", "currentValue": "5", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 103, "isPrivate": true}, {"id": 79173, "name": "Ingredient Line Status - F3", "macro": "EEP_INGR_LINE_STATUS_3", "description": "Bitfield for current ingredient prime status of pumped ingredients", "address": 435, "boardName": "Pump Board 2", "boardId": 2, "paramType": "BYTE", "defaultParam": "0", "currentValue": "5", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 103, "isPrivate": true}, {"id": 79174, "name": "Ingredient Line Status - F4", "macro": "EEP_INGR_LINE_STATUS_3", "description": "Bitfield for current ingredient prime status of pumped ingredients", "address": 435, "boardName": "Pump Board 3", "boardId": 3, "paramType": "BYTE", "defaultParam": "0", "currentValue": "5", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 103, "isPrivate": true}, {"id": 79175, "name": "Ingredient Line Status - F5", "macro": "EEP_INGR_LINE_STATUS_3", "description": "Bitfield for current ingredient prime status of pumped ingredients", "address": 435, "boardName": "Pump Board 4", "boardId": 4, "paramType": "BYTE", "defaultParam": "0", "currentValue": "9", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 103, "isPrivate": true}, {"id": 79176, "name": "Ingredient Line Status - F6", "macro": "EEP_INGR_LINE_STATUS_3", "description": "Bitfield for current ingredient prime status of pumped ingredients", "address": 435, "boardName": "Pump Board 5", "boardId": 5, "paramType": "BYTE", "defaultParam": "0", "currentValue": "5", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 103, "isPrivate": true}, {"id": 79177, "name": "Ingredient Line Status -A6", "macro": "EEP_INGR_LINE_STATUS_2", "description": "Bitfield for current ingredient prime status of pumped ingredients", "address": 434, "boardName": "Pump Board 9", "boardId": 9, "paramType": "BYTE", "defaultParam": "0", "currentValue": "9", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 103, "isPrivate": true}, {"id": 79178, "name": "Ingredient Line Status -B1", "macro": "EEP_INGR_LINE_STATUS_1", "description": "Bitfield for current ingredient prime status of pumped ingredients", "address": 433, "boardName": "Pump Board 7", "boardId": 7, "paramType": "BYTE", "defaultParam": "0", "currentValue": "5", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 103, "isPrivate": true}, {"id": 79179, "name": "Ingredient Mixing Enable - C1", "macro": "EEP_MIXING_ENABLE_0", "description": "Enable ingredient mixing for A1", "address": 96, "boardName": "Master", "boardId": 65535, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 103, "isPrivate": true}, {"id": 79180, "name": "Ingredient Mixing Enable - C2", "macro": "EEP_MIXING_ENABLE_4", "description": "Enable ingredient mixing for B1", "address": 100, "boardName": "Master", "boardId": 65535, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 103, "isPrivate": true}, {"id": 79181, "name": "Ingredient Mixing Enable - C3", "macro": "EEP_MIXING_ENABLE_7", "description": "Enable ingredient mixing for C1", "address": 104, "boardName": "Master", "boardId": 65535, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 103, "isPrivate": true}, {"id": 79182, "name": "Ingredient Mixing Enable - C4", "macro": "EEP_MIXING_ENABLE_12", "description": "Enable ingredient mixing for D1", "address": 108, "boardName": "Master", "boardId": 65535, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 103, "isPrivate": true}, {"id": 79183, "name": "Ingredient Mixing Enable - C5", "macro": "EEP_MIXING_ENABLE_16", "description": "Enable ingredient mixing for E1", "address": 112, "boardName": "Master", "boardId": 65535, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 103, "isPrivate": true}, {"id": 79184, "name": "Ingredient Mixing Enable - C6", "macro": "EEP_MIXING_ENABLE_20", "description": "Enable ingredient mixing for F1", "address": 116, "boardName": "Master", "boardId": 65535, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 103, "isPrivate": true}, {"id": 79185, "name": "Ingredient Mixing Enable - D1", "macro": "EEP_MIXING_ENABLE_1", "description": "Enable ingredient mixing for A2", "address": 97, "boardName": "Master", "boardId": 65535, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 103, "isPrivate": true}, {"id": 79186, "name": "Ingredient Mixing Enable - D2", "macro": "EEP_MIXING_ENABLE_5", "description": "Enable ingredient mixing for B2", "address": 101, "boardName": "Master", "boardId": 65535, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 103, "isPrivate": true}, {"id": 79187, "name": "Ingredient Mixing Enable - D3", "macro": "EEP_MIXING_ENABLE_9", "description": "Enable ingredient mixing for C2", "address": 105, "boardName": "Master", "boardId": 65535, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 103, "isPrivate": true}, {"id": 79188, "name": "Ingredient Mixing Enable - D4", "macro": "EEP_MIXING_ENABLE_13", "description": "Enable ingredient mixing for D2", "address": 109, "boardName": "Master", "boardId": 65535, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 103, "isPrivate": true}, {"id": 79189, "name": "Ingredient Mixing Enable - D5", "macro": "EEP_MIXING_ENABLE_17", "description": "Enable ingredient mixing for E2", "address": 113, "boardName": "Master", "boardId": 65535, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 103, "isPrivate": true}, {"id": 79190, "name": "Ingredient Mixing Enable - D6", "macro": "EEP_MIXING_ENABLE_21", "description": "Enable ingredient mixing for F2", "address": 117, "boardName": "Master", "boardId": 65535, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 103, "isPrivate": true}, {"id": 79191, "name": "Ingredient Mixing Enable - E1", "macro": "EEP_MIXING_ENABLE_2", "description": "Enable ingredient mixing for A3", "address": 98, "boardName": "Master", "boardId": 65535, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 103, "isPrivate": true}, {"id": 79192, "name": "Ingredient Mixing Enable - E2", "macro": "EEP_MIXING_ENABLE_6", "description": "Enable ingredient mixing for B3", "address": 102, "boardName": "Master", "boardId": 65535, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 103, "isPrivate": true}, {"id": 79193, "name": "Ingredient Mixing Enable - E3", "macro": "EEP_MIXING_ENABLE_10", "description": "Enable ingredient mixing for C3", "address": 106, "boardName": "Master", "boardId": 65535, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 103, "isPrivate": true}, {"id": 79194, "name": "Ingredient Mixing Enable - E4", "macro": "EEP_MIXING_ENABLE_14", "description": "Enable ingredient mixing for D3", "address": 110, "boardName": "Master", "boardId": 65535, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 103, "isPrivate": true}, {"id": 79195, "name": "Ingredient Mixing Enable - E5", "macro": "EEP_MIXING_ENABLE_18", "description": "Enable ingredient mixing for E3", "address": 114, "boardName": "Master", "boardId": 65535, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 103, "isPrivate": true}, {"id": 79196, "name": "Ingredient Mixing Enable - E6", "macro": "EEP_MIXING_ENABLE_22", "description": "Enable ingredient mixing for F3", "address": 118, "boardName": "Master", "boardId": 65535, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 103, "isPrivate": true}, {"id": 79197, "name": "Ingredient Mixing Enable - F1", "macro": "EEP_MIXING_ENABLE_3", "description": "Enable ingredient mixing for A4", "address": 99, "boardName": "Master", "boardId": 65535, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 103, "isPrivate": true}, {"id": 79198, "name": "Ingredient Mixing Enable - F2", "macro": "EEP_MIXING_ENABLE_7", "description": "Enable ingredient mixing for B4", "address": 103, "boardName": "Master", "boardId": 65535, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 103, "isPrivate": true}, {"id": 79199, "name": "Ingredient Mixing Enable - F3", "macro": "EEP_MIXING_ENABLE_11", "description": "Enable ingredient mixing for C4", "address": 107, "boardName": "Master", "boardId": 65535, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 103, "isPrivate": true}, {"id": 79200, "name": "Ingredient Mixing Enable - F4", "macro": "EEP_MIXING_ENABLE_15", "description": "Enable ingredient mixing for D4", "address": 111, "boardName": "Master", "boardId": 65535, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 103, "isPrivate": true}, {"id": 79201, "name": "Ingredient Mixing Enable - F5", "macro": "EEP_MIXING_ENABLE_19", "description": "Enable ingredient mixing for E4", "address": 115, "boardName": "Master", "boardId": 65535, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 103, "isPrivate": true}, {"id": 79202, "name": "Ingredient Mixing Enable - F6", "macro": "EEP_MIXING_ENABLE_23", "description": "Enable ingredient mixing for F4", "address": 119, "boardName": "Master", "boardId": 65535, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 103, "isPrivate": true}, {"id": 79203, "name": "Ingredient Mixing Enable - G1", "macro": "EEP_MIXING_ENABLE_24", "description": "Enable ingredient mixing for G1", "address": 120, "boardName": "Master", "boardId": 65535, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 103, "isPrivate": true}, {"id": 79204, "name": "Ingredient Mixing Enable - G2", "macro": "EEP_MIXING_ENABLE_25", "description": "Enable ingredient mixing for G2", "address": 121, "boardName": "Master", "boardId": 65535, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 103, "isPrivate": true}, {"id": 79205, "name": "Ingredient Mixing Enable - G3", "macro": "EEP_MIXING_ENABLE_26", "description": "Enable ingredient mixing for G3", "address": 122, "boardName": "Master", "boardId": 65535, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 103, "isPrivate": true}, {"id": 79206, "name": "Ingredient Mixing Enable - G4", "macro": "EEP_MIXING_ENABLE_27", "description": "Enable ingredient mixing for G4", "address": 123, "boardName": "Master", "boardId": 65535, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 103, "isPrivate": true}, {"id": 79207, "name": "Ingredient Sensor Active Threshold - A1, A2, B1, B2", "macro": "EEP_LEVEL_THRESHOLD", "description": "Ingredient level (expressed in 1/20 fl. oz. increments) below which runout detection becomes active for ingredients A1, A2, B1, B2", "address": 256, "boardName": "Pump Board 7", "boardId": 7, "paramType": "WORD", "defaultParam": "50", "currentValue": "50", "newValue": null, "minParam": "10", "maxParam": "3500", "machineId": 103, "isPrivate": false}, {"id": 79208, "name": "Ingredient Sensor Active Threshold - A3, A4, B3, B4", "macro": "EEP_LEVEL_THRESHOLD", "description": "Ingredient level (expressed in 1/20 fl. oz. increments) below which runout detection becomes active for ingredients A3, A4, B3, B4", "address": 256, "boardName": "Pump Board 8", "boardId": 8, "paramType": "WORD", "defaultParam": "50", "currentValue": "50", "newValue": null, "minParam": "10", "maxParam": "3500", "machineId": 103, "isPrivate": false}, {"id": 79209, "name": "Ingredient Sensor Active Threshold - A5, A6, B5, B6", "macro": "EEP_LEVEL_THRESHOLD", "description": "Ingredient level (expressed in 1/20 fl. oz. increments) below which runout detection becomes active for ingredients A5, A6, B5, B6", "address": 256, "boardName": "Pump Board 9", "boardId": 9, "paramType": "WORD", "defaultParam": "50", "currentValue": "50", "newValue": null, "minParam": "10", "maxParam": "3500", "machineId": 103, "isPrivate": false}, {"id": 79210, "name": "Ingredient Sensor Active Threshold - C1,D1,E1,F1", "macro": "EEP_LEVEL_THRESHOLD", "description": "Ingredient level (expressed in 1/20 fl. oz. increments) below which runout detection becomes active for ingredients C1,D1,E1,F1", "address": 256, "boardName": "Pump Board 0", "boardId": 0, "paramType": "WORD", "defaultParam": "50", "currentValue": "50", "newValue": null, "minParam": "10", "maxParam": "3500", "machineId": 103, "isPrivate": false}, {"id": 79211, "name": "Ingredient Sensor Active Threshold - C2, E2, D2, F2", "macro": "EEP_LEVEL_THRESHOLD", "description": "Ingredient level (expressed in 1/20 fl. oz. increments) below which runout detection becomes active for ingredients C2, E2, D2, F2", "address": 256, "boardName": "Pump Board 1", "boardId": 1, "paramType": "WORD", "defaultParam": "50", "currentValue": "50", "newValue": null, "minParam": "10", "maxParam": "3500", "machineId": 103, "isPrivate": false}, {"id": 79212, "name": "Ingredient Sensor Active Threshold - C3, D3, E3, F3", "macro": "EEP_LEVEL_THRESHOLD", "description": "Ingredient level (expressed in 1/20 fl. oz. increments) below which runout detection becomes active for ingredients C3, D3, E3, F3", "address": 256, "boardName": "Pump Board 2", "boardId": 2, "paramType": "WORD", "defaultParam": "50", "currentValue": "50", "newValue": null, "minParam": "10", "maxParam": "3500", "machineId": 103, "isPrivate": false}, {"id": 79213, "name": "Ingredient Sensor Active Threshold - C4, D4, E4, F4", "macro": "EEP_LEVEL_THRESHOLD", "description": "Ingredient level (expressed in 1/20 fl. oz. increments) below which runout detection becomes active for ingredients C4, D4, E4, F4", "address": 256, "boardName": "Pump Board 3", "boardId": 3, "paramType": "WORD", "defaultParam": "50", "currentValue": "50", "newValue": null, "minParam": "10", "maxParam": "3500", "machineId": 103, "isPrivate": false}, {"id": 79214, "name": "Ingredient Sensor Active Threshold - C5, D5, E5, F5", "macro": "EEP_LEVEL_THRESHOLD", "description": "Ingredient level (expressed in 1/20 fl. oz. increments) below which runout detection becomes active for ingredients C5, D5, E5, F5", "address": 256, "boardName": "Pump Board 4", "boardId": 4, "paramType": "WORD", "defaultParam": "50", "currentValue": "50", "newValue": null, "minParam": "10", "maxParam": "3500", "machineId": 103, "isPrivate": false}, {"id": 79215, "name": "Ingredient Sensor Active Threshold - C6, D6, E6, F6", "macro": "EEP_LEVEL_THRESHOLD", "description": "Ingredient level (expressed in 1/20 fl. oz. increments) below which runout detection becomes active for ingredients C6, D6, E6, F6", "address": 256, "boardName": "Pump Board 5", "boardId": 5, "paramType": "WORD", "defaultParam": "50", "currentValue": "50", "newValue": null, "minParam": "10", "maxParam": "3500", "machineId": 103, "isPrivate": false}, {"id": 79216, "name": "<PERSON>/<PERSON> Default Ingredient Speed- A1, B1, A2, B2", "macro": "EEP_OZMIN_INGR_SPEED_DEFAULT", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 416, "boardName": "Pump Board 7", "boardId": 7, "paramType": "SINGLE", "defaultParam": "34.5", "currentValue": "15.0", "newValue": null, "minParam": "7.5", "maxParam": "37.5", "machineId": 103, "isPrivate": true}, {"id": 79217, "name": "<PERSON>/<PERSON> De<PERSON>ult Ingredient Speed- A3, B3, A4, B4", "macro": "EEP_OZMIN_INGR_SPEED_DEFAULT", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 416, "boardName": "Pump Board 8", "boardId": 8, "paramType": "SINGLE", "defaultParam": "34.5", "currentValue": "15.0", "newValue": null, "minParam": "7.5", "maxParam": "37.5", "machineId": 103, "isPrivate": true}, {"id": 79218, "name": "<PERSON>/<PERSON> De<PERSON> Ingredient Speed- A5, B5, A6, B6", "macro": "EEP_OZMIN_INGR_SPEED_DEFAULT", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 416, "boardName": "Pump Board 9", "boardId": 9, "paramType": "SINGLE", "defaultParam": "34.5", "currentValue": "15.0", "newValue": null, "minParam": "7.5", "maxParam": "37.5", "machineId": 103, "isPrivate": true}, {"id": 79219, "name": "<PERSON>/<PERSON> De<PERSON>ult Ingredient Speed- C1, D1, E1, F1", "macro": "EEP_OZMIN_INGR_SPEED_DEFAULT", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 416, "boardName": "Pump Board 0", "boardId": 0, "paramType": "SINGLE", "defaultParam": "34.5", "currentValue": "15.0", "newValue": null, "minParam": "7.5", "maxParam": "37.5", "machineId": 103, "isPrivate": true}, {"id": 79220, "name": "<PERSON>/<PERSON> Default Ingredient Speed- C2, D2, E2, F2", "macro": "EEP_OZMIN_INGR_SPEED_DEFAULT", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 416, "boardName": "Pump Board 1", "boardId": 1, "paramType": "SINGLE", "defaultParam": "34.5", "currentValue": "15.0", "newValue": null, "minParam": "7.5", "maxParam": "37.5", "machineId": 103, "isPrivate": true}, {"id": 79221, "name": "<PERSON>/<PERSON> Default Ingredient Speed- C3, D3, E3, F3", "macro": "EEP_OZMIN_INGR_SPEED_DEFAULT", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 416, "boardName": "Pump Board 2", "boardId": 2, "paramType": "SINGLE", "defaultParam": "34.5", "currentValue": "15.0", "newValue": null, "minParam": "7.5", "maxParam": "37.5", "machineId": 103, "isPrivate": true}, {"id": 79222, "name": "<PERSON>/<PERSON> De<PERSON>ult Ingredient Speed- C4, D4, E4, F4", "macro": "EEP_OZMIN_INGR_SPEED_DEFAULT", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 416, "boardName": "Pump Board 3", "boardId": 3, "paramType": "SINGLE", "defaultParam": "34.5", "currentValue": "15.0", "newValue": null, "minParam": "7.5", "maxParam": "37.5", "machineId": 103, "isPrivate": true}, {"id": 79223, "name": "<PERSON>/<PERSON> De<PERSON>ult Ingredient Speed- C5, D5, E5, F5", "macro": "EEP_OZMIN_INGR_SPEED_DEFAULT", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 416, "boardName": "Pump Board 4", "boardId": 4, "paramType": "SINGLE", "defaultParam": "34.5", "currentValue": "15.0", "newValue": null, "minParam": "7.5", "maxParam": "37.5", "machineId": 103, "isPrivate": true}, {"id": 79224, "name": "<PERSON>/<PERSON> De<PERSON>ult Ingredient Speed- C6, D6, E6, F6", "macro": "EEP_OZMIN_INGR_SPEED_DEFAULT", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 416, "boardName": "Pump Board 5", "boardId": 5, "paramType": "SINGLE", "defaultParam": "34.5", "currentValue": "15.0", "newValue": null, "minParam": "7.5", "maxParam": "37.5", "machineId": 103, "isPrivate": true}, {"id": 79225, "name": "Priming Quantity - A1", "macro": "EEP_PRIMING_QTY_0", "description": "Priming quantity (expressed in 1/20 fl. oz. increments) for ingredient A1", "address": 84, "boardName": "Pump Board 7", "boardId": 7, "paramType": "BYTE", "defaultParam": "46", "currentValue": "63", "newValue": null, "minParam": "40", "maxParam": "110", "machineId": 103, "isPrivate": true}, {"id": 79226, "name": "Priming Quantity - A2", "macro": "EEP_PRIMING_QTY_2", "description": "Priming quantity (expressed in 1/20 fl. oz. increments) for ingredient A2", "address": 116, "boardName": "Pump Board 7", "boardId": 7, "paramType": "BYTE", "defaultParam": "56", "currentValue": "64", "newValue": null, "minParam": "40", "maxParam": "110", "machineId": 103, "isPrivate": true}, {"id": 79227, "name": "Priming Quantity - A3", "macro": "EEP_PRIMING_QTY_0", "description": "Priming quantity (expressed in 1/20 fl. oz. increments) for ingredient A3", "address": 84, "boardName": "Pump Board 8", "boardId": 8, "paramType": "BYTE", "defaultParam": "46", "currentValue": "62", "newValue": null, "minParam": "40", "maxParam": "110", "machineId": 103, "isPrivate": true}, {"id": 79228, "name": "Priming Quantity - A4", "macro": "EEP_PRIMING_QTY_2", "description": "Priming quantity (expressed in 1/20 fl. oz. increments) for ingredient A4", "address": 116, "boardName": "Pump Board 8", "boardId": 8, "paramType": "BYTE", "defaultParam": "57", "currentValue": "65", "newValue": null, "minParam": "40", "maxParam": "110", "machineId": 103, "isPrivate": true}, {"id": 79229, "name": "Priming Quantity - A5", "macro": "EEP_PRIMING_QTY_0", "description": "Priming quantity (expressed in 1/20 fl. oz. increments) for ingredient A5", "address": 84, "boardName": "Pump Board 9", "boardId": 9, "paramType": "BYTE", "defaultParam": "50", "currentValue": "43", "newValue": null, "minParam": "40", "maxParam": "110", "machineId": 103, "isPrivate": true}, {"id": 79230, "name": "Priming Quantity - A6", "macro": "EEP_PRIMING_QTY_2", "description": "Priming quantity (expressed in 1/20 fl. oz. increments) for ingredient A6", "address": 116, "boardName": "Pump Board 9", "boardId": 9, "paramType": "BYTE", "defaultParam": "65", "currentValue": "87", "newValue": null, "minParam": "40", "maxParam": "110", "machineId": 103, "isPrivate": true}, {"id": 79231, "name": "Priming Quantity - B1", "macro": "EEP_PRIMING_QTY_1", "description": "Priming quantity (expressed in 1/20 fl. oz. increments) for ingredient B1", "address": 100, "boardName": "Pump Board 7", "boardId": 7, "paramType": "BYTE", "defaultParam": "51", "currentValue": "60", "newValue": null, "minParam": "40", "maxParam": "110", "machineId": 103, "isPrivate": true}, {"id": 79232, "name": "Priming Quantity - B2", "macro": "EEP_PRIMING_QTY_3", "description": "Priming quantity (expressed in 1/20 fl. oz. increments) for ingredient B2", "address": 132, "boardName": "Pump Board 7", "boardId": 7, "paramType": "BYTE", "defaultParam": "55", "currentValue": "70", "newValue": null, "minParam": "40", "maxParam": "110", "machineId": 103, "isPrivate": true}, {"id": 79233, "name": "Priming Quantity - B3", "macro": "EEP_PRIMING_QTY_1", "description": "Priming quantity (expressed in 1/20 fl. oz. increments) for ingredient B3", "address": 100, "boardName": "Pump Board 8", "boardId": 8, "paramType": "BYTE", "defaultParam": "51", "currentValue": "70", "newValue": null, "minParam": "40", "maxParam": "110", "machineId": 103, "isPrivate": true}, {"id": 79234, "name": "Priming Quantity - B4", "macro": "EEP_PRIMING_QTY_3", "description": "Priming quantity (expressed in 1/20 fl. oz. increments) for ingredient B4", "address": 132, "boardName": "Pump Board 8", "boardId": 8, "paramType": "BYTE", "defaultParam": "55", "currentValue": "64", "newValue": null, "minParam": "40", "maxParam": "110", "machineId": 103, "isPrivate": true}, {"id": 79235, "name": "Priming Quantity - B5", "macro": "EEP_PRIMING_QTY_1", "description": "Priming quantity (expressed in 1/20 fl. oz. increments) for ingredient B5", "address": 100, "boardName": "Pump Board 9", "boardId": 9, "paramType": "BYTE", "defaultParam": "51", "currentValue": "56", "newValue": null, "minParam": "40", "maxParam": "110", "machineId": 103, "isPrivate": true}, {"id": 79236, "name": "Priming Quantity - B6", "macro": "EEP_PRIMING_QTY_3", "description": "Priming quantity (expressed in 1/20 fl. oz. increments) for ingredient B6", "address": 132, "boardName": "Pump Board 9", "boardId": 9, "paramType": "BYTE", "defaultParam": "58", "currentValue": "70", "newValue": null, "minParam": "40", "maxParam": "110", "machineId": 103, "isPrivate": true}, {"id": 79237, "name": "Priming Quantity - Bib 1", "macro": "EEP_BIB_PRIME_QTY_0", "description": "Priming quantity (expressed in 1/20th oz increments) for ingredient Bib 1", "address": 112, "boardName": "Nozzle", "boardId": 560, "paramType": "WORD", "defaultParam": "40", "currentValue": "65535", "newValue": null, "minParam": "40", "maxParam": "400", "machineId": 103, "isPrivate": true}, {"id": 79238, "name": "Priming Quantity - Bib 2", "macro": "EEP_BIB_PRIME_QTY_1", "description": "Priming quantity (expressed in 1/20th oz increments) for ingredient Bib 2", "address": 114, "boardName": "Nozzle", "boardId": 560, "paramType": "WORD", "defaultParam": "40", "currentValue": "65535", "newValue": null, "minParam": "40", "maxParam": "400", "machineId": 103, "isPrivate": true}, {"id": 79239, "name": "Priming Quantity - Bib 3", "macro": "EEP_BIB_PRIME_QTY_2", "description": "Priming quantity (expressed in 1/20th oz increments) for ingredient Bib 3", "address": 116, "boardName": "Nozzle", "boardId": 560, "paramType": "WORD", "defaultParam": "40", "currentValue": "65535", "newValue": null, "minParam": "40", "maxParam": "400", "machineId": 103, "isPrivate": true}, {"id": 79240, "name": "Priming Quantity - Bib 4", "macro": "EEP_BIB_PRIME_QTY_3", "description": "Priming quantity (expressed in 1/20th oz increments) for ingredient Bib 4", "address": 118, "boardName": "Nozzle", "boardId": 560, "paramType": "WORD", "defaultParam": "40", "currentValue": "65535", "newValue": null, "minParam": "40", "maxParam": "400", "machineId": 103, "isPrivate": true}, {"id": 79241, "name": "Priming Quantity - Bib 5", "macro": "EEP_BIB_PRIME_QTY_0", "description": "Priming quantity (expressed in 1/20th oz increments) for ingredient bib 5", "address": 112, "boardName": "Solenoid", "boardId": 12, "paramType": "WORD", "defaultParam": "40", "currentValue": "40", "newValue": null, "minParam": "40", "maxParam": "400", "machineId": 103, "isPrivate": true}, {"id": 79242, "name": "Priming Quantity - Bib 6", "macro": "EEP_BIB_PRIME_QTY_1", "description": "Priming quantity (expressed in 1/20th oz increments) for ingredient bib 6", "address": 114, "boardName": "Solenoid", "boardId": 12, "paramType": "WORD", "defaultParam": "40", "currentValue": "40", "newValue": null, "minParam": "40", "maxParam": "400", "machineId": 103, "isPrivate": true}, {"id": 79243, "name": "Priming Quantity - Bib 7", "macro": "EEP_BIB_PRIME_QTY_2", "description": "Priming quantity (expressed in 1/20th oz increments) for ingredient bib 7", "address": 116, "boardName": "Solenoid", "boardId": 12, "paramType": "WORD", "defaultParam": "40", "currentValue": "40", "newValue": null, "minParam": "40", "maxParam": "400", "machineId": 103, "isPrivate": true}, {"id": 79244, "name": "Priming Quantity - Bib 8", "macro": "EEP_BIB_PRIME_QTY_3", "description": "Priming quantity (expressed in 1/20th oz increments) for ingredient bib 8", "address": 118, "boardName": "Solenoid", "boardId": 12, "paramType": "WORD", "defaultParam": "40", "currentValue": "40", "newValue": null, "minParam": "40", "maxParam": "400", "machineId": 103, "isPrivate": true}, {"id": 79245, "name": "Priming Quantity - C1", "macro": "EEP_PRIMING_QTY_0", "description": "Priming quantity (expressed in 1/20 fl. oz. increments) for ingredient C1", "address": 84, "boardName": "Pump Board 0", "boardId": 0, "paramType": "BYTE", "defaultParam": "54", "currentValue": "88", "newValue": null, "minParam": "40", "maxParam": "110", "machineId": 103, "isPrivate": true}, {"id": 79246, "name": "Priming Quantity - C2", "macro": "EEP_PRIMING_QTY_0", "description": "Priming quantity (expressed in 1/20 fl. oz. increments) for ingredient C2", "address": 84, "boardName": "Pump Board 1", "boardId": 1, "paramType": "BYTE", "defaultParam": "50", "currentValue": "20", "newValue": null, "minParam": "40", "maxParam": "110", "machineId": 103, "isPrivate": true}, {"id": 79247, "name": "Priming Quantity - C3", "macro": "EEP_PRIMING_QTY_0", "description": "Priming quantity (expressed in 1/20 fl. oz. increments) for ingredient C3", "address": 84, "boardName": "Pump Board 2", "boardId": 2, "paramType": "BYTE", "defaultParam": "57", "currentValue": "55", "newValue": null, "minParam": "40", "maxParam": "110", "machineId": 103, "isPrivate": true}, {"id": 79248, "name": "Priming Quantity - C4", "macro": "EEP_PRIMING_QTY_0", "description": "Priming quantity (expressed in 1/20 fl. oz. increments) for ingredient C4", "address": 84, "boardName": "Pump Board 3", "boardId": 3, "paramType": "BYTE", "defaultParam": "50", "currentValue": "101", "newValue": null, "minParam": "40", "maxParam": "110", "machineId": 103, "isPrivate": true}, {"id": 79249, "name": "Priming Quantity - C5", "macro": "EEP_PRIMING_QTY_0", "description": "Priming quantity (expressed in 1/20 fl. oz. increments) for ingredient C5", "address": 84, "boardName": "Pump Board 4", "boardId": 4, "paramType": "BYTE", "defaultParam": "55", "currentValue": "67", "newValue": null, "minParam": "40", "maxParam": "110", "machineId": 103, "isPrivate": true}, {"id": 79250, "name": "Priming Quantity - C6", "macro": "EEP_PRIMING_QTY_0", "description": "Priming quantity (expressed in 1/20 fl. oz. increments) for ingredient C6", "address": 84, "boardName": "Pump Board 5", "boardId": 5, "paramType": "BYTE", "defaultParam": "55", "currentValue": "38", "newValue": null, "minParam": "40", "maxParam": "110", "machineId": 103, "isPrivate": true}, {"id": 79251, "name": "Priming Quantity - D1", "macro": "EEP_PRIMING_QTY_1", "description": "Priming quantity (expressed in 1/20 fl. oz. increments) for ingredient D1", "address": 100, "boardName": "Pump Board 0", "boardId": 0, "paramType": "BYTE", "defaultParam": "64", "currentValue": "71", "newValue": null, "minParam": "40", "maxParam": "110", "machineId": 103, "isPrivate": true}, {"id": 79252, "name": "Priming Quantity - D2", "macro": "EEP_PRIMING_QTY_1", "description": "Priming quantity (expressed in 1/20 fl. oz. increments) for ingredient D2", "address": 100, "boardName": "Pump Board 1", "boardId": 1, "paramType": "BYTE", "defaultParam": "48", "currentValue": "78", "newValue": null, "minParam": "40", "maxParam": "110", "machineId": 103, "isPrivate": true}, {"id": 79253, "name": "Priming Quantity - D3", "macro": "EEP_PRIMING_QTY_1", "description": "Priming quantity (expressed in 1/20 fl. oz. increments) for ingredient D3", "address": 100, "boardName": "Pump Board 2", "boardId": 2, "paramType": "BYTE", "defaultParam": "56", "currentValue": "64", "newValue": null, "minParam": "40", "maxParam": "110", "machineId": 103, "isPrivate": true}, {"id": 79254, "name": "Priming Quantity - D4", "macro": "EEP_PRIMING_QTY_1", "description": "Priming quantity (expressed in 1/20 fl. oz. increments) for ingredient D4", "address": 100, "boardName": "Pump Board 3", "boardId": 3, "paramType": "BYTE", "defaultParam": "51", "currentValue": "20", "newValue": null, "minParam": "40", "maxParam": "110", "machineId": 103, "isPrivate": true}, {"id": 79255, "name": "Priming Quantity - D5", "macro": "EEP_PRIMING_QTY_1", "description": "Priming quantity (expressed in 1/20 fl. oz. increments) for ingredient D5", "address": 100, "boardName": "Pump Board 4", "boardId": 4, "paramType": "BYTE", "defaultParam": "55", "currentValue": "70", "newValue": null, "minParam": "40", "maxParam": "110", "machineId": 103, "isPrivate": true}, {"id": 79256, "name": "Priming Quantity - D6", "macro": "EEP_PRIMING_QTY_1", "description": "Priming quantity (expressed in 1/20 fl. oz. increments) for ingredient D6", "address": 100, "boardName": "Pump Board 5", "boardId": 5, "paramType": "BYTE", "defaultParam": "52", "currentValue": "71", "newValue": null, "minParam": "40", "maxParam": "110", "machineId": 103, "isPrivate": true}, {"id": 79257, "name": "Priming Quantity - E1", "macro": "EEP_PRIMING_QTY_2", "description": "Priming quantity (expressed in 1/20 fl. oz. increments) for ingredient E1", "address": 116, "boardName": "Pump Board 0", "boardId": 0, "paramType": "BYTE", "defaultParam": "60", "currentValue": "63", "newValue": null, "minParam": "40", "maxParam": "110", "machineId": 103, "isPrivate": true}, {"id": 79258, "name": "Priming Quantity - E2", "macro": "EEP_PRIMING_QTY_2", "description": "Priming quantity (expressed in 1/20 fl. oz. increments) for ingredient E2", "address": 116, "boardName": "Pump Board 1", "boardId": 1, "paramType": "BYTE", "defaultParam": "46", "currentValue": "20", "newValue": null, "minParam": "40", "maxParam": "110", "machineId": 103, "isPrivate": true}, {"id": 79259, "name": "Priming Quantity - E3", "macro": "EEP_PRIMING_QTY_2", "description": "Priming quantity (expressed in 1/20 fl. oz. increments) for ingredient E3", "address": 116, "boardName": "Pump Board 2", "boardId": 2, "paramType": "BYTE", "defaultParam": "63", "currentValue": "20", "newValue": null, "minParam": "40", "maxParam": "110", "machineId": 103, "isPrivate": true}, {"id": 79260, "name": "Priming Quantity - E4", "macro": "EEP_PRIMING_QTY_2", "description": "Priming quantity (expressed in 1/20 fl. oz. increments) for ingredient E4", "address": 116, "boardName": "Pump Board 3", "boardId": 3, "paramType": "BYTE", "defaultParam": "48", "currentValue": "20", "newValue": null, "minParam": "40", "maxParam": "110", "machineId": 103, "isPrivate": true}, {"id": 79261, "name": "Priming Quantity - E5", "macro": "EEP_PRIMING_QTY_2", "description": "Priming quantity (expressed in 1/20 fl. oz. increments) for ingredient E5", "address": 116, "boardName": "Pump Board 4", "boardId": 4, "paramType": "BYTE", "defaultParam": "64", "currentValue": "69", "newValue": null, "minParam": "40", "maxParam": "110", "machineId": 103, "isPrivate": true}, {"id": 79262, "name": "Priming Quantity - E6", "macro": "EEP_PRIMING_QTY_2", "description": "Priming quantity (expressed in 1/20 fl. oz. increments) for ingredient E6", "address": 116, "boardName": "Pump Board 5", "boardId": 5, "paramType": "BYTE", "defaultParam": "51", "currentValue": "20", "newValue": null, "minParam": "40", "maxParam": "110", "machineId": 103, "isPrivate": true}, {"id": 79263, "name": "Priming Quantity - F1", "macro": "EEP_PRIMING_QTY_3", "description": "Priming quantity (expressed in 1/20 fl. oz. increments) for ingredient F1", "address": 132, "boardName": "Pump Board 0", "boardId": 0, "paramType": "BYTE", "defaultParam": "66", "currentValue": "75", "newValue": null, "minParam": "40", "maxParam": "110", "machineId": 103, "isPrivate": true}, {"id": 79264, "name": "Priming Quantity - F2", "macro": "EEP_PRIMING_QTY_3", "description": "Priming quantity (expressed in 1/20 fl. oz. increments) for ingredient F2", "address": 132, "boardName": "Pump Board 1", "boardId": 1, "paramType": "BYTE", "defaultParam": "46", "currentValue": "74", "newValue": null, "minParam": "40", "maxParam": "110", "machineId": 103, "isPrivate": true}, {"id": 79265, "name": "Priming Quantity - F3", "macro": "EEP_PRIMING_QTY_3", "description": "Priming quantity (expressed in 1/20 fl. oz. increments) for ingredient F3", "address": 132, "boardName": "Pump Board 2", "boardId": 2, "paramType": "BYTE", "defaultParam": "62", "currentValue": "76", "newValue": null, "minParam": "40", "maxParam": "110", "machineId": 103, "isPrivate": true}, {"id": 79266, "name": "Priming Quantity - F4", "macro": "EEP_PRIMING_QTY_3", "description": "Priming quantity (expressed in 1/20 fl. oz. increments) for ingredient F4", "address": 132, "boardName": "Pump Board 3", "boardId": 3, "paramType": "BYTE", "defaultParam": "47", "currentValue": "68", "newValue": null, "minParam": "40", "maxParam": "110", "machineId": 103, "isPrivate": true}, {"id": 79267, "name": "Priming Quantity - F5", "macro": "EEP_PRIMING_QTY_3", "description": "Priming quantity (expressed in 1/20 fl. oz. increments) for ingredient F5", "address": 132, "boardName": "Pump Board 4", "boardId": 4, "paramType": "BYTE", "defaultParam": "63", "currentValue": "77", "newValue": null, "minParam": "40", "maxParam": "110", "machineId": 103, "isPrivate": true}, {"id": 79268, "name": "Priming Quantity - F6", "macro": "EEP_PRIMING_QTY_3", "description": "Priming quantity (expressed in 1/20 fl. oz. increments) for ingredient F6", "address": 132, "boardName": "Pump Board 5", "boardId": 5, "paramType": "BYTE", "defaultParam": "48", "currentValue": "83", "newValue": null, "minParam": "40", "maxParam": "110", "machineId": 103, "isPrivate": true}, {"id": 79269, "name": "Priming Quantity - Soda Bib", "macro": "EEP_BIB_PRIME_QTY_SODA", "description": "Priming quantity (expressed in 1/20th oz increments) for the soda bib", "address": 120, "boardName": "Nozzle", "boardId": 560, "paramType": "WORD", "defaultParam": "40", "currentValue": "65535", "newValue": null, "minParam": "40", "maxParam": "400", "machineId": 103, "isPrivate": true}, {"id": 79270, "name": "Priming Quantity - Water Bib", "macro": "EEP_BIB_PRIME_QTY_WATER", "description": "Priming quantity (expressed in 1/20th oz increments) for the water bib", "address": 122, "boardName": "Nozzle", "boardId": 560, "paramType": "WORD", "defaultParam": "40", "currentValue": "65535", "newValue": null, "minParam": "40", "maxParam": "400", "machineId": 103, "isPrivate": true}, {"id": 79271, "name": "Refresh Line Timeout - A1", "macro": "EEP_REFRESH_TIMEOUT_0", "description": "Time (in seconds) this ingredients can remain in the lines without any dispensing operations. Set to 0 for no timeout.", "address": 336, "boardName": "Pump Board 7", "boardId": 7, "paramType": "WORD", "defaultParam": "0", "currentValue": "3600", "newValue": null, "minParam": "0", "maxParam": "10800", "machineId": 103, "isPrivate": true}, {"id": 79272, "name": "Refresh Line Timeout - A2", "macro": "EEP_REFRESH_TIMEOUT_2", "description": "Time (in seconds) this ingredients can remain in the lines without any dispensing operations. Set to 0 for no timeout.", "address": 340, "boardName": "Pump Board 7", "boardId": 7, "paramType": "WORD", "defaultParam": "0", "currentValue": "3600", "newValue": null, "minParam": "0", "maxParam": "10800", "machineId": 103, "isPrivate": true}, {"id": 79273, "name": "Refresh Line Timeout - A3", "macro": "EEP_REFRESH_TIMEOUT_0", "description": "Time (in seconds) this ingredients can remain in the lines without any dispensing operations. Set to 0 for no timeout.", "address": 336, "boardName": "Pump Board 8", "boardId": 8, "paramType": "WORD", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "10800", "machineId": 103, "isPrivate": true}, {"id": 79274, "name": "Refresh Line Timeout - A4", "macro": "EEP_REFRESH_TIMEOUT_2", "description": "Time (in seconds) this ingredients can remain in the lines without any dispensing operations. Set to 0 for no timeout.", "address": 340, "boardName": "Pump Board 8", "boardId": 8, "paramType": "WORD", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "10800", "machineId": 103, "isPrivate": true}, {"id": 79275, "name": "Refresh Line Timeout - A5", "macro": "EEP_REFRESH_TIMEOUT_0", "description": "Time (in seconds) this ingredients can remain in the lines without any dispensing operations. Set to 0 for no timeout.", "address": 336, "boardName": "Pump Board 9", "boardId": 9, "paramType": "WORD", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "10800", "machineId": 103, "isPrivate": true}, {"id": 79276, "name": "Refresh Line Timeout - A6", "macro": "EEP_REFRESH_TIMEOUT_2", "description": "Time (in seconds) this ingredients can remain in the lines without any dispensing operations. Set to 0 for no timeout.", "address": 340, "boardName": "Pump Board 9", "boardId": 9, "paramType": "WORD", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "10800", "machineId": 103, "isPrivate": true}, {"id": 79277, "name": "Refresh Line Timeout - B1", "macro": "EEP_REFRESH_TIMEOUT_1", "description": "Time (in seconds) this ingredients can remain in the lines without any dispensing operations. Set to 0 for no timeout.", "address": 338, "boardName": "Pump Board 7", "boardId": 7, "paramType": "WORD", "defaultParam": "0", "currentValue": "3600", "newValue": null, "minParam": "0", "maxParam": "10800", "machineId": 103, "isPrivate": true}, {"id": 79278, "name": "Refresh Line Timeout - B2", "macro": "EEP_REFRESH_TIMEOUT_3", "description": "Time (in seconds) this ingredients can remain in the lines without any dispensing operations. Set to 0 for no timeout.", "address": 342, "boardName": "Pump Board 7", "boardId": 7, "paramType": "WORD", "defaultParam": "0", "currentValue": "3600", "newValue": null, "minParam": "0", "maxParam": "10800", "machineId": 103, "isPrivate": true}, {"id": 79279, "name": "Refresh Line Timeout - B3", "macro": "EEP_REFRESH_TIMEOUT_1", "description": "Time (in seconds) this ingredients can remain in the lines without any dispensing operations. Set to 0 for no timeout.", "address": 338, "boardName": "Pump Board 8", "boardId": 8, "paramType": "WORD", "defaultParam": "0", "currentValue": "7200", "newValue": null, "minParam": "0", "maxParam": "10800", "machineId": 103, "isPrivate": true}, {"id": 79280, "name": "Refresh Line Timeout - B4", "macro": "EEP_REFRESH_TIMEOUT_3", "description": "Time (in seconds) this ingredients can remain in the lines without any dispensing operations. Set to 0 for no timeout.", "address": 342, "boardName": "Pump Board 8", "boardId": 8, "paramType": "WORD", "defaultParam": "0", "currentValue": "7200", "newValue": null, "minParam": "0", "maxParam": "10800", "machineId": 103, "isPrivate": true}, {"id": 79281, "name": "Refresh Line Timeout - B5", "macro": "EEP_REFRESH_TIMEOUT_1", "description": "Time (in seconds) this ingredients can remain in the lines without any dispensing operations. Set to 0 for no timeout.", "address": 338, "boardName": "Pump Board 9", "boardId": 9, "paramType": "WORD", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "10800", "machineId": 103, "isPrivate": true}, {"id": 79282, "name": "Refresh Line Timeout - B6", "macro": "EEP_REFRESH_TIMEOUT_3", "description": "Time (in seconds) this ingredients can remain in the lines without any dispensing operations. Set to 0 for no timeout.", "address": 342, "boardName": "Pump Board 9", "boardId": 9, "paramType": "WORD", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "10800", "machineId": 103, "isPrivate": true}, {"id": 79283, "name": "Refresh Line Timeout - C1", "macro": "EEP_REFRESH_TIMEOUT_0", "description": "Time (in seconds) this ingredients can remain in the lines without any dispensing operations. Set to 0 for no timeout.", "address": 336, "boardName": "Pump Board 0", "boardId": 0, "paramType": "WORD", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "10800", "machineId": 103, "isPrivate": true}, {"id": 79284, "name": "Refresh Line Timeout - C2", "macro": "EEP_REFRESH_TIMEOUT_0", "description": "Time (in seconds) this ingredients can remain in the lines without any dispensing operations. Set to 0 for no timeout.", "address": 336, "boardName": "Pump Board 1", "boardId": 1, "paramType": "WORD", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "10800", "machineId": 103, "isPrivate": true}, {"id": 79285, "name": "Refresh Line Timeout - C3", "macro": "EEP_REFRESH_TIMEOUT_0", "description": "Time (in seconds) this ingredients can remain in the lines without any dispensing operations. Set to 0 for no timeout.", "address": 336, "boardName": "Pump Board 2", "boardId": 2, "paramType": "WORD", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "10800", "machineId": 103, "isPrivate": true}, {"id": 79286, "name": "Refresh Line Timeout - C4", "macro": "EEP_REFRESH_TIMEOUT_0", "description": "Time (in seconds) this ingredients can remain in the lines without any dispensing operations. Set to 0 for no timeout.", "address": 336, "boardName": "Pump Board 3", "boardId": 3, "paramType": "WORD", "defaultParam": "0", "currentValue": "7200", "newValue": null, "minParam": "0", "maxParam": "10800", "machineId": 103, "isPrivate": true}, {"id": 79287, "name": "Refresh Line Timeout - C5", "macro": "EEP_REFRESH_TIMEOUT_0", "description": "Time (in seconds) this ingredients can remain in the lines without any dispensing operations. Set to 0 for no timeout.", "address": 336, "boardName": "Pump Board 4", "boardId": 4, "paramType": "WORD", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "10800", "machineId": 103, "isPrivate": true}, {"id": 79288, "name": "Refresh Line Timeout - C6", "macro": "EEP_REFRESH_TIMEOUT_0", "description": "Time (in seconds) this ingredients can remain in the lines without any dispensing operations. Set to 0 for no timeout.", "address": 336, "boardName": "Pump Board 5", "boardId": 5, "paramType": "WORD", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "10800", "machineId": 103, "isPrivate": true}, {"id": 79289, "name": "Refresh Line Timeout - D1", "macro": "EEP_REFRESH_TIMEOUT_1", "description": "Time (in seconds) this ingredients can remain in the lines without any dispensing operations. Set to 0 for no timeout.", "address": 338, "boardName": "Pump Board 0", "boardId": 0, "paramType": "WORD", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "10800", "machineId": 103, "isPrivate": true}, {"id": 79290, "name": "Refresh Line Timeout - D2", "macro": "EEP_REFRESH_TIMEOUT_1", "description": "Time (in seconds) this ingredients can remain in the lines without any dispensing operations. Set to 0 for no timeout.", "address": 338, "boardName": "Pump Board 1", "boardId": 1, "paramType": "WORD", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "10800", "machineId": 103, "isPrivate": true}, {"id": 79291, "name": "Refresh Line Timeout - D3", "macro": "EEP_REFRESH_TIMEOUT_1", "description": "Time (in seconds) this ingredients can remain in the lines without any dispensing operations. Set to 0 for no timeout.", "address": 338, "boardName": "Pump Board 2", "boardId": 2, "paramType": "WORD", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "10800", "machineId": 103, "isPrivate": true}, {"id": 79292, "name": "Refresh Line Timeout - D4", "macro": "EEP_REFRESH_TIMEOUT_1", "description": "Time (in seconds) this ingredients can remain in the lines without any dispensing operations. Set to 0 for no timeout.", "address": 338, "boardName": "Pump Board 3", "boardId": 3, "paramType": "WORD", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "10800", "machineId": 103, "isPrivate": true}, {"id": 79293, "name": "Refresh Line Timeout - D5", "macro": "EEP_REFRESH_TIMEOUT_1", "description": "Time (in seconds) this ingredients can remain in the lines without any dispensing operations. Set to 0 for no timeout.", "address": 338, "boardName": "Pump Board 4", "boardId": 4, "paramType": "WORD", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "10800", "machineId": 103, "isPrivate": true}, {"id": 79294, "name": "Refresh Line Timeout - D6", "macro": "EEP_REFRESH_TIMEOUT_1", "description": "Time (in seconds) this ingredients can remain in the lines without any dispensing operations. Set to 0 for no timeout.", "address": 338, "boardName": "Pump Board 5", "boardId": 5, "paramType": "WORD", "defaultParam": "0", "currentValue": "7200", "newValue": null, "minParam": "0", "maxParam": "10800", "machineId": 103, "isPrivate": true}, {"id": 79295, "name": "Refresh Line Timeout - E1", "macro": "EEP_REFRESH_TIMEOUT_2", "description": "Time (in seconds) this ingredients can remain in the lines without any dispensing operations. Set to 0 for no timeout.", "address": 340, "boardName": "Pump Board 0", "boardId": 0, "paramType": "WORD", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "10800", "machineId": 103, "isPrivate": true}, {"id": 79296, "name": "Refresh Line Timeout - E2", "macro": "EEP_REFRESH_TIMEOUT_2", "description": "Time (in seconds) this ingredients can remain in the lines without any dispensing operations. Set to 0 for no timeout.", "address": 340, "boardName": "Pump Board 1", "boardId": 1, "paramType": "WORD", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "10800", "machineId": 103, "isPrivate": true}, {"id": 79297, "name": "Refresh Line Timeout - E3", "macro": "EEP_REFRESH_TIMEOUT_2", "description": "Time (in seconds) this ingredients can remain in the lines without any dispensing operations. Set to 0 for no timeout.", "address": 340, "boardName": "Pump Board 2", "boardId": 2, "paramType": "WORD", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "10800", "machineId": 103, "isPrivate": true}, {"id": 79298, "name": "Refresh Line Timeout - E4", "macro": "EEP_REFRESH_TIMEOUT_2", "description": "Time (in seconds) this ingredients can remain in the lines without any dispensing operations. Set to 0 for no timeout.", "address": 340, "boardName": "Pump Board 3", "boardId": 3, "paramType": "WORD", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "10800", "machineId": 103, "isPrivate": true}, {"id": 79299, "name": "Refresh Line Timeout - E5", "macro": "EEP_REFRESH_TIMEOUT_2", "description": "Time (in seconds) this ingredients can remain in the lines without any dispensing operations. Set to 0 for no timeout.", "address": 340, "boardName": "Pump Board 4", "boardId": 4, "paramType": "WORD", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "10800", "machineId": 103, "isPrivate": true}, {"id": 79300, "name": "Refresh Line Timeout - E6", "macro": "EEP_REFRESH_TIMEOUT_2", "description": "Time (in seconds) this ingredients can remain in the lines without any dispensing operations. Set to 0 for no timeout.", "address": 340, "boardName": "Pump Board 5", "boardId": 5, "paramType": "WORD", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "10800", "machineId": 103, "isPrivate": true}, {"id": 79301, "name": "Refresh Line Timeout - F1", "macro": "EEP_REFRESH_TIMEOUT_3", "description": "Time (in seconds) this ingredients can remain in the lines without any dispensing operations. Set to 0 for no timeout.", "address": 342, "boardName": "Pump Board 0", "boardId": 0, "paramType": "WORD", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "10800", "machineId": 103, "isPrivate": true}, {"id": 79302, "name": "Refresh Line Timeout - F2", "macro": "EEP_REFRESH_TIMEOUT_3", "description": "Time (in seconds) this ingredients can remain in the lines without any dispensing operations. Set to 0 for no timeout.", "address": 342, "boardName": "Pump Board 1", "boardId": 1, "paramType": "WORD", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "10800", "machineId": 103, "isPrivate": true}, {"id": 79303, "name": "Refresh Line Timeout - F3", "macro": "EEP_REFRESH_TIMEOUT_3", "description": "Time (in seconds) this ingredients can remain in the lines without any dispensing operations. Set to 0 for no timeout.", "address": 342, "boardName": "Pump Board 2", "boardId": 2, "paramType": "WORD", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "10800", "machineId": 103, "isPrivate": true}, {"id": 79304, "name": "Refresh Line Timeout - F4", "macro": "EEP_REFRESH_TIMEOUT_3", "description": "Time (in seconds) this ingredients can remain in the lines without any dispensing operations. Set to 0 for no timeout.", "address": 342, "boardName": "Pump Board 3", "boardId": 3, "paramType": "WORD", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "10800", "machineId": 103, "isPrivate": true}, {"id": 79305, "name": "Refresh Line Timeout - F5", "macro": "EEP_REFRESH_TIMEOUT_3", "description": "Time (in seconds) this ingredients can remain in the lines without any dispensing operations. Set to 0 for no timeout.", "address": 342, "boardName": "Pump Board 4", "boardId": 4, "paramType": "WORD", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "10800", "machineId": 103, "isPrivate": true}, {"id": 79306, "name": "Refresh Line Timeout - F6", "macro": "EEP_REFRESH_TIMEOUT_3", "description": "Time (in seconds) this ingredients can remain in the lines without any dispensing operations. Set to 0 for no timeout.", "address": 342, "boardName": "Pump Board 5", "boardId": 5, "paramType": "WORD", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "10800", "machineId": 103, "isPrivate": true}, {"id": 86977, "name": "Calibration Factor - Cleaner", "macro": "EEP_CLEANER_CAL_VAL", "description": "Cleaner calibration factor (Number of pulses per fluid oz)", "address": 128, "boardName": "Nozzle", "boardId": 560, "paramType": "DWORD", "defaultParam": "52720", "currentValue": "52720", "newValue": null, "minParam": "9720", "maxParam": "194400", "machineId": 103, "isPrivate": false}, {"id": 86978, "name": "Current Cleaner Level", "macro": "EEP_CLEANER_LEVEL", "description": "Current volume of cleaner concentrate (expressed in 1/20th oz increments)", "address": 134, "boardName": "Nozzle", "boardId": 560, "paramType": "WORD", "defaultParam": "0", "currentValue": "55938", "newValue": null, "minParam": "0", "maxParam": "65535", "machineId": 103, "isPrivate": true}, {"id": 86979, "name": "Enable Cleaner Tracking", "macro": "EEP_ENABLE_CLEANER_TRACKING", "description": "Enable cleaner tracking using inline paddle sensors", "address": 137, "boardName": "Nozzle", "boardId": 560, "paramType": "BOOLEAN", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 103, "isPrivate": false}, {"id": 86980, "name": "Ingredient number for Water", "macro": "EEP_WATER_INGR_NUM", "description": "Ingredient number for water. Used for self cleaning procedure", "address": 136, "boardName": "Nozzle", "boardId": 560, "paramType": "BYTE", "defaultParam": "45", "currentValue": "45", "newValue": null, "minParam": "40", "maxParam": "47", "machineId": 103, "isPrivate": false}, {"id": 86981, "name": "Priming Quantity - Cleaner", "macro": "EEP_CLEANER_PRIME_QTY", "description": "Priming quantity (expressed in 1/20th oz increments) for ingredient Cleaner", "address": 132, "boardName": "Nozzle", "boardId": 560, "paramType": "WORD", "defaultParam": "40", "currentValue": "40", "newValue": null, "minParam": "20", "maxParam": "400", "machineId": 103, "isPrivate": false}, {"id": 95456, "name": "Enable Evaporator Plate Temperature Check", "macro": "EEP_ENABLE_EVAP_PLATE_TEMP_CEHCK", "description": "Enable Evaporator Plate temperature measurements to be reported back to the master PCB", "address": 273, "boardName": "Cooling", "boardId": 576, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 103, "isPrivate": false}, {"id": 95457, "name": "Ingredient Line Status - Bib 1", "macro": "EEP_BIB_LINE_STATUS_0", "description": "Bitfield for current ingredient prime status of ingredient", "address": 144, "boardName": "Nozzle", "boardId": 560, "paramType": "BYTE", "defaultParam": "0", "currentValue": "9", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 103, "isPrivate": true}, {"id": 95458, "name": "Ingredient Line Status - Bib 2", "macro": "EEP_BIB_LINE_STATUS_1", "description": "Bitfield for current ingredient prime status of ingredient", "address": 145, "boardName": "Nozzle", "boardId": 560, "paramType": "BYTE", "defaultParam": "0", "currentValue": "9", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 103, "isPrivate": true}, {"id": 95459, "name": "Ingredient Line Status - Bib 3", "macro": "EEP_BIB_LINE_STATUS_2", "description": "Bitfield for current ingredient prime status of ingredient", "address": 146, "boardName": "Nozzle", "boardId": 560, "paramType": "BYTE", "defaultParam": "0", "currentValue": "9", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 103, "isPrivate": true}, {"id": 95460, "name": "Ingredient Line Status - Bib 4", "macro": "EEP_BIB_LINE_STATUS_3", "description": "Bitfield for current ingredient prime status of ingredient", "address": 147, "boardName": "Nozzle", "boardId": 560, "paramType": "BYTE", "defaultParam": "0", "currentValue": "9", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 103, "isPrivate": true}, {"id": 95461, "name": "Ingredient Line Status - Bib 5", "macro": "EEP_BIB_LINE_STATUS_0", "description": "Bitfield for current ingredient prime status of ingredient", "address": 144, "boardName": "Solenoid", "boardId": 12, "paramType": "BYTE", "defaultParam": "0", "currentValue": "9", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 103, "isPrivate": true}, {"id": 95462, "name": "Ingredient Line Status - Bib 6", "macro": "EEP_BIB_LINE_STATUS_1", "description": "Bitfield for current ingredient prime status of ingredient", "address": 145, "boardName": "Solenoid", "boardId": 12, "paramType": "BYTE", "defaultParam": "0", "currentValue": "9", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 103, "isPrivate": true}, {"id": 95463, "name": "Ingredient Line Status - Bib 7", "macro": "EEP_BIB_LINE_STATUS_2", "description": "Bitfield for current ingredient prime status of ingredient", "address": 146, "boardName": "Solenoid", "boardId": 12, "paramType": "BYTE", "defaultParam": "0", "currentValue": "9", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 103, "isPrivate": true}, {"id": 95464, "name": "Ingredient Line Status - Bib 8", "macro": "EEP_BIB_LINE_STATUS_3", "description": "Bitfield for current ingredient prime status of ingredient", "address": 147, "boardName": "Solenoid", "boardId": 12, "paramType": "BYTE", "defaultParam": "0", "currentValue": "9", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 103, "isPrivate": true}, {"id": 95465, "name": "Ingredient Line Status - Cleaner", "macro": "EEP_BIB_LINE_STATUS_CLEANER", "description": "Bitfield for current ingredient prime status of ingredient", "address": 150, "boardName": "Nozzle", "boardId": 560, "paramType": "BYTE", "defaultParam": "0", "currentValue": "255", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 103, "isPrivate": true}, {"id": 95466, "name": "Ingredient Line Status - Soda Bib", "macro": "EEP_BIB_LINE_STATUS_SODA", "description": "Bitfield for current ingredient prime status of ingredient", "address": 148, "boardName": "Nozzle", "boardId": 560, "paramType": "BYTE", "defaultParam": "0", "currentValue": "9", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 103, "isPrivate": true}, {"id": 95467, "name": "Ingredient Line Status - Unused Bib", "macro": "EEP_BIB_LINE_STATUS_7", "description": "Bitfield for current ingredient prime status of ingredient", "address": 151, "boardName": "Nozzle", "boardId": 560, "paramType": "BYTE", "defaultParam": "0", "currentValue": "9", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 103, "isPrivate": true}, {"id": 95468, "name": "Ingredient Line Status - Water Bib", "macro": "EEP_BIB_LINE_STATUS_WATER", "description": "Bitfield for current ingredient prime status of ingredient", "address": 149, "boardName": "Nozzle", "boardId": 560, "paramType": "BYTE", "defaultParam": "0", "currentValue": "255", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 103, "isPrivate": true}, {"id": 95471, "name": "Soaking Lines", "macro": "EEP_SOAKING_LINES", "description": "Bitfield of which dispensing lines are soaking with water or cleaner", "address": 240, "boardName": "Master", "boardId": 65535, "paramType": "DWORD", "defaultParam": "0", "currentValue": "4038565355", "newValue": null, "minParam": "0", "maxParam": "1048575", "machineId": 103, "isPrivate": true}, {"id": 95818, "name": "Shutdown Prep Quantity", "macro": "EEP_SHUTDOWN_PREP_QTY", "description": "The volume of liquid to backtrack before shutdown to ensure there is no cross contamination between ingredients in the nozzle cap (in 1/20th oz increments)", "address": 394, "boardName": "Master", "boardId": 65535, "paramType": "BYTE", "defaultParam": "2", "currentValue": "2", "newValue": null, "minParam": "0", "maxParam": "60", "machineId": 103, "isPrivate": false}, {"id": 95819, "name": "Startup Prep Quantity", "macro": "EEP_STARTUP_PREP_QTY", "description": "The volume of liquid to dispense before startup to ensure all ingredients are primed and ready to dispense into the first drink (in 1/20th oz increments)", "address": 395, "boardName": "Master", "boardId": 65535, "paramType": "BYTE", "defaultParam": "7", "currentValue": "7", "newValue": null, "minParam": "0", "maxParam": "60", "machineId": 103, "isPrivate": false}, {"id": 96655, "name": "Ingredient Sensor Enable - A5, A6, B5, B6", "macro": "EEP_BUBBLE_DETECT_ENABLE`", "description": "Enable the use of ingredient detection sensors for a group of 4 house mix ingredients", "address": 160, "boardName": "Pump Board 9", "boardId": 9, "paramType": "BOOLEAN", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 103, "isPrivate": false}, {"id": 96689, "name": "Pickup LEDs Always On", "macro": "EEP_OUTPUT_LEDS_ON", "description": "Force the output/pickup LEDs to remain on until a drink/order is picked up", "address": 64, "boardName": "Master", "boardId": 65535, "paramType": "BOOLEAN", "defaultParam": " 0", "currentValue": " 0", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 103, "isPrivate": false}, {"id": 96690, "name": "Ingredient Line Status - Unused Bib", "macro": "EEP_BIB_LINE_STATUS_8", "description": "Bitfield for current ingredient prime status of ingredient", "address": 151, "boardName": "Nozzle", "boardId": 560, "paramType": "BYTE", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 103, "isPrivate": true}, {"id": 96691, "name": "<PERSON><PERSON><PERSON> (2) Current Maximum", "macro": "EEP_PERIPH_CHAS_FAN2_MAX_CURRENT", "description": "ADC maximum value of normal operation for the external chassis fan (2) (Current = ADC * 5/4096)", "address": 302, "boardName": "Cooling", "boardId": 576, "paramType": "WORD", "defaultParam": "819", "currentValue": "819", "newValue": null, "minParam": "0", "maxParam": "1500", "machineId": 103, "isPrivate": false}, {"id": 96692, "name": "External Fan 4 Current Maximum", "macro": "EEP_PERIPH_EXT_FAN4_MAX_CURRENT", "description": "ADC maximum value of normal operation for the external fan (4) (Current = ADC * 5/4096)", "address": 300, "boardName": "Cooling", "boardId": 576, "paramType": "WORD", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "1500", "machineId": 103, "isPrivate": false}, {"id": 96693, "name": "<PERSON><PERSON><PERSON> (1) Current Maximum", "macro": "EEP_PERIPH_CHAS_FAN1_MAX_CURRENT", "description": "ADC maximum value of normal operation for the external chassis fan (1) (Current = ADC * 5/4096)", "address": 298, "boardName": "Cooling", "boardId": 576, "paramType": "WORD", "defaultParam": "819", "currentValue": "819", "newValue": null, "minParam": "0", "maxParam": "1500", "machineId": 103, "isPrivate": false}, {"id": 96694, "name": "Compressor Fan Current Maximum", "macro": "EEP_PERIPH_COMP_FAN_MAX_CURRENT", "description": "ADC maximum value of normal operation for the fridge’s external compressor fan (Current = ADC * 5/4096)", "address": 296, "boardName": "Cooling", "boardId": 576, "paramType": "WORD", "defaultParam": "819", "currentValue": "819", "newValue": null, "minParam": "0", "maxParam": "1500", "machineId": 103, "isPrivate": false}, {"id": 96695, "name": "Internal Fan (Right) Current Maximum", "macro": "EEP_PERIPH_INT_RGHT_MAX_CURRENT", "description": "ADC maximum value of normal operation for the fridge’s right internal fan (Current = ADC * 5/4096)", "address": 294, "boardName": "Cooling", "boardId": 576, "paramType": "WORD", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "1500", "machineId": 103, "isPrivate": false}, {"id": 96696, "name": "Internal Fan (Left) Current Maximum", "macro": "EEP_PERIPH_INT_LFT_MAX_CURRENT", "description": "ADC maximum value of normal operation for the fridge’s left internal fan (Current = ADC * 5/4096)", "address": 292, "boardName": "Cooling", "boardId": 576, "paramType": "WORD", "defaultParam": "1229", "currentValue": "1229", "newValue": null, "minParam": "0", "maxParam": "1500", "machineId": 103, "isPrivate": false}, {"id": 96697, "name": "Internal Fan (Top) Current Maximum", "macro": "EEP_PERIPH_INT_TOP_MAX_CURRENT", "description": "ADC maximum value of normal operation for the fridge’s top internal fan (Current = ADC * 5/4096)", "address": 290, "boardName": "Cooling", "boardId": 576, "paramType": "WORD", "defaultParam": "1229", "currentValue": "1229", "newValue": null, "minParam": "0", "maxParam": "1500", "machineId": 103, "isPrivate": false}, {"id": 96698, "name": "Compressor Current Maximum", "macro": "EEP_PERIPH_COMPRESSOR_MAX_CURRENT", "description": "ADC maximum value of normal operation for the fridge compressor (Current = ADC * 102.4)", "address": 288, "boardName": "Cooling", "boardId": 576, "paramType": "WORD", "defaultParam": "204", "currentValue": "204", "newValue": null, "minParam": "0", "maxParam": "1500", "machineId": 103, "isPrivate": false}, {"id": 96699, "name": "<PERSON><PERSON><PERSON> (2) Current Minimum", "macro": "EEP_PERIPH_CHAS_FAN2_MIN_CURRENT", "description": "ADC minimum value of normal operation for the external chassis fan (2) (Current = ADC * 5/4096)", "address": 270, "boardName": "Cooling", "boardId": 576, "paramType": "WORD", "defaultParam": "163", "currentValue": "163", "newValue": null, "minParam": "0", "maxParam": "1500", "machineId": 103, "isPrivate": false}, {"id": 96700, "name": "External Fan 4 Current Minimum", "macro": "EEP_PERIPH_EXT_FAN4_MIN_CURRENT", "description": "ADC minimum value of normal operation for the external fan (4) (Current = ADC * 5/4096)", "address": 268, "boardName": "Cooling", "boardId": 576, "paramType": "WORD", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "1500", "machineId": 103, "isPrivate": false}, {"id": 96701, "name": "<PERSON><PERSON><PERSON> (1) Current Minimum", "macro": "EEP_PERIPH_CHAS_FAN1_MIN_CURRENT", "description": "ADC minimum value of normal operation for the external chassis fan (1) (Current = ADC * 5/4096)", "address": 266, "boardName": "Cooling", "boardId": 576, "paramType": "WORD", "defaultParam": "163", "currentValue": "163", "newValue": null, "minParam": "0", "maxParam": "1500", "machineId": 103, "isPrivate": false}, {"id": 96702, "name": "Compressor Fan Current Minimum", "macro": "EEP_PERIPH_COMP_FAN_MIN_CURRENT", "description": "ADC minimum value of normal operation for the fridge’s external compressor fan (Current = ADC * 5/4096)", "address": 264, "boardName": "Cooling", "boardId": 576, "paramType": "WORD", "defaultParam": "327", "currentValue": "327", "newValue": null, "minParam": "0", "maxParam": "1500", "machineId": 103, "isPrivate": false}, {"id": 96703, "name": "Internal Fan (Right) Current Minimum", "macro": "EEP_PERIPH_INT_RGHT_MIN_CURRENT", "description": "ADC minimum value of normal operation for the fridge’s right internal fan (Current = ADC * 5/4096)", "address": 262, "boardName": "Cooling", "boardId": 576, "paramType": "WORD", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "1500", "machineId": 103, "isPrivate": false}, {"id": 96704, "name": "Internal Fan (Left) Current Minimum", "macro": "EEP_PERIPH_INT_LFT_MIN_CURRENT", "description": "ADC minimum value of normal operation for the fridge’s left internal fan (Current = ADC * 5/4096)", "address": 260, "boardName": "Cooling", "boardId": 576, "paramType": "WORD", "defaultParam": "491", "currentValue": "491", "newValue": null, "minParam": "0", "maxParam": "1500", "machineId": 103, "isPrivate": false}, {"id": 96705, "name": "Internal Fan (Top) Current Minimum", "macro": "EEP_PERIPH_INT_TOP_MIN_CURRENT", "description": "ADC minimum value of normal operation for the fridge’s top internal fan (Current = ADC * 5/4096)", "address": 258, "boardName": "Cooling", "boardId": 576, "paramType": "WORD", "defaultParam": "491", "currentValue": "491", "newValue": null, "minParam": "0", "maxParam": "1500", "machineId": 103, "isPrivate": false}, {"id": 96706, "name": "Compressor Current Minimum", "macro": "EEP_PERIPH_COMPRESSOR_MIN_CURRENT", "description": "ADC minimum value of normal operation for the fridge compressor (Current = ADC * 102.4)", "address": 256, "boardName": "Cooling", "boardId": 576, "paramType": "WORD", "defaultParam": "25", "currentValue": "25", "newValue": null, "minParam": "0", "maxParam": "1500", "machineId": 103, "isPrivate": false}, {"id": 96707, "name": "Compressor Minimum Off Time", "macro": "EEP_COMPRESSOR_MIN_OFFTIME", "description": "Timeout used for allowing evaporator plate to thaw before turning the compressor back on. Ignored in machines that have an evaporator plate sensor", "address": 214, "boardName": "Cooling", "boardId": 576, "paramType": "WORD", "defaultParam": "900", "currentValue": "900", "newValue": null, "minParam": "60", "maxParam": "7200", "machineId": 103, "isPrivate": false}, {"id": 96708, "name": "Shutdown Prep Volume", "macro": "EEP_SHUTDOWN_PREP_VOL", "description": "The volume of liquid to backtrack before shutdown to ensure there is no cross contamination between ingredients in the nozzle cap (in 1/20th oz increments)", "address": 394, "boardName": "Master", "boardId": 65535, "paramType": "BYTE", "defaultParam": "2", "currentValue": "2", "newValue": null, "minParam": "0", "maxParam": "60", "machineId": 103, "isPrivate": false}, {"id": 96709, "name": "Startup Prep Volume", "macro": "EEP_STARTUP_PREP_VOL", "description": "The volume of liquid to dispense before startup to ensure all ingredients are primed and ready to dispense into the first drink (in 1/20th oz increments)", "address": 395, "boardName": "Master", "boardId": 65535, "paramType": "BYTE", "defaultParam": "7", "currentValue": "7", "newValue": null, "minParam": "0", "maxParam": "60", "machineId": 103, "isPrivate": false}, {"id": 96710, "name": "Cleaning Paddle Target (Water)", "macro": "EEP_CLEANING_PADDLE_TARGET_WATER", "description": "Expected target for average value of water paddle sensor during self cleaning procedure", "address": 162, "boardName": "Nozzle", "boardId": 560, "paramType": "WORD", "defaultParam": "40", "currentValue": "40", "newValue": null, "minParam": "0", "maxParam": "254", "machineId": 103, "isPrivate": false}, {"id": 96711, "name": "Cleaning Paddle Target (Cleaner)", "macro": "EEP_CLEANING_PADDLE_TARGET_CLEANER", "description": "Expected target for average value of cleaner paddle sensor during self cleaning procedure", "address": 160, "boardName": "Nozzle", "boardId": 560, "paramType": "WORD", "defaultParam": "100", "currentValue": "100", "newValue": null, "minParam": "0", "maxParam": "254", "machineId": 103, "isPrivate": false}, {"id": 98802, "name": "Retraction Quantity C1, D1, E1, F1", "macro": "EEP_RETRACTION_QTY", "description": "Amount to retract (expressed in 1/20 fl. oz. increments) after a forward dispense ", "address": 278, "boardName": "Pump Board 0", "boardId": 0, "paramType": "BYTE", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "20", "machineId": 103, "isPrivate": false}, {"id": 98803, "name": "Retraction Quantity C2, E2, D2, F2", "macro": "EEP_RETRACTION_QTY", "description": "Amount to retract (expressed in 1/20 fl. oz. increments) after a forward dispense ", "address": 278, "boardName": "Pump Board 1", "boardId": 1, "paramType": "BYTE", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "20", "machineId": 103, "isPrivate": false}]