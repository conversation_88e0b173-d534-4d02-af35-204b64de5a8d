[{"id": 62030, "name": "Drain Auto Mode Enable", "macro": "EEP_DRAIN_AUTO_ENABLE", "description": "Enable automatic drain pump cycling based on preset ON time and OFF time periods", "address": 112, "boardName": "Cooling", "boardId": 576, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 112, "isPrivate": false}, {"id": 62031, "name": "<PERSON><PERSON>ump OFF Time", "macro": "EEP_DRAIN_OFF_TIME", "description": "Time the drain pump is turned OFF (in seconds)", "address": 96, "boardName": "Cooling", "boardId": 576, "paramType": "WORD", "defaultParam": "540", "currentValue": "540", "newValue": null, "minParam": "10", "maxParam": "10800", "machineId": 112, "isPrivate": false}, {"id": 62032, "name": "<PERSON><PERSON>ump ON Time", "macro": "EEP_DRAIN_ON_TIME", "description": "Time the drain pump is turned ON (in seconds)", "address": 80, "boardName": "Cooling", "boardId": 576, "paramType": "WORD", "defaultParam": "60", "currentValue": "60", "newValue": null, "minParam": "10", "maxParam": "1800", "machineId": 112, "isPrivate": false}, {"id": 62033, "name": "Refrigeration Enable", "macro": "EEP_ENABLE_REFRIGERATION", "description": "Enable the compressor for cooling the fridge. See ", "address": 50, "boardName": "Cooling", "boardId": 576, "paramType": "BOOLEAN", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 112, "isPrivate": false}, {"id": 62034, "name": "Lower Temperature Threshold (Top Temp Sensor)", "macro": "EEP_LOWER_TEMP_THRESHOLD", "description": "Temperature of the top fridge sensor (in degrees Fahrenheit) below which the compressor is turned OFF", "address": 32, "boardName": "Cooling", "boardId": 576, "paramType": "SINGLE", "defaultParam": "36", "currentValue": "36", "newValue": null, "minParam": "32", "maxParam": "46", "machineId": 112, "isPrivate": false}, {"id": 62035, "name": "Upper Temperature Threshold (Top Temp Sensor)", "macro": "EEP_UPPER_TEMP_THRESHOLD", "description": "Temperature of the top fridge sensor (in degrees Fahrenheit) above which the compressor is turned ON", "address": 16, "boardName": "Cooling", "boardId": 576, "paramType": "SINGLE", "defaultParam": "44", "currentValue": "44", "newValue": null, "minParam": "32", "maxParam": "46", "machineId": 112, "isPrivate": false}, {"id": 62036, "name": "Cooling Drawer <PERSON><PERSON> <PERSON><PERSON>", "macro": "EEP_COOLER_DRAWER_OPEN_ALERT_TIME", "description": "Time the cooling drawer switch can remain open until an alert is sent from the cooling board (in seconds)", "address": 176, "boardName": "Cooling", "boardId": 576, "paramType": "WORD", "defaultParam": "600", "currentValue": "600", "newValue": null, "minParam": "300", "maxParam": "1200", "machineId": 112, "isPrivate": false}, {"id": 62037, "name": "Cooling Drawer Switch <PERSON><PERSON>", "macro": "EEP_COOLER_DRAWER_OPEN_ALERT_ENABLE", "description": "Enable alert for cooling drawer switch open", "address": 160, "boardName": "Cooling", "boardId": 576, "paramType": "BOOLEAN", "defaultParam": "1", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 112, "isPrivate": false}, {"id": 62038, "name": "Upper Temperature Threshold (Bottom Temp Sensor)", "macro": "EEP_UPPER_TEMP_THRESHOLD_BOT", "description": "Temperature of the bottom fridge sensor (in degrees Fahrenheit) above which the compressor is turned ON", "address": 224, "boardName": "Cooling", "boardId": 576, "paramType": "SINGLE", "defaultParam": "44", "currentValue": "44", "newValue": null, "minParam": "32", "maxParam": "46", "machineId": 112, "isPrivate": false}, {"id": 62039, "name": "Ingredient Mixing Enable - A1", "macro": "EEP_MIXING_ENABLE_28", "description": "Enable ingredient mixing for A1", "address": 124, "boardName": "Master", "boardId": 65535, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 112, "isPrivate": false}, {"id": 62040, "name": "Ingredient Mixing Enable - B1", "macro": "EEP_MIXING_ENABLE_29", "description": "Enable ingredient mixing for B1", "address": 125, "boardName": "Master", "boardId": 65535, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 112, "isPrivate": false}, {"id": 62041, "name": "Ingredient Mixing Enable - A2", "macro": "EEP_MIXING_ENABLE_30", "description": "Enable ingredient mixing for A2", "address": 126, "boardName": "Master", "boardId": 65535, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 112, "isPrivate": false}, {"id": 62042, "name": "Ingredient Mixing Enable - B2", "macro": "EEP_MIXING_ENABLE_31", "description": "Enable ingredient mixing for B2", "address": 127, "boardName": "Master", "boardId": 65535, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 112, "isPrivate": false}, {"id": 62043, "name": "Ingredient Mixing Period", "macro": "EEP_MIXING_PERIOD", "description": "Time (in seconds) between house mix mixing/agitation operations", "address": 32, "boardName": "Master", "boardId": 65535, "paramType": "WORD", "defaultParam": "600", "currentValue": "600", "newValue": null, "minParam": "60", "maxParam": "3600", "machineId": 112, "isPrivate": false}, {"id": 62044, "name": "Testing Mode Enable", "macro": "EEP_SILENT_MODE_ENABLE", "description": "Testing and production ONLY. Stop all asynchronous machine actions, CAN frames, and serial messages for testing purposes", "address": 80, "boardName": "Master", "boardId": 65535, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 112, "isPrivate": false}, {"id": 62045, "name": "Ingredient Mixing Enable - A3", "macro": "EEP_MIXING_ENABLE_32", "description": "Enable ingredient mixing for A3", "address": 128, "boardName": "Master", "boardId": 65535, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 112, "isPrivate": false}, {"id": 62046, "name": "Ingredient Mixing Enable - B3", "macro": "EEP_MIXING_ENABLE_33", "description": "Enable ingredient mixing for B3", "address": 129, "boardName": "Master", "boardId": 65535, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 112, "isPrivate": false}, {"id": 62047, "name": "Ingredient Mixing Enable - A4", "macro": "EEP_MIXING_ENABLE_34", "description": "Enable ingredient mixing for A4", "address": 130, "boardName": "Master", "boardId": 65535, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 112, "isPrivate": false}, {"id": 62048, "name": "Ingredient Mixing Enable - B4", "macro": "EEP_MIXING_ENABLE_35", "description": "Enable ingredient mixing for B4", "address": 131, "boardName": "Master", "boardId": 65535, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 112, "isPrivate": false}, {"id": 62049, "name": "Ingredient Mixing Enable - A5", "macro": "EEP_MIXING_ENABLE_36", "description": "Enable ingredient mixing for A5", "address": 132, "boardName": "Master", "boardId": 65535, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 112, "isPrivate": false}, {"id": 62050, "name": "Ingredient Mixing Enable - B5", "macro": "EEP_MIXING_ENABLE_37", "description": "Enable ingredient mixing for B5", "address": 133, "boardName": "Master", "boardId": 65535, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 112, "isPrivate": false}, {"id": 62051, "name": "Ingredient Mixing Enable - A6", "macro": "EEP_MIXING_ENABLE_38", "description": "Enable ingredient mixing for A6", "address": 134, "boardName": "Master", "boardId": 65535, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 112, "isPrivate": false}, {"id": 62052, "name": "Ingredient Mixing Enable - B6", "macro": "EEP_MIXING_ENABLE_39", "description": "Enable ingredient mixing for B6", "address": 135, "boardName": "Master", "boardId": 65535, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 112, "isPrivate": false}, {"id": 62053, "name": "Refresh Lines Check Period", "macro": "EEP_REFRESH_CHECK_PERIOD", "description": "Time (in seconds) between refresh line operations, if there are any to perform", "address": 320, "boardName": "Master", "boardId": 65535, "paramType": "WORD", "defaultParam": "3600", "currentValue": "3600", "newValue": null, "minParam": "300", "maxParam": "5400", "machineId": 112, "isPrivate": false}, {"id": 62054, "name": "Oz/Min Ingredient Speed - B6", "macro": "EEP_OZMIN_INGR_SPEED_3", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 412, "boardName": "Pump Board 9", "boardId": 9, "paramType": "SINGLE", "defaultParam": "20", "currentValue": "20", "newValue": null, "minParam": "7.5", "maxParam": "37.5", "machineId": 112, "isPrivate": false}, {"id": 62055, "name": "Oz/Min Ingredient Speed - B4", "macro": "EEP_OZMIN_INGR_SPEED_3", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 412, "boardName": "Pump Board 8", "boardId": 8, "paramType": "SINGLE", "defaultParam": "20", "currentValue": "20", "newValue": null, "minParam": "7.5", "maxParam": "37.5", "machineId": 112, "isPrivate": false}, {"id": 62056, "name": "Oz/Min Ingredient Speed - B2", "macro": "EEP_OZMIN_INGR_SPEED_3", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 412, "boardName": "Pump Board 7", "boardId": 7, "paramType": "SINGLE", "defaultParam": "20", "currentValue": "20", "newValue": null, "minParam": "7.5", "maxParam": "37.5", "machineId": 112, "isPrivate": false}, {"id": 62057, "name": "Oz/Min Ingredient Speed - F6", "macro": "EEP_OZMIN_INGR_SPEED_3", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 412, "boardName": "Pump Board 5", "boardId": 5, "paramType": "SINGLE", "defaultParam": "20", "currentValue": "20", "newValue": null, "minParam": "7.5", "maxParam": "37.5", "machineId": 112, "isPrivate": false}, {"id": 62058, "name": "Oz/Min Ingredient Speed - F5", "macro": "EEP_OZMIN_INGR_SPEED_3", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 412, "boardName": "Pump Board 4", "boardId": 4, "paramType": "SINGLE", "defaultParam": "20", "currentValue": "20", "newValue": null, "minParam": "7.5", "maxParam": "37.5", "machineId": 112, "isPrivate": false}, {"id": 62059, "name": "Oz/Min Ingredient Speed - F4", "macro": "EEP_OZMIN_INGR_SPEED_3", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 412, "boardName": "Pump Board 3", "boardId": 3, "paramType": "SINGLE", "defaultParam": "20", "currentValue": "20", "newValue": "0", "minParam": "7.5", "maxParam": "37.5", "machineId": 112, "isPrivate": false}, {"id": 62060, "name": "Oz/Min Ingredient Speed - F3", "macro": "EEP_OZMIN_INGR_SPEED_3", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 412, "boardName": "Pump Board 2", "boardId": 2, "paramType": "SINGLE", "defaultParam": "20", "currentValue": "20", "newValue": null, "minParam": "7.5", "maxParam": "37.5", "machineId": 112, "isPrivate": false}, {"id": 62061, "name": "Oz/Min Ingredient Speed - F2", "macro": "EEP_OZMIN_INGR_SPEED_3", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 412, "boardName": "Pump Board 1", "boardId": 1, "paramType": "SINGLE", "defaultParam": "20", "currentValue": "20", "newValue": "0", "minParam": "7.5", "maxParam": "37.5", "machineId": 112, "isPrivate": false}, {"id": 62062, "name": "<PERSON>/<PERSON> Ingredient Speed - F1", "macro": "EEP_OZMIN_INGR_SPEED_3", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 412, "boardName": "Pump Board 0", "boardId": 0, "paramType": "SINGLE", "defaultParam": "7.5", "currentValue": "7.5", "newValue": "0", "minParam": "7.5", "maxParam": "37.5", "machineId": 112, "isPrivate": false}, {"id": 62063, "name": "Oz/Min Ingredient Speed - A5", "macro": "EEP_OZMIN_INGR_SPEED_0", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 400, "boardName": "Pump Board 9", "boardId": 9, "paramType": "SINGLE", "defaultParam": "20", "currentValue": "20", "newValue": null, "minParam": "7.5", "maxParam": "37.5", "machineId": 112, "isPrivate": false}, {"id": 62064, "name": "Oz/Min Ingredient Speed - A3", "macro": "EEP_OZMIN_INGR_SPEED_0", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 400, "boardName": "Pump Board 8", "boardId": 8, "paramType": "SINGLE", "defaultParam": "20", "currentValue": "20", "newValue": "15", "minParam": "7.5", "maxParam": "37.5", "machineId": 112, "isPrivate": false}, {"id": 62065, "name": "<PERSON>/<PERSON> Ingredient Speed - A1", "macro": "EEP_OZMIN_INGR_SPEED_0", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 400, "boardName": "Pump Board 7", "boardId": 7, "paramType": "SINGLE", "defaultParam": "20", "currentValue": "20", "newValue": "0", "minParam": "7.5", "maxParam": "37.5", "machineId": 112, "isPrivate": false}, {"id": 62066, "name": "Oz/Min Ingredient Speed - C6", "macro": "EEP_OZMIN_INGR_SPEED_0", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 400, "boardName": "Pump Board 5", "boardId": 5, "paramType": "SINGLE", "defaultParam": "20", "currentValue": "20", "newValue": "15", "minParam": "7.5", "maxParam": "37.5", "machineId": 112, "isPrivate": false}, {"id": 62067, "name": "Oz/Min Ingredient Speed - C5", "macro": "EEP_OZMIN_INGR_SPEED_0", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 400, "boardName": "Pump Board 4", "boardId": 4, "paramType": "SINGLE", "defaultParam": "20", "currentValue": "20", "newValue": "0", "minParam": "7.5", "maxParam": "37.5", "machineId": 112, "isPrivate": false}, {"id": 62068, "name": "Oz/Min Ingredient Speed - C4", "macro": "EEP_OZMIN_INGR_SPEED_0", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 400, "boardName": "Pump Board 3", "boardId": 3, "paramType": "SINGLE", "defaultParam": "20", "currentValue": "20", "newValue": "15", "minParam": "7.5", "maxParam": "37.5", "machineId": 112, "isPrivate": false}, {"id": 62069, "name": "Oz/Min Ingredient Speed - C3", "macro": "EEP_OZMIN_INGR_SPEED_0", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 400, "boardName": "Pump Board 2", "boardId": 2, "paramType": "SINGLE", "defaultParam": "20", "currentValue": "20", "newValue": "0", "minParam": "7.5", "maxParam": "37.5", "machineId": 112, "isPrivate": false}, {"id": 62070, "name": "Oz/Min Ingredient Speed - C2", "macro": "EEP_OZMIN_INGR_SPEED_0", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 400, "boardName": "Pump Board 1", "boardId": 1, "paramType": "SINGLE", "defaultParam": "20", "currentValue": "20", "newValue": "0", "minParam": "7.5", "maxParam": "37.5", "machineId": 112, "isPrivate": false}, {"id": 62071, "name": "Oz/Min Ingredient Speed - C1", "macro": "EEP_OZMIN_INGR_SPEED_0", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 400, "boardName": "Pump Board 0", "boardId": 0, "paramType": "SINGLE", "defaultParam": "7.5", "currentValue": "7.5", "newValue": null, "minParam": "7.5", "maxParam": "37.5", "machineId": 112, "isPrivate": false}, {"id": 62072, "name": "Ingredient Line Status Enable (A5, B5, A6, B6)", "macro": "EEP_INGR_LINE_STATUS_EN", "description": "Enable ingredient prime status of pumped ingredients", "address": 436, "boardName": "Pump Board 9", "boardId": 9, "paramType": "BOOLEAN", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 112, "isPrivate": false}, {"id": 62073, "name": "Ingredient Line Status Enable (A3, B3, A4, B4)", "macro": "EEP_INGR_LINE_STATUS_EN", "description": "Enable ingredient prime status of pumped ingredients", "address": 436, "boardName": "Pump Board 8", "boardId": 8, "paramType": "BOOLEAN", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 112, "isPrivate": false}, {"id": 62074, "name": "Ingredient Line Status Enable (A1, B1, A2, B2)", "macro": "EEP_INGR_LINE_STATUS_EN", "description": "Enable ingredient prime status of pumped ingredients", "address": 436, "boardName": "Pump Board 7", "boardId": 7, "paramType": "BOOLEAN", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 112, "isPrivate": false}, {"id": 62075, "name": "Ingredient Line Status Enable (C6, D6, E6, F6)", "macro": "EEP_INGR_LINE_STATUS_EN", "description": "Enable ingredient prime status of pumped ingredients", "address": 436, "boardName": "Pump Board 5", "boardId": 5, "paramType": "BOOLEAN", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 112, "isPrivate": false}, {"id": 62076, "name": "Ingredient Line Status Enable (C5, D5, E5, F5)", "macro": "EEP_INGR_LINE_STATUS_EN", "description": "Enable ingredient prime status of pumped ingredients", "address": 436, "boardName": "Pump Board 4", "boardId": 4, "paramType": "BOOLEAN", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 112, "isPrivate": false}, {"id": 62077, "name": "Ingredient Line Status Enable (C4, D4, E4, F4)", "macro": "EEP_INGR_LINE_STATUS_EN", "description": "Enable ingredient prime status of pumped ingredients", "address": 436, "boardName": "Pump Board 3", "boardId": 3, "paramType": "BOOLEAN", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 112, "isPrivate": false}, {"id": 62078, "name": "Ingredient Line Status Enable (C3, D3, E3, F3)", "macro": "EEP_INGR_LINE_STATUS_EN", "description": "Enable ingredient prime status of pumped ingredients", "address": 436, "boardName": "Pump Board 2", "boardId": 2, "paramType": "BOOLEAN", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 112, "isPrivate": false}, {"id": 62079, "name": "Ingredient Line Status Enable (C2, D2, E2, F2)", "macro": "EEP_INGR_LINE_STATUS_EN", "description": "Enable ingredient prime status of pumped ingredients", "address": 436, "boardName": "Pump Board 1", "boardId": 1, "paramType": "BOOLEAN", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 112, "isPrivate": false}, {"id": 62080, "name": "Ingredient Line Status Enable (C1, D1, E1, F1)", "macro": "EEP_INGR_LINE_STATUS_EN", "description": "Enable ingredient prime status of pumped ingredients", "address": 436, "boardName": "Pump Board 0", "boardId": 0, "paramType": "BOOLEAN", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 112, "isPrivate": false}, {"id": 62081, "name": "Oz/Min Ingredient Speed - B5", "macro": "EEP_OZMIN_INGR_SPEED_1", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 404, "boardName": "Pump Board 9", "boardId": 9, "paramType": "SINGLE", "defaultParam": "20", "currentValue": "20", "newValue": "0", "minParam": "7.5", "maxParam": "37.5", "machineId": 112, "isPrivate": false}, {"id": 62082, "name": "Oz/Min Ingredient Speed - B3", "macro": "EEP_OZMIN_INGR_SPEED_1", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 404, "boardName": "Pump Board 8", "boardId": 8, "paramType": "SINGLE", "defaultParam": "20", "currentValue": "20", "newValue": "0", "minParam": "7.5", "maxParam": "37.5", "machineId": 112, "isPrivate": false}, {"id": 62083, "name": "Oz/Min Ingredient Speed - B1", "macro": "EEP_OZMIN_INGR_SPEED_1", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 404, "boardName": "Pump Board 7", "boardId": 7, "paramType": "SINGLE", "defaultParam": "20", "currentValue": "20", "newValue": null, "minParam": "7.5", "maxParam": "37.5", "machineId": 112, "isPrivate": false}, {"id": 62084, "name": "Oz/Min Ingredient Speed - D6", "macro": "EEP_OZMIN_INGR_SPEED_1", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 404, "boardName": "Pump Board 5", "boardId": 5, "paramType": "SINGLE", "defaultParam": "20", "currentValue": "20", "newValue": "0", "minParam": "7.5", "maxParam": "37.5", "machineId": 112, "isPrivate": false}, {"id": 62085, "name": "Oz/Min Ingredient Speed - D5", "macro": "EEP_OZMIN_INGR_SPEED_1", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 404, "boardName": "Pump Board 4", "boardId": 4, "paramType": "SINGLE", "defaultParam": "20", "currentValue": "20", "newValue": "0", "minParam": "7.5", "maxParam": "37.5", "machineId": 112, "isPrivate": false}, {"id": 62086, "name": "Oz/Min Ingredient Speed - D4", "macro": "EEP_OZMIN_INGR_SPEED_1", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 404, "boardName": "Pump Board 3", "boardId": 3, "paramType": "SINGLE", "defaultParam": "20", "currentValue": "20", "newValue": "0", "minParam": "7.5", "maxParam": "37.5", "machineId": 112, "isPrivate": false}, {"id": 62087, "name": "Oz/Min Ingredient Speed - D3", "macro": "EEP_OZMIN_INGR_SPEED_1", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 404, "boardName": "Pump Board 2", "boardId": 2, "paramType": "SINGLE", "defaultParam": "20", "currentValue": "20", "newValue": "0", "minParam": "7.5", "maxParam": "37.5", "machineId": 112, "isPrivate": false}, {"id": 62088, "name": "Oz/Min Ingredient Speed - D2", "macro": "EEP_OZMIN_INGR_SPEED_1", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 404, "boardName": "Pump Board 1", "boardId": 1, "paramType": "SINGLE", "defaultParam": "20", "currentValue": "20", "newValue": "15", "minParam": "7.5", "maxParam": "37.5", "machineId": 112, "isPrivate": false}, {"id": 62089, "name": "Oz/Min Ingredient Speed - D1", "macro": "EEP_OZMIN_INGR_SPEED_1", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 404, "boardName": "Pump Board 0", "boardId": 0, "paramType": "SINGLE", "defaultParam": "7.5", "currentValue": "7.5", "newValue": "0", "minParam": "7.5", "maxParam": "37.5", "machineId": 112, "isPrivate": false}, {"id": 62090, "name": "Oz/Min Ingredient Speed - A6", "macro": "EEP_OZMIN_INGR_SPEED_2", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 408, "boardName": "Pump Board 9", "boardId": 9, "paramType": "SINGLE", "defaultParam": "20", "currentValue": "20", "newValue": "15", "minParam": "7.5", "maxParam": "37.5", "machineId": 112, "isPrivate": false}, {"id": 62091, "name": "Oz/Min Ingredient Speed - A4", "macro": "EEP_OZMIN_INGR_SPEED_2", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 408, "boardName": "Pump Board 8", "boardId": 8, "paramType": "SINGLE", "defaultParam": "20", "currentValue": "20", "newValue": "0", "minParam": "7.5", "maxParam": "37.5", "machineId": 112, "isPrivate": false}, {"id": 62092, "name": "<PERSON>/Min Ingredient Speed - A2", "macro": "EEP_OZMIN_INGR_SPEED_2", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 408, "boardName": "Pump Board 7", "boardId": 7, "paramType": "SINGLE", "defaultParam": "20", "currentValue": "20", "newValue": "15", "minParam": "7.5", "maxParam": "37.5", "machineId": 112, "isPrivate": false}, {"id": 62093, "name": "Oz/Min Ingredient Speed - E6", "macro": "EEP_OZMIN_INGR_SPEED_2", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 408, "boardName": "Pump Board 5", "boardId": 5, "paramType": "SINGLE", "defaultParam": "20", "currentValue": "20", "newValue": "0", "minParam": "7.5", "maxParam": "37.5", "machineId": 112, "isPrivate": false}, {"id": 62094, "name": "Oz/Min Ingredient Speed - E5", "macro": "EEP_OZMIN_INGR_SPEED_2", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 408, "boardName": "Pump Board 4", "boardId": 4, "paramType": "SINGLE", "defaultParam": "20", "currentValue": "20", "newValue": "0", "minParam": "7.5", "maxParam": "37.5", "machineId": 112, "isPrivate": false}, {"id": 62095, "name": "Oz/Min Ingredient Speed - E4", "macro": "EEP_OZMIN_INGR_SPEED_2", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 408, "boardName": "Pump Board 3", "boardId": 3, "paramType": "SINGLE", "defaultParam": "20", "currentValue": "20", "newValue": "0", "minParam": "7.5", "maxParam": "37.5", "machineId": 112, "isPrivate": false}, {"id": 62096, "name": "Oz/Min Ingredient Speed - E3", "macro": "EEP_OZMIN_INGR_SPEED_2", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 408, "boardName": "Pump Board 2", "boardId": 2, "paramType": "SINGLE", "defaultParam": "20", "currentValue": "20", "newValue": "0", "minParam": "7.5", "maxParam": "37.5", "machineId": 112, "isPrivate": false}, {"id": 62097, "name": "Oz/Min Ingredient Speed - E2", "macro": "EEP_OZMIN_INGR_SPEED_2", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 408, "boardName": "Pump Board 1", "boardId": 1, "paramType": "SINGLE", "defaultParam": "20", "currentValue": "20", "newValue": "15", "minParam": "7.5", "maxParam": "37.5", "machineId": 112, "isPrivate": false}, {"id": 62098, "name": "Oz/Min Ingredient Speed - E1", "macro": "EEP_OZMIN_INGR_SPEED_2", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 408, "boardName": "Pump Board 0", "boardId": 0, "paramType": "SINGLE", "defaultParam": "7.5", "currentValue": "7.5", "newValue": "15", "minParam": "7.5", "maxParam": "37.5", "machineId": 112, "isPrivate": false}, {"id": 62099, "name": "Ingredient Sensor Active Threshold - C1,D1,E1,F1", "macro": "EEP_LEVEL_THRESHOLD", "description": "Ingredient level (expressed in 1/20 fl. oz. increments) below which runout detection becomes active for ingredients C1,D1,E1,F1", "address": 256, "boardName": "Pump Board 0", "boardId": 0, "paramType": "WORD", "defaultParam": "50", "currentValue": "50", "newValue": null, "minParam": "10", "maxParam": "3500", "machineId": 112, "isPrivate": false}, {"id": 62100, "name": "Ingredient Sensor Enable - C1,D1,E1,F1", "macro": "EEP_BUBBLE_DETECT_ENABLE", "description": "Enable the use of ingredient detection sensors for an entire column of ingredients", "address": 160, "boardName": "Pump Board 0", "boardId": 0, "paramType": "BOOLEAN", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 112, "isPrivate": false}, {"id": 62101, "name": "Bubble Detection Threshold - C1", "macro": "EEP_BUBBLE_ROC_THRESHOLD_0", "description": "Rate of change value required to signify a runout. (Default Oat milk = 30, almond/dairy = 50, all other 75+)", "address": 368, "boardName": "Pump Board 0", "boardId": 0, "paramType": "BYTE", "defaultParam": "50", "currentValue": "50", "newValue": null, "minParam": "15", "maxParam": "150", "machineId": 112, "isPrivate": false}, {"id": 62102, "name": "Bubble Detection Threshold - F1", "macro": "EEP_BUBBLE_ROC_THRESHOLD_3", "description": "Rate of change value required to signify a runout. (Default Oat milk = 30, almond/dairy = 50, all other 75+)", "address": 371, "boardName": "Pump Board 0", "boardId": 0, "paramType": "BYTE", "defaultParam": "50", "currentValue": "50", "newValue": "0", "minParam": "15", "maxParam": "150", "machineId": 112, "isPrivate": false}, {"id": 62103, "name": "Bubble Detection Threshold - E1", "macro": "EEP_BUBBLE_ROC_THRESHOLD_2", "description": "Rate of change value required to signify a runout. (Default Oat milk = 30, almond/dairy = 50, all other 75+)", "address": 370, "boardName": "Pump Board 0", "boardId": 0, "paramType": "BYTE", "defaultParam": "50", "currentValue": "50", "newValue": "50", "minParam": "15", "maxParam": "150", "machineId": 112, "isPrivate": false}, {"id": 62104, "name": "Bubble Detection Threshold - D1", "macro": "EEP_BUBBLE_ROC_THRESHOLD_1", "description": "Rate of change value required to signify a runout. (Default Oat milk = 30, almond/dairy = 50, all other 75+)", "address": 369, "boardName": "Pump Board 0", "boardId": 0, "paramType": "BYTE", "defaultParam": "50", "currentValue": "50", "newValue": "0", "minParam": "15", "maxParam": "150", "machineId": 112, "isPrivate": false}, {"id": 62105, "name": "Ingredient Sensor Enable - C2, E2, D2, F2", "macro": "EEP_BUBBLE_DETECT_ENABLE", "description": "Enable the use of ingredient detection sensors for an entire column of ingredients", "address": 160, "boardName": "Pump Board 1", "boardId": 1, "paramType": "BOOLEAN", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 112, "isPrivate": false}, {"id": 62106, "name": "Ingredient Sensor Active Threshold - C2, E2, D2, F2", "macro": "EEP_LEVEL_THRESHOLD", "description": "Ingredient level (expressed in 1/20 fl. oz. increments) below which runout detection becomes active for ingredients C2, E2, D2, F2", "address": 256, "boardName": "Pump Board 1", "boardId": 1, "paramType": "WORD", "defaultParam": "50", "currentValue": "50", "newValue": null, "minParam": "10", "maxParam": "3500", "machineId": 112, "isPrivate": false}, {"id": 62107, "name": "Bubble Detection Threshold - D2", "macro": "EEP_BUBBLE_ROC_THRESHOLD_1", "description": "Rate of change value required to signify a runout. (Default Oat milk = 30, almond/dairy = 50, all other 75+)", "address": 369, "boardName": "Pump Board 1", "boardId": 1, "paramType": "BYTE", "defaultParam": "50", "currentValue": "50", "newValue": "50", "minParam": "15", "maxParam": "150", "machineId": 112, "isPrivate": false}, {"id": 62108, "name": "Bubble Detection Threshold - C2", "macro": "EEP_BUBBLE_ROC_THRESHOLD_0", "description": "Rate of change value required to signify a runout. (Default Oat milk = 30, almond/dairy = 50, all other 75+)", "address": 368, "boardName": "Pump Board 1", "boardId": 1, "paramType": "BYTE", "defaultParam": "50", "currentValue": "50", "newValue": "0", "minParam": "15", "maxParam": "150", "machineId": 112, "isPrivate": false}, {"id": 62109, "name": "Bubble Detection Threshold - F2", "macro": "EEP_BUBBLE_ROC_THRESHOLD_3", "description": "Rate of change value required to signify a runout. (Default Oat milk = 30, almond/dairy = 50, all other 75+)", "address": 371, "boardName": "Pump Board 1", "boardId": 1, "paramType": "BYTE", "defaultParam": "50", "currentValue": "50", "newValue": "0", "minParam": "15", "maxParam": "150", "machineId": 112, "isPrivate": false}, {"id": 62110, "name": "Bubble Detection Threshold - E2", "macro": "EEP_BUBBLE_ROC_THRESHOLD_2", "description": "Rate of change value required to signify a runout. (Default Oat milk = 30, almond/dairy = 50, all other 75+)", "address": 370, "boardName": "Pump Board 1", "boardId": 1, "paramType": "BYTE", "defaultParam": "50", "currentValue": "50", "newValue": "50", "minParam": "15", "maxParam": "150", "machineId": 112, "isPrivate": false}, {"id": 62111, "name": "Ingredient Sensor Active Threshold - C3, D3, E3, F3", "macro": "EEP_LEVEL_THRESHOLD", "description": "Ingredient level (expressed in 1/20 fl. oz. increments) below which runout detection becomes active for ingredients C3, D3, E3, F3", "address": 256, "boardName": "Pump Board 2", "boardId": 2, "paramType": "WORD", "defaultParam": "50", "currentValue": "50", "newValue": null, "minParam": "10", "maxParam": "3500", "machineId": 112, "isPrivate": false}, {"id": 62112, "name": "Ingredient Sensor Enable - C3, D3, E3, F3", "macro": "EEP_BUBBLE_DETECT_ENABLE", "description": "Enable the use of ingredient detection sensors for an entire column of ingredients", "address": 160, "boardName": "Pump Board 2", "boardId": 2, "paramType": "BOOLEAN", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 112, "isPrivate": false}, {"id": 62113, "name": "Bubble Detection Threshold - C3", "macro": "EEP_BUBBLE_ROC_THRESHOLD_0", "description": "Rate of change value required to signify a runout. (Default Oat milk = 30, almond/dairy = 50, all other 75+)", "address": 368, "boardName": "Pump Board 2", "boardId": 2, "paramType": "BYTE", "defaultParam": "50", "currentValue": "50", "newValue": "0", "minParam": "15", "maxParam": "150", "machineId": 112, "isPrivate": false}, {"id": 62114, "name": "Bubble Detection Threshold - D3", "macro": "EEP_BUBBLE_ROC_THRESHOLD_1", "description": "Rate of change value required to signify a runout. (Default Oat milk = 30, almond/dairy = 50, all other 75+)", "address": 369, "boardName": "Pump Board 2", "boardId": 2, "paramType": "BYTE", "defaultParam": "50", "currentValue": "50", "newValue": "0", "minParam": "15", "maxParam": "150", "machineId": 112, "isPrivate": false}, {"id": 62115, "name": "Bubble Detection Threshold - F3", "macro": "EEP_BUBBLE_ROC_THRESHOLD_3", "description": "Rate of change value required to signify a runout. (Default Oat milk = 30, almond/dairy = 50, all other 75+)", "address": 371, "boardName": "Pump Board 2", "boardId": 2, "paramType": "BYTE", "defaultParam": "50", "currentValue": "50", "newValue": null, "minParam": "15", "maxParam": "150", "machineId": 112, "isPrivate": false}, {"id": 62116, "name": "Bubble Detection Threshold - E3", "macro": "EEP_BUBBLE_ROC_THRESHOLD_2", "description": "Rate of change value required to signify a runout. (Default Oat milk = 30, almond/dairy = 50, all other 75+)", "address": 370, "boardName": "Pump Board 2", "boardId": 2, "paramType": "BYTE", "defaultParam": "50", "currentValue": "50", "newValue": "0", "minParam": "15", "maxParam": "150", "machineId": 112, "isPrivate": false}, {"id": 62117, "name": "Ingredient Sensor Enable - C4, D4, E4, F4", "macro": "EEP_BUBBLE_DETECT_ENABLE", "description": "Enable the use of ingredient detection sensors for an entire column of ingredients", "address": 160, "boardName": "Pump Board 3", "boardId": 3, "paramType": "BOOLEAN", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 112, "isPrivate": false}, {"id": 62118, "name": "Ingredient Sensor Active Threshold - C4, D4, E4, F4", "macro": "EEP_LEVEL_THRESHOLD", "description": "Ingredient level (expressed in 1/20 fl. oz. increments) below which runout detection becomes active for ingredients C4, D4, E4, F4", "address": 256, "boardName": "Pump Board 3", "boardId": 3, "paramType": "WORD", "defaultParam": "50", "currentValue": "50", "newValue": null, "minParam": "10", "maxParam": "3500", "machineId": 112, "isPrivate": false}, {"id": 62119, "name": "Bubble Detection Threshold - C4", "macro": "EEP_BUBBLE_ROC_THRESHOLD_0", "description": "Rate of change value required to signify a runout. (Default Oat milk = 30, almond/dairy = 50, all other 75+)", "address": 368, "boardName": "Pump Board 3", "boardId": 3, "paramType": "BYTE", "defaultParam": "50", "currentValue": "50", "newValue": "50", "minParam": "15", "maxParam": "150", "machineId": 112, "isPrivate": false}, {"id": 62120, "name": "Bubble Detection Threshold - D4", "macro": "EEP_BUBBLE_ROC_THRESHOLD_1", "description": "Rate of change value required to signify a runout. (Default Oat milk = 30, almond/dairy = 50, all other 75+)", "address": 369, "boardName": "Pump Board 3", "boardId": 3, "paramType": "BYTE", "defaultParam": "50", "currentValue": "50", "newValue": "0", "minParam": "15", "maxParam": "150", "machineId": 112, "isPrivate": false}, {"id": 62121, "name": "Bubble Detection Threshold - E4", "macro": "EEP_BUBBLE_ROC_THRESHOLD_2", "description": "Rate of change value required to signify a runout. (Default Oat milk = 30, almond/dairy = 50, all other 75+)", "address": 370, "boardName": "Pump Board 3", "boardId": 3, "paramType": "BYTE", "defaultParam": "50", "currentValue": "50", "newValue": "0", "minParam": "15", "maxParam": "150", "machineId": 112, "isPrivate": false}, {"id": 62122, "name": "Bubble Detection Threshold - F4", "macro": "EEP_BUBBLE_ROC_THRESHOLD_3", "description": "Rate of change value required to signify a runout. (Default Oat milk = 30, almond/dairy = 50, all other 75+)", "address": 371, "boardName": "Pump Board 3", "boardId": 3, "paramType": "BYTE", "defaultParam": "50", "currentValue": "50", "newValue": "0", "minParam": "15", "maxParam": "150", "machineId": 112, "isPrivate": false}, {"id": 62123, "name": "Ingredient Sensor Enable - C5, D5, E5, F5", "macro": "EEP_BUBBLE_DETECT_ENABLE", "description": "Enable the use of ingredient detection sensors for an entire column of ingredients", "address": 160, "boardName": "Pump Board 4", "boardId": 4, "paramType": "BOOLEAN", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 112, "isPrivate": false}, {"id": 62124, "name": "Ingredient Sensor Active Threshold - C5, D5, E5, F5", "macro": "EEP_LEVEL_THRESHOLD", "description": "Ingredient level (expressed in 1/20 fl. oz. increments) below which runout detection becomes active for ingredients C5, D5, E5, F5", "address": 256, "boardName": "Pump Board 4", "boardId": 4, "paramType": "WORD", "defaultParam": "50", "currentValue": "50", "newValue": null, "minParam": "10", "maxParam": "3500", "machineId": 112, "isPrivate": false}, {"id": 62125, "name": "Bubble Detection Threshold - C5", "macro": "EEP_BUBBLE_ROC_THRESHOLD_0", "description": "Rate of change value required to signify a runout. (Default Oat milk = 30, almond/dairy = 50, all other 75+)", "address": 368, "boardName": "Pump Board 4", "boardId": 4, "paramType": "BYTE", "defaultParam": "50", "currentValue": "50", "newValue": "0", "minParam": "15", "maxParam": "150", "machineId": 112, "isPrivate": false}, {"id": 62126, "name": "Bubble Detection Threshold - D5", "macro": "EEP_BUBBLE_ROC_THRESHOLD_1", "description": "Rate of change value required to signify a runout. (Default Oat milk = 30, almond/dairy = 50, all other 75+)", "address": 369, "boardName": "Pump Board 4", "boardId": 4, "paramType": "BYTE", "defaultParam": "50", "currentValue": "50", "newValue": "0", "minParam": "15", "maxParam": "150", "machineId": 112, "isPrivate": false}, {"id": 62127, "name": "Bubble Detection Threshold - F5", "macro": "EEP_BUBBLE_ROC_THRESHOLD_3", "description": "Rate of change value required to signify a runout. (Default Oat milk = 30, almond/dairy = 50, all other 75+)", "address": 371, "boardName": "Pump Board 4", "boardId": 4, "paramType": "BYTE", "defaultParam": "50", "currentValue": "50", "newValue": null, "minParam": "15", "maxParam": "150", "machineId": 112, "isPrivate": false}, {"id": 62128, "name": "Ingredient Sensor Enable - C6, D6, E6, F6", "macro": "EEP_BUBBLE_DETECT_ENABLE", "description": "Enable the use of ingredient detection sensors for an entire column of ingredients", "address": 160, "boardName": "Pump Board 5", "boardId": 5, "paramType": "BOOLEAN", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 112, "isPrivate": false}, {"id": 62129, "name": "Ingredient Sensor Active Threshold - C6, D6, E6, F6", "macro": "EEP_LEVEL_THRESHOLD", "description": "Ingredient level (expressed in 1/20 fl. oz. increments) below which runout detection becomes active for ingredients C6, D6, E6, F6", "address": 256, "boardName": "Pump Board 5", "boardId": 5, "paramType": "WORD", "defaultParam": "50", "currentValue": "50", "newValue": null, "minParam": "10", "maxParam": "3500", "machineId": 112, "isPrivate": false}, {"id": 62130, "name": "Bubble Detection Threshold - C6", "macro": "EEP_BUBBLE_ROC_THRESHOLD_0", "description": "Rate of change value required to signify a runout. (Default Oat milk = 30, almond/dairy = 50, all other 75+)", "address": 368, "boardName": "Pump Board 5", "boardId": 5, "paramType": "BYTE", "defaultParam": "50", "currentValue": "50", "newValue": "50", "minParam": "15", "maxParam": "150", "machineId": 112, "isPrivate": false}, {"id": 62131, "name": "Bubble Detection Threshold - E6", "macro": "EEP_BUBBLE_ROC_THRESHOLD_2", "description": "Rate of change value required to signify a runout. (Default Oat milk = 30, almond/dairy = 50, all other 75+)", "address": 370, "boardName": "Pump Board 5", "boardId": 5, "paramType": "BYTE", "defaultParam": "50", "currentValue": "50", "newValue": "0", "minParam": "15", "maxParam": "150", "machineId": 112, "isPrivate": false}, {"id": 62132, "name": "Bubble Detection Threshold - D6", "macro": "EEP_BUBBLE_ROC_THRESHOLD_1", "description": "Rate of change value required to signify a runout. (Default Oat milk = 30, almond/dairy = 50, all other 75+)", "address": 369, "boardName": "Pump Board 5", "boardId": 5, "paramType": "BYTE", "defaultParam": "50", "currentValue": "50", "newValue": "0", "minParam": "15", "maxParam": "150", "machineId": 112, "isPrivate": false}, {"id": 62133, "name": "Bubble Detection Threshold - F6", "macro": "EEP_BUBBLE_ROC_THRESHOLD_3", "description": "Rate of change value required to signify a runout. (Default Oat milk = 30, almond/dairy = 50, all other 75+)", "address": 371, "boardName": "Pump Board 5", "boardId": 5, "paramType": "BYTE", "defaultParam": "50", "currentValue": "50", "newValue": null, "minParam": "15", "maxParam": "150", "machineId": 112, "isPrivate": false}, {"id": 62134, "name": "Ingredient Sensor Enable - A1, A2, B1, B2", "macro": "EEP_BUBBLE_DETECT_ENABLE", "description": "Enable the use of ingredient detection sensors for a group of 4 house mix ingredients", "address": 160, "boardName": "Pump Board 7", "boardId": 7, "paramType": "BOOLEAN", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 112, "isPrivate": false}, {"id": 62135, "name": "Ingredient Sensor Active Threshold - A1, A2, B1, B2", "macro": "EEP_LEVEL_THRESHOLD", "description": "Ingredient level (expressed in 1/20 fl. oz. increments) below which runout detection becomes active for ingredients A1, A2, B1, B2", "address": 256, "boardName": "Pump Board 7", "boardId": 7, "paramType": "WORD", "defaultParam": "50", "currentValue": "50", "newValue": null, "minParam": "10", "maxParam": "3500", "machineId": 112, "isPrivate": false}, {"id": 62136, "name": "Bubble Detection Threshold - A1", "macro": "EEP_BUBBLE_ROC_THRESHOLD_0", "description": "Rate of change value required to signify a runout. (Default Oat milk = 30, almond/dairy = 50, all other 75+)", "address": 368, "boardName": "Pump Board 7", "boardId": 7, "paramType": "BYTE", "defaultParam": "50", "currentValue": "50", "newValue": "0", "minParam": "15", "maxParam": "150", "machineId": 112, "isPrivate": false}, {"id": 62137, "name": "Bubble Detection Threshold - B2", "macro": "EEP_BUBBLE_ROC_THRESHOLD_3", "description": "Rate of change value required to signify a runout. (Default Oat milk = 30, almond/dairy = 50, all other 75+)", "address": 371, "boardName": "Pump Board 7", "boardId": 7, "paramType": "BYTE", "defaultParam": "50", "currentValue": "50", "newValue": null, "minParam": "15", "maxParam": "150", "machineId": 112, "isPrivate": false}, {"id": 62138, "name": "Bubble Detection Threshold - A2", "macro": "EEP_BUBBLE_ROC_THRESHOLD_2", "description": "Rate of change value required to signify a runout. (Default Oat milk = 30, almond/dairy = 50, all other 75+)", "address": 370, "boardName": "Pump Board 7", "boardId": 7, "paramType": "BYTE", "defaultParam": "50", "currentValue": "50", "newValue": "50", "minParam": "15", "maxParam": "150", "machineId": 112, "isPrivate": false}, {"id": 62139, "name": "Bubble Detection Threshold - B1", "macro": "EEP_BUBBLE_ROC_THRESHOLD_1", "description": "Rate of change value required to signify a runout. (Default Oat milk = 30, almond/dairy = 50, all other 75+)", "address": 369, "boardName": "Pump Board 7", "boardId": 7, "paramType": "BYTE", "defaultParam": "50", "currentValue": "50", "newValue": null, "minParam": "15", "maxParam": "150", "machineId": 112, "isPrivate": false}, {"id": 62140, "name": "Ingredient Sensor Enable - A3, A4, B3, B4", "macro": "EEP_BUBBLE_DETECT_ENABLE", "description": "Enable the use of ingredient detection sensors for a group of 4 house mix ingredients", "address": 160, "boardName": "Pump Board 8", "boardId": 8, "paramType": "BOOLEAN", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 112, "isPrivate": false}, {"id": 62141, "name": "Ingredient Sensor Active Threshold - A3, A4, B3, B4", "macro": "EEP_LEVEL_THRESHOLD", "description": "Ingredient level (expressed in 1/20 fl. oz. increments) below which runout detection becomes active for ingredients A3, A4, B3, B4", "address": 256, "boardName": "Pump Board 8", "boardId": 8, "paramType": "WORD", "defaultParam": "50", "currentValue": "50", "newValue": null, "minParam": "10", "maxParam": "3500", "machineId": 112, "isPrivate": false}, {"id": 62142, "name": "Bubble Detection Threshold - B4", "macro": "EEP_BUBBLE_ROC_THRESHOLD_3", "description": "Rate of change value required to signify a runout. (Default Oat milk = 30, almond/dairy = 50, all other 75+)", "address": 371, "boardName": "Pump Board 8", "boardId": 8, "paramType": "BYTE", "defaultParam": "50", "currentValue": "50", "newValue": null, "minParam": "15", "maxParam": "150", "machineId": 112, "isPrivate": false}, {"id": 62143, "name": "Bubble Detection Threshold - B3", "macro": "EEP_BUBBLE_ROC_THRESHOLD_1", "description": "Rate of change value required to signify a runout. (Default Oat milk = 30, almond/dairy = 50, all other 75+)", "address": 369, "boardName": "Pump Board 8", "boardId": 8, "paramType": "BYTE", "defaultParam": "50", "currentValue": "50", "newValue": "0", "minParam": "15", "maxParam": "150", "machineId": 112, "isPrivate": false}, {"id": 62144, "name": "Bubble Detection Threshold - A3", "macro": "EEP_BUBBLE_ROC_THRESHOLD_0", "description": "Rate of change value required to signify a runout. (Default Oat milk = 30, almond/dairy = 50, all other 75+)", "address": 368, "boardName": "Pump Board 8", "boardId": 8, "paramType": "BYTE", "defaultParam": "50", "currentValue": "50", "newValue": "50", "minParam": "15", "maxParam": "150", "machineId": 112, "isPrivate": false}, {"id": 62145, "name": "Bubble Detection Threshold - A4", "macro": "EEP_BUBBLE_ROC_THRESHOLD_2", "description": "Rate of change value required to signify a runout. (Default Oat milk = 30, almond/dairy = 50, all other 75+)", "address": 370, "boardName": "Pump Board 8", "boardId": 8, "paramType": "BYTE", "defaultParam": "50", "currentValue": "50", "newValue": "0", "minParam": "15", "maxParam": "150", "machineId": 112, "isPrivate": false}, {"id": 62146, "name": "Ingredient Sensor Active Threshold - A5, A6, B5, B6", "macro": "EEP_LEVEL_THRESHOLD", "description": "Ingredient level (expressed in 1/20 fl. oz. increments) below which runout detection becomes active for ingredients A5, A6, B5, B6", "address": 256, "boardName": "Pump Board 9", "boardId": 9, "paramType": "WORD", "defaultParam": "50", "currentValue": "50", "newValue": null, "minParam": "10", "maxParam": "3500", "machineId": 112, "isPrivate": false}, {"id": 62147, "name": "Ingredient Sensor Enable - A5, A6, B5, B6", "macro": "EEP_BUBBLE_DETECT_ENABLE", "description": "Enable the use of ingredient detection sensors for a group of 4 house mix ingredients", "address": 160, "boardName": "Pump Board 9", "boardId": 9, "paramType": "BOOLEAN", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 112, "isPrivate": false}, {"id": 62148, "name": "Bubble Detection Threshold - A5", "macro": "EEP_BUBBLE_ROC_THRESHOLD_0", "description": "Rate of change value required to signify a runout. (Default Oat milk = 30, almond/dairy = 50, all other 75+)", "address": 368, "boardName": "Pump Board 9", "boardId": 9, "paramType": "BYTE", "defaultParam": "50", "currentValue": "50", "newValue": null, "minParam": "15", "maxParam": "150", "machineId": 112, "isPrivate": false}, {"id": 62149, "name": "Bubble Detection Threshold - B5", "macro": "EEP_BUBBLE_ROC_THRESHOLD_1", "description": "Rate of change value required to signify a runout. (Default Oat milk = 30, almond/dairy = 50, all other 75+)", "address": 369, "boardName": "Pump Board 9", "boardId": 9, "paramType": "BYTE", "defaultParam": "50", "currentValue": "50", "newValue": "0", "minParam": "15", "maxParam": "150", "machineId": 112, "isPrivate": false}, {"id": 62150, "name": "Bubble Detection Threshold - B6", "macro": "EEP_BUBBLE_ROC_THRESHOLD_3", "description": "Rate of change value required to signify a runout. (Default Oat milk = 30, almond/dairy = 50, all other 75+)", "address": 371, "boardName": "Pump Board 9", "boardId": 9, "paramType": "BYTE", "defaultParam": "50", "currentValue": "50", "newValue": null, "minParam": "15", "maxParam": "150", "machineId": 112, "isPrivate": false}, {"id": 62151, "name": "Bubble Detection Threshold - A6", "macro": "EEP_BUBBLE_ROC_THRESHOLD_2", "description": "Rate of change value required to signify a runout. (Default Oat milk = 30, almond/dairy = 50, all other 75+)", "address": 370, "boardName": "Pump Board 9", "boardId": 9, "paramType": "BYTE", "defaultParam": "50", "currentValue": "50", "newValue": "50", "minParam": "15", "maxParam": "150", "machineId": 112, "isPrivate": false}, {"id": 62152, "name": "Lower Temperature Threshold (Bottom Temp Sensor)", "macro": "EEP_LOWER_TEMP_THRESHOLD_BOT", "description": "Temperature of the bottom fridge sensor (in degrees Fahrenheit) below which the compressor is turned OFF", "address": 228, "boardName": "Cooling", "boardId": 576, "paramType": "SINGLE", "defaultParam": "36", "currentValue": "36", "newValue": null, "minParam": "32", "maxParam": "46", "machineId": 112, "isPrivate": false}, {"id": 62153, "name": "<PERSON><PERSON> Defrost Timeout", "macro": "EEP_DEFROST_TIMEOUT", "description": "Time that the compressor is forced to stay off during a defrost cycle (in seconds)", "address": 240, "boardName": "Cooling", "boardId": 576, "paramType": "WORD", "defaultParam": "3600", "currentValue": "3600", "newValue": null, "minParam": "1800", "maxParam": "43200", "machineId": 112, "isPrivate": false}, {"id": 62154, "name": "Refresh Batching Enable", "macro": "EEP_ENABLE_REFRESH_BATCHING ", "description": "Refreshes happen max once/hr", "address": 368, "boardName": "Master", "boardId": 65535, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 112, "isPrivate": false}, {"id": 62156, "name": "Refresh Sync Enable", "macro": "EEP_ENABLE_REFRESH_SYNC", "description": "Attempt to set refresh schedule to the top of the hour every hour. If the top of the next hour is unknown, continue to refresh every hour", "address": 369, "boardName": "Master", "boardId": 65535, "paramType": "BOOLEAN", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 112, "isPrivate": false}, {"id": 62157, "name": "Bubble Detection Threshold - E5", "macro": "EEP_BUBBLE_ROC_THRESHOLD_2", "description": "Rate of change value required to signify a runout. (Default Oat milk = 30, almond/dairy = 50, all other 75+)", "address": 370, "boardName": "Pump Board 4", "boardId": 4, "paramType": "BYTE", "defaultParam": "50", "currentValue": "50", "newValue": "0", "minParam": "15", "maxParam": "150", "machineId": 112, "isPrivate": false}, {"id": 62158, "name": "Compressor Normal Runtime Max", "macro": "EEP_COMPRESSOR_NORMAL_RT_MAX", "description": "Continuous time that the compressor can be enabled before sending a runtime alert", "address": 208, "boardName": "Cooling", "boardId": 576, "paramType": "WORD", "defaultParam": "43200", "currentValue": "43200", "newValue": null, "minParam": "900", "maxParam": "10800", "machineId": 112, "isPrivate": false}, {"id": 62161, "name": "Refrigeration Scheduled OFF Time", "macro": "EEP_COMPRESSOR_STATIC_OFF", "description": "Time the compressor is turned OFF for static, scheduled compressor operation (in seconds)", "address": 144, "boardName": "Cooling", "boardId": 576, "paramType": "WORD", "defaultParam": "1800", "currentValue": "1800", "newValue": null, "minParam": "300", "maxParam": "7200", "machineId": 112, "isPrivate": false}, {"id": 62162, "name": "Refrigeration Scheduled ON Time", "macro": "EEP_COMPRESSOR_STATIC_ON", "description": "Time the compressor is turned ON for static, scheduled compressor operation (in seconds)", "address": 128, "boardName": "Cooling", "boardId": 576, "paramType": "WORD", "defaultParam": "1800", "currentValue": "1800", "newValue": null, "minParam": "300", "maxParam": "7200", "machineId": 112, "isPrivate": false}, {"id": 62163, "name": "Allow Retraction A1, A2, B1, B2", "macro": "EEP_ALLOW_RETRACTION", "description": "Allow user-configurable retraction for this board's ingredients", "address": 276, "boardName": "Pump Board 7", "boardId": 7, "paramType": "BOOLEAN", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 112, "isPrivate": false}, {"id": 62164, "name": "Allow Retraction A3, A4, B3, B4", "macro": "EEP_ALLOW_RETRACTION", "description": "Allow user-configurable retraction for this board's ingredients", "address": 276, "boardName": "Pump Board 8", "boardId": 8, "paramType": "BOOLEAN", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 112, "isPrivate": false}, {"id": 62165, "name": "Allow Retraction A5, A6, B5, B6", "macro": "EEP_ALLOW_RETRACTION", "description": "Allow user-configurable retraction for this board's ingredients", "address": 276, "boardName": "Pump Board 9", "boardId": 9, "paramType": "BOOLEAN", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 112, "isPrivate": false}, {"id": 62166, "name": "Allow Retraction C1, D1, E1, F1", "macro": "EEP_ALLOW_RETRACTION", "description": "Allow user-configurable retraction for this board's ingredients", "address": 276, "boardName": "Pump Board 0", "boardId": 0, "paramType": "BOOLEAN", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 112, "isPrivate": false}, {"id": 62167, "name": "Allow Retraction C2, E2, D2, F2", "macro": "EEP_ALLOW_RETRACTION", "description": "Allow user-configurable retraction for this board's ingredients", "address": 276, "boardName": "Pump Board 1", "boardId": 1, "paramType": "BOOLEAN", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 112, "isPrivate": false}, {"id": 62168, "name": "Allow Retraction C3, D3, E3, F3", "macro": "EEP_ALLOW_RETRACTION", "description": "Allow user-configurable retraction for this board's ingredients", "address": 276, "boardName": "Pump Board 2", "boardId": 2, "paramType": "BOOLEAN", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 112, "isPrivate": false}, {"id": 62169, "name": "Allow Retraction C4, D4, E4, F4", "macro": "EEP_ALLOW_RETRACTION", "description": "Allow user-configurable retraction for this board's ingredients", "address": 276, "boardName": "Pump Board 3", "boardId": 3, "paramType": "BOOLEAN", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 112, "isPrivate": false}, {"id": 62170, "name": "Allow Retraction C5, D5, E5, F5", "macro": "EEP_ALLOW_RETRACTION", "description": "Allow user-configurable retraction for this board's ingredients", "address": 276, "boardName": "Pump Board 4", "boardId": 4, "paramType": "BOOLEAN", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 112, "isPrivate": false}, {"id": 62171, "name": "Allow Retraction C6, D6, E6, F6", "macro": "EEP_ALLOW_RETRACTION", "description": "Allow user-configurable retraction for this board's ingredients", "address": 276, "boardName": "Pump Board 5", "boardId": 5, "paramType": "BOOLEAN", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 112, "isPrivate": false}, {"id": 62172, "name": "Bubble Detection - A1", "macro": "EEP_BUBBLE_DETECTION_0", "description": "Flag indicating whether a bubble/air is detected in A1's dispensing line", "address": 16, "boardName": "Pump Board 7", "boardId": 7, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 112, "isPrivate": true}, {"id": 62173, "name": "Bubble Detection - A2", "macro": "EEP_BUBBLE_DETECTION_2", "description": "Flag indicating whether a bubble/air is detected in A2's dispensing line", "address": 48, "boardName": "Pump Board 7", "boardId": 7, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 112, "isPrivate": true}, {"id": 62174, "name": "Bubble Detection - A3", "macro": "EEP_BUBBLE_DETECTION_0", "description": "Flag indicating whether a bubble/air is detected in A3's dispensing line", "address": 16, "boardName": "Pump Board 8", "boardId": 8, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 112, "isPrivate": true}, {"id": 62175, "name": "Bubble Detection - A4", "macro": "EEP_BUBBLE_DETECTION_2", "description": "Flag indicating whether a bubble/air is detected in A4's dispensing line", "address": 48, "boardName": "Pump Board 8", "boardId": 8, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 112, "isPrivate": true}, {"id": 62176, "name": "Bubble Detection - A5", "macro": "EEP_BUBBLE_DETECTION_0", "description": "Flag indicating whether a bubble/air is detected in A5's dispensing line", "address": 16, "boardName": "Pump Board 9", "boardId": 9, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 112, "isPrivate": true}, {"id": 62177, "name": "Bubble Detection - A6", "macro": "EEP_BUBBLE_DETECTION_2", "description": "Flag indicating whether a bubble/air is detected in A6's dispensing line", "address": 48, "boardName": "Pump Board 9", "boardId": 9, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 112, "isPrivate": true}, {"id": 62178, "name": "Bubble Detection - B1", "macro": "EEP_BUBBLE_DETECTION_1", "description": "Flag indicating whether a bubble/air is detected in B1's dispensing line", "address": 32, "boardName": "Pump Board 7", "boardId": 7, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 112, "isPrivate": true}, {"id": 62179, "name": "Bubble Detection - B2", "macro": "EEP_BUBBLE_DETECTION_3", "description": "Flag indicating whether a bubble/air is detected in B2's dispensing line", "address": 64, "boardName": "Pump Board 7", "boardId": 7, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 112, "isPrivate": true}, {"id": 62180, "name": "Bubble Detection - B3", "macro": "EEP_BUBBLE_DETECTION_1", "description": "Flag indicating whether a bubble/air is detected in B3's dispensing line", "address": 32, "boardName": "Pump Board 8", "boardId": 8, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 112, "isPrivate": true}, {"id": 62181, "name": "Bubble Detection - B4", "macro": "EEP_BUBBLE_DETECTION_3", "description": "Flag indicating whether a bubble/air is detected in B4's dispensing line", "address": 64, "boardName": "Pump Board 8", "boardId": 8, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 112, "isPrivate": true}, {"id": 62182, "name": "Bubble Detection - B5", "macro": "EEP_BUBBLE_DETECTION_1", "description": "Flag indicating whether a bubble/air is detected in B5's dispensing line", "address": 32, "boardName": "Pump Board 9", "boardId": 9, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 112, "isPrivate": true}, {"id": 62183, "name": "Bubble Detection - B6", "macro": "EEP_BUBBLE_DETECTION_3", "description": "Flag indicating whether a bubble/air is detected in B6's dispensing line", "address": 64, "boardName": "Pump Board 9", "boardId": 9, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 112, "isPrivate": true}, {"id": 62184, "name": "Bubble Detection - C1", "macro": "EEP_BUBBLE_DETECTION_0", "description": "Flag indicating whether a bubble/air is detected in C1's dispensing line", "address": 16, "boardName": "Pump Board 0", "boardId": 0, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 112, "isPrivate": true}, {"id": 62185, "name": "Bubble Detection - C2", "macro": "EEP_BUBBLE_DETECTION_0", "description": "Flag indicating whether a bubble/air is detected in C2's dispensing line", "address": 16, "boardName": "Pump Board 1", "boardId": 1, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 112, "isPrivate": true}, {"id": 62186, "name": "Bubble Detection - C3", "macro": "EEP_BUBBLE_DETECTION_0", "description": "Flag indicating whether a bubble/air is detected in C3's dispensing line", "address": 16, "boardName": "Pump Board 2", "boardId": 2, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 112, "isPrivate": true}, {"id": 62187, "name": "Bubble Detection - C4", "macro": "EEP_BUBBLE_DETECTION_0", "description": "Flag indicating whether a bubble/air is detected in C4's dispensing line", "address": 16, "boardName": "Pump Board 3", "boardId": 3, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 112, "isPrivate": true}, {"id": 62188, "name": "Bubble Detection - C5", "macro": "EEP_BUBBLE_DETECTION_0", "description": "Flag indicating whether a bubble/air is detected in C5's dispensing line", "address": 16, "boardName": "Pump Board 4", "boardId": 4, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 112, "isPrivate": true}, {"id": 62189, "name": "Bubble Detection - C6", "macro": "EEP_BUBBLE_DETECTION_0", "description": "Flag indicating whether a bubble/air is detected in C6's dispensing line", "address": 16, "boardName": "Pump Board 5", "boardId": 5, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 112, "isPrivate": true}, {"id": 62190, "name": "Bubble Detection - D1", "macro": "EEP_BUBBLE_DETECTION_1", "description": "Flag indicating whether a bubble/air is detected in D1's dispensing line", "address": 32, "boardName": "Pump Board 0", "boardId": 0, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 112, "isPrivate": true}, {"id": 62191, "name": "Bubble Detection - D2", "macro": "EEP_BUBBLE_DETECTION_1", "description": "Flag indicating whether a bubble/air is detected in D2's dispensing line", "address": 32, "boardName": "Pump Board 1", "boardId": 1, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 112, "isPrivate": true}, {"id": 62192, "name": "Bubble Detection - D3", "macro": "EEP_BUBBLE_DETECTION_1", "description": "Flag indicating whether a bubble/air is detected in D3's dispensing line", "address": 32, "boardName": "Pump Board 2", "boardId": 2, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 112, "isPrivate": true}, {"id": 62193, "name": "Bubble Detection - D4", "macro": "EEP_BUBBLE_DETECTION_1", "description": "Flag indicating whether a bubble/air is detected in D4's dispensing line", "address": 32, "boardName": "Pump Board 3", "boardId": 3, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 112, "isPrivate": true}, {"id": 62194, "name": "Bubble Detection - D5", "macro": "EEP_BUBBLE_DETECTION_1", "description": "Flag indicating whether a bubble/air is detected in D5's dispensing line", "address": 32, "boardName": "Pump Board 4", "boardId": 4, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 112, "isPrivate": true}, {"id": 62195, "name": "Bubble Detection - D6", "macro": "EEP_BUBBLE_DETECTION_1", "description": "Flag indicating whether a bubble/air is detected in D6's dispensing line", "address": 32, "boardName": "Pump Board 5", "boardId": 5, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 112, "isPrivate": true}, {"id": 62196, "name": "Bubble Detection - E1", "macro": "EEP_BUBBLE_DETECTION_2", "description": "Flag indicating whether a bubble/air is detected in E1's dispensing line", "address": 48, "boardName": "Pump Board 0", "boardId": 0, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 112, "isPrivate": true}, {"id": 62197, "name": "Bubble Detection - E2", "macro": "EEP_BUBBLE_DETECTION_2", "description": "Flag indicating whether a bubble/air is detected in E2's dispensing line", "address": 48, "boardName": "Pump Board 1", "boardId": 1, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 112, "isPrivate": true}, {"id": 62198, "name": "Bubble Detection - E3", "macro": "EEP_BUBBLE_DETECTION_2", "description": "Flag indicating whether a bubble/air is detected in E3's dispensing line", "address": 48, "boardName": "Pump Board 2", "boardId": 2, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 112, "isPrivate": true}, {"id": 62199, "name": "Bubble Detection - E4", "macro": "EEP_BUBBLE_DETECTION_2", "description": "Flag indicating whether a bubble/air is detected in E4's dispensing line", "address": 48, "boardName": "Pump Board 3", "boardId": 3, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 112, "isPrivate": true}, {"id": 62200, "name": "Bubble Detection - E5", "macro": "EEP_BUBBLE_DETECTION_2", "description": "Flag indicating whether a bubble/air is detected in E5's dispensing line", "address": 48, "boardName": "Pump Board 4", "boardId": 4, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 112, "isPrivate": true}, {"id": 62201, "name": "Bubble Detection - E6", "macro": "EEP_BUBBLE_DETECTION_2", "description": "Flag indicating whether a bubble/air is detected in E6's dispensing line", "address": 48, "boardName": "Pump Board 5", "boardId": 5, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 112, "isPrivate": true}, {"id": 62202, "name": "Bubble Detection - F1", "macro": "EEP_BUBBLE_DETECTION_3", "description": "Flag indicating whether a bubble/air is detected in F1's dispensing line", "address": 64, "boardName": "Pump Board 0", "boardId": 0, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 112, "isPrivate": true}, {"id": 62203, "name": "Bubble Detection - F2", "macro": "EEP_BUBBLE_DETECTION_3", "description": "Flag indicating whether a bubble/air is detected in F2's dispensing line", "address": 64, "boardName": "Pump Board 1", "boardId": 1, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 112, "isPrivate": true}, {"id": 62204, "name": "Bubble Detection - F3", "macro": "EEP_BUBBLE_DETECTION_3", "description": "Flag indicating whether a bubble/air is detected in F3's dispensing line", "address": 64, "boardName": "Pump Board 2", "boardId": 2, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 112, "isPrivate": true}, {"id": 62205, "name": "Bubble Detection - F4", "macro": "EEP_BUBBLE_DETECTION_3", "description": "Flag indicating whether a bubble/air is detected in F4's dispensing line", "address": 64, "boardName": "Pump Board 3", "boardId": 3, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 112, "isPrivate": true}, {"id": 62206, "name": "Bubble Detection - F5", "macro": "EEP_BUBBLE_DETECTION_3", "description": "Flag indicating whether a bubble/air is detected in F5's dispensing line", "address": 64, "boardName": "Pump Board 4", "boardId": 4, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 112, "isPrivate": true}, {"id": 62207, "name": "Bubble Detection - F6", "macro": "EEP_BUBBLE_DETECTION_3", "description": "Flag indicating whether a bubble/air is detected in F6's dispensing line", "address": 64, "boardName": "Pump Board 5", "boardId": 5, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 112, "isPrivate": true}, {"id": 62208, "name": "Calibration Factor - Bib 1", "macro": "EEP_BIB_0_CAL_VAL", "description": "Ingredient calibration factor (Number of ms per fluid oz)", "address": 16, "boardName": "Nozzle", "boardId": 560, "paramType": "WORD", "defaultParam": "2000", "currentValue": "2000", "newValue": null, "minParam": "100", "maxParam": "10000", "machineId": 112, "isPrivate": true}, {"id": 62209, "name": "Calibration Factor - Bib 2", "macro": "EEP_BIB_1_CAL_VAL", "description": "Ingredient calibration factor (Number of ms per fluid oz)", "address": 32, "boardName": "Nozzle", "boardId": 560, "paramType": "WORD", "defaultParam": "2000", "currentValue": "2000", "newValue": null, "minParam": "100", "maxParam": "10000", "machineId": 112, "isPrivate": true}, {"id": 62210, "name": "Calibration Factor - Bib 3", "macro": "EEP_BIB_2_CAL_VAL", "description": "Ingredient calibration factor (Number of ms per fluid oz)", "address": 48, "boardName": "Nozzle", "boardId": 560, "paramType": "WORD", "defaultParam": "2000", "currentValue": "2000", "newValue": null, "minParam": "100", "maxParam": "10000", "machineId": 112, "isPrivate": true}, {"id": 62211, "name": "Calibration Factor - Bib 4", "macro": "EEP_BIB_3_CAL_VAL", "description": "Ingredient calibration factor (Number of ms per fluid oz)", "address": 64, "boardName": "Nozzle", "boardId": 560, "paramType": "WORD", "defaultParam": "2000", "currentValue": "2000", "newValue": null, "minParam": "100", "maxParam": "10000", "machineId": 112, "isPrivate": true}, {"id": 62212, "name": "Calibration Factor - Bib 5", "macro": "EEP_BIB_0_CAL_VAL", "description": "Ingredient calibration factor (Number of ms per fluid oz)", "address": 16, "boardName": "Solenoid", "boardId": 12, "paramType": "WORD", "defaultParam": "2000", "currentValue": "2000", "newValue": null, "minParam": "100", "maxParam": "10000", "machineId": 112, "isPrivate": true}, {"id": 62213, "name": "Calibration Factor - Bib 6", "macro": "EEP_BIB_1_CAL_VAL", "description": "Ingredient calibration factor (Number of ms per fluid oz)", "address": 32, "boardName": "Solenoid", "boardId": 12, "paramType": "WORD", "defaultParam": "2000", "currentValue": "2000", "newValue": null, "minParam": "100", "maxParam": "10000", "machineId": 112, "isPrivate": true}, {"id": 62214, "name": "Calibration Factor - Bib 7", "macro": "EEP_BIB_2_CAL_VAL", "description": "Ingredient calibration factor (Number of ms per fluid oz)", "address": 48, "boardName": "Solenoid", "boardId": 12, "paramType": "WORD", "defaultParam": "2000", "currentValue": "784", "newValue": null, "minParam": "100", "maxParam": "10000", "machineId": 112, "isPrivate": true}, {"id": 62215, "name": "Calibration Factor - Bib 8", "macro": "EEP_BIB_3_CAL_VAL", "description": "Ingredient calibration factor (Number of ms per fluid oz)", "address": 64, "boardName": "Solenoid", "boardId": 12, "paramType": "WORD", "defaultParam": "2000", "currentValue": "2000", "newValue": null, "minParam": "100", "maxParam": "10000", "machineId": 112, "isPrivate": true}, {"id": 62216, "name": "Calibration Factor - Soda Bib", "macro": "EEP_SODA_CAL_VAL", "description": "Ingredient calibration factor (Number of ms per fluid oz)", "address": 80, "boardName": "Nozzle", "boardId": 560, "paramType": "WORD", "defaultParam": "2000", "currentValue": "493", "newValue": null, "minParam": "100", "maxParam": "10000", "machineId": 112, "isPrivate": true}, {"id": 62217, "name": "Calibration Factor - Water Bib", "macro": "EEP_WATER_CAL_VAL", "description": "Ingredient calibration factor (Number of ms per fluid oz)", "address": 96, "boardName": "Nozzle", "boardId": 560, "paramType": "WORD", "defaultParam": "2000", "currentValue": "1000", "newValue": null, "minParam": "100", "maxParam": "10000", "machineId": 112, "isPrivate": true}, {"id": 62218, "name": "Dispensing Calibration Factor - A1", "macro": "EEP_INGR_CALIBRATION_FACTOR_0", "description": "Ingredient calibration factor (Number of pulses per fluid oz)", "address": 384, "boardName": "Pump Board 7", "boardId": 7, "paramType": "DWORD", "defaultParam": "48600", "currentValue": "48600", "newValue": "0", "minParam": "9720", "maxParam": "194400", "machineId": 112, "isPrivate": true}, {"id": 62219, "name": "Dispensing Calibration Factor - A2", "macro": "EEP_INGR_CALIBRATION_FACTOR_2", "description": "Ingredient calibration factor (Number of pulses per fluid oz)", "address": 392, "boardName": "Pump Board 7", "boardId": 7, "paramType": "DWORD", "defaultParam": "48600", "currentValue": "48600", "newValue": "48600", "minParam": "9720", "maxParam": "194400", "machineId": 112, "isPrivate": true}, {"id": 62220, "name": "Dispensing Calibration Factor - A3", "macro": "EEP_INGR_CALIBRATION_FACTOR_0", "description": "Ingredient calibration factor (Number of pulses per fluid oz)", "address": 384, "boardName": "Pump Board 8", "boardId": 8, "paramType": "DWORD", "defaultParam": "48600", "currentValue": "48600", "newValue": "48600", "minParam": "9720", "maxParam": "194400", "machineId": 112, "isPrivate": true}, {"id": 62221, "name": "Dispensing Calibration Factor - A4", "macro": "EEP_INGR_CALIBRATION_FACTOR_2", "description": "Ingredient calibration factor (Number of pulses per fluid oz)", "address": 392, "boardName": "Pump Board 8", "boardId": 8, "paramType": "DWORD", "defaultParam": "48600", "currentValue": "48600", "newValue": "0", "minParam": "9720", "maxParam": "194400", "machineId": 112, "isPrivate": true}, {"id": 62222, "name": "Dispensing Calibration Factor - A5", "macro": "EEP_INGR_CALIBRATION_FACTOR_0", "description": "Ingredient calibration factor (Number of pulses per fluid oz)", "address": 384, "boardName": "Pump Board 9", "boardId": 9, "paramType": "DWORD", "defaultParam": "48600", "currentValue": "81000", "newValue": null, "minParam": "9720", "maxParam": "194400", "machineId": 112, "isPrivate": true}, {"id": 62223, "name": "Dispensing Calibration Factor - A6", "macro": "EEP_INGR_CALIBRATION_FACTOR_2", "description": "Ingredient calibration factor (Number of pulses per fluid oz)", "address": 392, "boardName": "Pump Board 9", "boardId": 9, "paramType": "DWORD", "defaultParam": "48600", "currentValue": "48600", "newValue": "48600", "minParam": "9720", "maxParam": "194400", "machineId": 112, "isPrivate": true}, {"id": 62224, "name": "Dispensing Calibration Factor - B1", "macro": "EEP_INGR_CALIBRATION_FACTOR_1", "description": "Ingredient calibration factor (Number of pulses per fluid oz)", "address": 388, "boardName": "Pump Board 7", "boardId": 7, "paramType": "DWORD", "defaultParam": "48600", "currentValue": "48600", "newValue": null, "minParam": "9720", "maxParam": "194400", "machineId": 112, "isPrivate": true}, {"id": 62225, "name": "Dispensing Calibration Factor - B2", "macro": "EEP_INGR_CALIBRATION_FACTOR_3", "description": "Ingredient calibration factor (Number of pulses per fluid oz)", "address": 396, "boardName": "Pump Board 7", "boardId": 7, "paramType": "DWORD", "defaultParam": "48600", "currentValue": "48600", "newValue": null, "minParam": "9720", "maxParam": "194400", "machineId": 112, "isPrivate": true}, {"id": 62226, "name": "Dispensing Calibration Factor - B3", "macro": "EEP_INGR_CALIBRATION_FACTOR_1", "description": "Ingredient calibration factor (Number of pulses per fluid oz)", "address": 388, "boardName": "Pump Board 8", "boardId": 8, "paramType": "DWORD", "defaultParam": "48600", "currentValue": "48600", "newValue": "0", "minParam": "9720", "maxParam": "194400", "machineId": 112, "isPrivate": true}, {"id": 62227, "name": "Dispensing Calibration Factor - B4", "macro": "EEP_INGR_CALIBRATION_FACTOR_3", "description": "Ingredient calibration factor (Number of pulses per fluid oz)", "address": 396, "boardName": "Pump Board 8", "boardId": 8, "paramType": "DWORD", "defaultParam": "48600", "currentValue": "48600", "newValue": null, "minParam": "9720", "maxParam": "194400", "machineId": 112, "isPrivate": true}, {"id": 62228, "name": "Dispensing Calibration Factor - B5", "macro": "EEP_INGR_CALIBRATION_FACTOR_1", "description": "Ingredient calibration factor (Number of pulses per fluid oz)", "address": 388, "boardName": "Pump Board 9", "boardId": 9, "paramType": "DWORD", "defaultParam": "48600", "currentValue": "48600", "newValue": "0", "minParam": "9720", "maxParam": "194400", "machineId": 112, "isPrivate": true}, {"id": 62229, "name": "Dispensing Calibration Factor - B6", "macro": "EEP_INGR_CALIBRATION_FACTOR_3", "description": "Ingredient calibration factor (Number of pulses per fluid oz)", "address": 396, "boardName": "Pump Board 9", "boardId": 9, "paramType": "DWORD", "defaultParam": "48600", "currentValue": "48600", "newValue": null, "minParam": "9720", "maxParam": "194400", "machineId": 112, "isPrivate": true}, {"id": 62230, "name": "Dispensing Calibration Factor - C1", "macro": "EEP_INGR_CALIBRATION_FACTOR_0", "description": "Ingredient calibration factor (Number of pulses per fluid oz)", "address": 384, "boardName": "Pump Board 0", "boardId": 0, "paramType": "DWORD", "defaultParam": "24300", "currentValue": "34700", "newValue": null, "minParam": "9720", "maxParam": "194400", "machineId": 112, "isPrivate": true}, {"id": 62231, "name": "Dispensing Calibration Factor - C2", "macro": "EEP_INGR_CALIBRATION_FACTOR_0", "description": "Ingredient calibration factor (Number of pulses per fluid oz)", "address": 384, "boardName": "Pump Board 1", "boardId": 1, "paramType": "DWORD", "defaultParam": "48600", "currentValue": "48600", "newValue": "0", "minParam": "9720", "maxParam": "194400", "machineId": 112, "isPrivate": true}, {"id": 62232, "name": "Dispensing Calibration Factor - C3", "macro": "EEP_INGR_CALIBRATION_FACTOR_0", "description": "Ingredient calibration factor (Number of pulses per fluid oz)", "address": 384, "boardName": "Pump Board 2", "boardId": 2, "paramType": "DWORD", "defaultParam": "48600", "currentValue": "48600", "newValue": "0", "minParam": "9720", "maxParam": "194400", "machineId": 112, "isPrivate": true}, {"id": 62233, "name": "Dispensing Calibration Factor - C4", "macro": "EEP_INGR_CALIBRATION_FACTOR_0", "description": "Ingredient calibration factor (Number of pulses per fluid oz)", "address": 384, "boardName": "Pump Board 3", "boardId": 3, "paramType": "DWORD", "defaultParam": "48600", "currentValue": "48600", "newValue": "48600", "minParam": "9720", "maxParam": "194400", "machineId": 112, "isPrivate": true}, {"id": 62234, "name": "Dispensing Calibration Factor - C5", "macro": "EEP_INGR_CALIBRATION_FACTOR_0", "description": "Ingredient calibration factor (Number of pulses per fluid oz)", "address": 384, "boardName": "Pump Board 4", "boardId": 4, "paramType": "DWORD", "defaultParam": "48600", "currentValue": "48600", "newValue": "0", "minParam": "9720", "maxParam": "194400", "machineId": 112, "isPrivate": true}, {"id": 62235, "name": "Dispensing Calibration Factor - C6", "macro": "EEP_INGR_CALIBRATION_FACTOR_0", "description": "Ingredient calibration factor (Number of pulses per fluid oz)", "address": 384, "boardName": "Pump Board 5", "boardId": 5, "paramType": "DWORD", "defaultParam": "48600", "currentValue": "48600", "newValue": "48600", "minParam": "9720", "maxParam": "194400", "machineId": 112, "isPrivate": true}, {"id": 62236, "name": "Dispensing Calibration Factor - D1", "macro": "EEP_INGR_CALIBRATION_FACTOR_1", "description": "Ingredient calibration factor (Number of pulses per fluid oz)", "address": 388, "boardName": "Pump Board 0", "boardId": 0, "paramType": "DWORD", "defaultParam": "24300", "currentValue": "24920", "newValue": "0", "minParam": "9720", "maxParam": "194400", "machineId": 112, "isPrivate": true}, {"id": 62237, "name": "Dispensing Calibration Factor - D2", "macro": "EEP_INGR_CALIBRATION_FACTOR_1", "description": "Ingredient calibration factor (Number of pulses per fluid oz)", "address": 388, "boardName": "Pump Board 1", "boardId": 1, "paramType": "DWORD", "defaultParam": "48600", "currentValue": "48600", "newValue": "48600", "minParam": "9720", "maxParam": "194400", "machineId": 112, "isPrivate": true}, {"id": 62238, "name": "Dispensing Calibration Factor - D3", "macro": "EEP_INGR_CALIBRATION_FACTOR_1", "description": "Ingredient calibration factor (Number of pulses per fluid oz)", "address": 388, "boardName": "Pump Board 2", "boardId": 2, "paramType": "DWORD", "defaultParam": "48600", "currentValue": "48600", "newValue": "0", "minParam": "9720", "maxParam": "194400", "machineId": 112, "isPrivate": true}, {"id": 62239, "name": "Dispensing Calibration Factor - D4", "macro": "EEP_INGR_CALIBRATION_FACTOR_1", "description": "Ingredient calibration factor (Number of pulses per fluid oz)", "address": 388, "boardName": "Pump Board 3", "boardId": 3, "paramType": "DWORD", "defaultParam": "48600", "currentValue": "48600", "newValue": "0", "minParam": "9720", "maxParam": "194400", "machineId": 112, "isPrivate": true}, {"id": 62240, "name": "Dispensing Calibration Factor - D5", "macro": "EEP_INGR_CALIBRATION_FACTOR_1", "description": "Ingredient calibration factor (Number of pulses per fluid oz)", "address": 388, "boardName": "Pump Board 4", "boardId": 4, "paramType": "DWORD", "defaultParam": "48600", "currentValue": "54000", "newValue": "0", "minParam": "9720", "maxParam": "194400", "machineId": 112, "isPrivate": true}, {"id": 62241, "name": "Dispensing Calibration Factor - D6", "macro": "EEP_INGR_CALIBRATION_FACTOR_1", "description": "Ingredient calibration factor (Number of pulses per fluid oz)", "address": 388, "boardName": "Pump Board 5", "boardId": 5, "paramType": "DWORD", "defaultParam": "48600", "currentValue": "48600", "newValue": "0", "minParam": "9720", "maxParam": "194400", "machineId": 112, "isPrivate": true}, {"id": 62242, "name": "Dispensing Calibration Factor - E1", "macro": "EEP_INGR_CALIBRATION_FACTOR_2", "description": "Ingredient calibration factor (Number of pulses per fluid oz)", "address": 392, "boardName": "Pump Board 0", "boardId": 0, "paramType": "DWORD", "defaultParam": "24300", "currentValue": "27000", "newValue": "48600", "minParam": "9720", "maxParam": "194400", "machineId": 112, "isPrivate": true}, {"id": 62243, "name": "Dispensing Calibration Factor - E2", "macro": "EEP_INGR_CALIBRATION_FACTOR_2", "description": "Ingredient calibration factor (Number of pulses per fluid oz)", "address": 392, "boardName": "Pump Board 1", "boardId": 1, "paramType": "DWORD", "defaultParam": "48600", "currentValue": "48600", "newValue": "48600", "minParam": "9720", "maxParam": "194400", "machineId": 112, "isPrivate": true}, {"id": 62244, "name": "Dispensing Calibration Factor - E3", "macro": "EEP_INGR_CALIBRATION_FACTOR_2", "description": "Ingredient calibration factor (Number of pulses per fluid oz)", "address": 392, "boardName": "Pump Board 2", "boardId": 2, "paramType": "DWORD", "defaultParam": "48600", "currentValue": "48600", "newValue": "0", "minParam": "9720", "maxParam": "194400", "machineId": 112, "isPrivate": true}, {"id": 62245, "name": "Dispensing Calibration Factor - E4", "macro": "EEP_INGR_CALIBRATION_FACTOR_2", "description": "Ingredient calibration factor (Number of pulses per fluid oz)", "address": 392, "boardName": "Pump Board 3", "boardId": 3, "paramType": "DWORD", "defaultParam": "48600", "currentValue": "48600", "newValue": "0", "minParam": "9720", "maxParam": "194400", "machineId": 112, "isPrivate": true}, {"id": 62246, "name": "Dispensing Calibration Factor - E5", "macro": "EEP_INGR_CALIBRATION_FACTOR_2", "description": "Ingredient calibration factor (Number of pulses per fluid oz)", "address": 392, "boardName": "Pump Board 4", "boardId": 4, "paramType": "DWORD", "defaultParam": "48600", "currentValue": "48600", "newValue": "0", "minParam": "9720", "maxParam": "194400", "machineId": 112, "isPrivate": true}, {"id": 62247, "name": "Dispensing Calibration Factor - E6", "macro": "EEP_INGR_CALIBRATION_FACTOR_2", "description": "Ingredient calibration factor (Number of pulses per fluid oz)", "address": 392, "boardName": "Pump Board 5", "boardId": 5, "paramType": "DWORD", "defaultParam": "48600", "currentValue": "48600", "newValue": "0", "minParam": "9720", "maxParam": "194400", "machineId": 112, "isPrivate": true}, {"id": 62248, "name": "Dispensing Calibration Factor - F1", "macro": "EEP_INGR_CALIBRATION_FACTOR_3", "description": "Ingredient calibration factor (Number of pulses per fluid oz)", "address": 396, "boardName": "Pump Board 0", "boardId": 0, "paramType": "DWORD", "defaultParam": "24300", "currentValue": "24300", "newValue": "0", "minParam": "9720", "maxParam": "194400", "machineId": 112, "isPrivate": true}, {"id": 62249, "name": "Dispensing Calibration Factor - F2", "macro": "EEP_INGR_CALIBRATION_FACTOR_3", "description": "Ingredient calibration factor (Number of pulses per fluid oz)", "address": 396, "boardName": "Pump Board 1", "boardId": 1, "paramType": "DWORD", "defaultParam": "48600", "currentValue": "48600", "newValue": "0", "minParam": "9720", "maxParam": "194400", "machineId": 112, "isPrivate": true}, {"id": 62250, "name": "Dispensing Calibration Factor - F3", "macro": "EEP_INGR_CALIBRATION_FACTOR_3", "description": "Ingredient calibration factor (Number of pulses per fluid oz)", "address": 396, "boardName": "Pump Board 2", "boardId": 2, "paramType": "DWORD", "defaultParam": "48600", "currentValue": "48600", "newValue": null, "minParam": "9720", "maxParam": "194400", "machineId": 112, "isPrivate": true}, {"id": 62251, "name": "Dispensing Calibration Factor - F4", "macro": "EEP_INGR_CALIBRATION_FACTOR_3", "description": "Ingredient calibration factor (Number of pulses per fluid oz)", "address": 396, "boardName": "Pump Board 3", "boardId": 3, "paramType": "DWORD", "defaultParam": "48600", "currentValue": "48600", "newValue": "0", "minParam": "9720", "maxParam": "194400", "machineId": 112, "isPrivate": true}, {"id": 62252, "name": "Dispensing Calibration Factor - F5", "macro": "EEP_INGR_CALIBRATION_FACTOR_3", "description": "Ingredient calibration factor (Number of pulses per fluid oz)", "address": 396, "boardName": "Pump Board 4", "boardId": 4, "paramType": "DWORD", "defaultParam": "48600", "currentValue": "54000", "newValue": null, "minParam": "9720", "maxParam": "194400", "machineId": 112, "isPrivate": true}, {"id": 62253, "name": "Dispensing Calibration Factor - F6", "macro": "EEP_INGR_CALIBRATION_FACTOR_3", "description": "Ingredient calibration factor (Number of pulses per fluid oz)", "address": 396, "boardName": "Pump Board 5", "boardId": 5, "paramType": "DWORD", "defaultParam": "48600", "currentValue": "48600", "newValue": null, "minParam": "9720", "maxParam": "194400", "machineId": 112, "isPrivate": true}, {"id": 62254, "name": "Ingredient Line Status - A1", "macro": "EEP_INGR_LINE_STATUS_0", "description": "Bitfield for current ingredient prime status of pumped ingredients", "address": 432, "boardName": "Pump Board 7", "boardId": 7, "paramType": "BYTE", "defaultParam": "0", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 112, "isPrivate": true}, {"id": 62255, "name": "Ingredient Line Status - A2", "macro": "EEP_INGR_LINE_STATUS_2", "description": "Bitfield for current ingredient prime status of pumped ingredients", "address": 434, "boardName": "Pump Board 7", "boardId": 7, "paramType": "BYTE", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 112, "isPrivate": true}, {"id": 62256, "name": "Ingredient Line Status - A3", "macro": "EEP_INGR_LINE_STATUS_0", "description": "Bitfield for current ingredient prime status of pumped ingredients", "address": 432, "boardName": "Pump Board 8", "boardId": 8, "paramType": "BYTE", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 112, "isPrivate": true}, {"id": 62257, "name": "Ingredient Line Status - A4", "macro": "EEP_INGR_LINE_STATUS_2", "description": "Bitfield for current ingredient prime status of pumped ingredients", "address": 434, "boardName": "Pump Board 8", "boardId": 8, "paramType": "BYTE", "defaultParam": "0", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 112, "isPrivate": true}, {"id": 62258, "name": "Ingredient Line Status - A5", "macro": "EEP_INGR_LINE_STATUS_0", "description": "Bitfield for current ingredient prime status of pumped ingredients", "address": 432, "boardName": "Pump Board 9", "boardId": 9, "paramType": "BYTE", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 112, "isPrivate": true}, {"id": 62259, "name": "Ingredient Line Status - B2", "macro": "EEP_INGR_LINE_STATUS_3", "description": "Bitfield for current ingredient prime status of pumped ingredients", "address": 435, "boardName": "Pump Board 7", "boardId": 7, "paramType": "BYTE", "defaultParam": "0", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 112, "isPrivate": true}, {"id": 62260, "name": "Ingredient Line Status - B3", "macro": "EEP_INGR_LINE_STATUS_1", "description": "Bitfield for current ingredient prime status of pumped ingredients", "address": 433, "boardName": "Pump Board 8", "boardId": 8, "paramType": "BYTE", "defaultParam": "0", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 112, "isPrivate": true}, {"id": 62261, "name": "Ingredient Line Status - B4", "macro": "EEP_INGR_LINE_STATUS_3", "description": "Bitfield for current ingredient prime status of pumped ingredients", "address": 435, "boardName": "Pump Board 8", "boardId": 8, "paramType": "BYTE", "defaultParam": "0", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 112, "isPrivate": true}, {"id": 62262, "name": "Ingredient Line Status - B5", "macro": "EEP_INGR_LINE_STATUS_1", "description": "Bitfield for current ingredient prime status of pumped ingredients", "address": 433, "boardName": "Pump Board 9", "boardId": 9, "paramType": "BYTE", "defaultParam": "0", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 112, "isPrivate": true}, {"id": 62263, "name": "Ingredient Line Status - B6", "macro": "EEP_INGR_LINE_STATUS_3", "description": "Bitfield for current ingredient prime status of pumped ingredients", "address": 435, "boardName": "Pump Board 9", "boardId": 9, "paramType": "BYTE", "defaultParam": "0", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 112, "isPrivate": true}, {"id": 62264, "name": "Ingredient Line Status - C1", "macro": "EEP_INGR_LINE_STATUS_0", "description": "Bitfield for current ingredient prime status of pumped ingredients", "address": 432, "boardName": "Pump Board 0", "boardId": 0, "paramType": "BYTE", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 112, "isPrivate": true}, {"id": 62265, "name": "Ingredient Line Status - C2", "macro": "EEP_INGR_LINE_STATUS_0", "description": "Bitfield for current ingredient prime status of pumped ingredients", "address": 432, "boardName": "Pump Board 1", "boardId": 1, "paramType": "BYTE", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 112, "isPrivate": true}, {"id": 62266, "name": "Ingredient Line Status - C3", "macro": "EEP_INGR_LINE_STATUS_0", "description": "Bitfield for current ingredient prime status of pumped ingredients", "address": 432, "boardName": "Pump Board 2", "boardId": 2, "paramType": "BYTE", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 112, "isPrivate": true}, {"id": 62267, "name": "Ingredient Line Status - C4", "macro": "EEP_INGR_LINE_STATUS_0", "description": "Bitfield for current ingredient prime status of pumped ingredients", "address": 432, "boardName": "Pump Board 3", "boardId": 3, "paramType": "BYTE", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 112, "isPrivate": true}, {"id": 62268, "name": "Ingredient Line Status - C5", "macro": "EEP_INGR_LINE_STATUS_0", "description": "Bitfield for current ingredient prime status of pumped ingredients", "address": 432, "boardName": "Pump Board 4", "boardId": 4, "paramType": "BYTE", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 112, "isPrivate": true}, {"id": 62269, "name": "Ingredient Line Status - C6", "macro": "EEP_INGR_LINE_STATUS_0", "description": "Bitfield for current ingredient prime status of pumped ingredients", "address": 432, "boardName": "Pump Board 5", "boardId": 5, "paramType": "BYTE", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 112, "isPrivate": true}, {"id": 62270, "name": "Ingredient Line Status - D1", "macro": "EEP_INGR_LINE_STATUS_1", "description": "Bitfield for current ingredient prime status of pumped ingredients", "address": 433, "boardName": "Pump Board 0", "boardId": 0, "paramType": "BYTE", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 112, "isPrivate": true}, {"id": 62271, "name": "Ingredient Line Status - D2", "macro": "EEP_INGR_LINE_STATUS_1", "description": "Bitfield for current ingredient prime status of pumped ingredients", "address": 433, "boardName": "Pump Board 1", "boardId": 1, "paramType": "BYTE", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 112, "isPrivate": true}, {"id": 62272, "name": "Ingredient Line Status - D3", "macro": "EEP_INGR_LINE_STATUS_1", "description": "Bitfield for current ingredient prime status of pumped ingredients", "address": 433, "boardName": "Pump Board 2", "boardId": 2, "paramType": "BYTE", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 112, "isPrivate": true}, {"id": 62273, "name": "Ingredient Line Status - D4", "macro": "EEP_INGR_LINE_STATUS_1", "description": "Bitfield for current ingredient prime status of pumped ingredients", "address": 433, "boardName": "Pump Board 3", "boardId": 3, "paramType": "BYTE", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 112, "isPrivate": true}, {"id": 62274, "name": "Ingredient Line Status - D5", "macro": "EEP_INGR_LINE_STATUS_1", "description": "Bitfield for current ingredient prime status of pumped ingredients", "address": 433, "boardName": "Pump Board 4", "boardId": 4, "paramType": "BYTE", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 112, "isPrivate": true}, {"id": 62275, "name": "Ingredient Line Status - D6", "macro": "EEP_INGR_LINE_STATUS_1", "description": "Bitfield for current ingredient prime status of pumped ingredients", "address": 433, "boardName": "Pump Board 5", "boardId": 5, "paramType": "BYTE", "defaultParam": "0", "currentValue": "5", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 112, "isPrivate": true}, {"id": 62276, "name": "Ingredient Line Status - E1", "macro": "EEP_INGR_LINE_STATUS_2", "description": "Bitfield for current ingredient prime status of pumped ingredients", "address": 434, "boardName": "Pump Board 0", "boardId": 0, "paramType": "BYTE", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 112, "isPrivate": true}, {"id": 62277, "name": "Ingredient Line Status - E2", "macro": "EEP_INGR_LINE_STATUS_2", "description": "Bitfield for current ingredient prime status of pumped ingredients", "address": 434, "boardName": "Pump Board 1", "boardId": 1, "paramType": "BYTE", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 112, "isPrivate": true}, {"id": 62278, "name": "Ingredient Line Status - E3", "macro": "EEP_INGR_LINE_STATUS_2", "description": "Bitfield for current ingredient prime status of pumped ingredients", "address": 434, "boardName": "Pump Board 2", "boardId": 2, "paramType": "BYTE", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 112, "isPrivate": true}, {"id": 62279, "name": "Ingredient Line Status - E4", "macro": "EEP_INGR_LINE_STATUS_2", "description": "Bitfield for current ingredient prime status of pumped ingredients", "address": 434, "boardName": "Pump Board 3", "boardId": 3, "paramType": "BYTE", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 112, "isPrivate": true}, {"id": 62280, "name": "Ingredient Line Status - E5", "macro": "EEP_INGR_LINE_STATUS_2", "description": "Bitfield for current ingredient prime status of pumped ingredients", "address": 434, "boardName": "Pump Board 4", "boardId": 4, "paramType": "BYTE", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 112, "isPrivate": true}, {"id": 62281, "name": "Ingredient Line Status - E6", "macro": "EEP_INGR_LINE_STATUS_2", "description": "Bitfield for current ingredient prime status of pumped ingredients", "address": 434, "boardName": "Pump Board 5", "boardId": 5, "paramType": "BYTE", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 112, "isPrivate": true}, {"id": 62282, "name": "Ingredient Line Status - F1", "macro": "EEP_INGR_LINE_STATUS_3", "description": "Bitfield for current ingredient prime status of pumped ingredients", "address": 435, "boardName": "Pump Board 0", "boardId": 0, "paramType": "BYTE", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 112, "isPrivate": true}, {"id": 62283, "name": "Ingredient Line Status - F2", "macro": "EEP_INGR_LINE_STATUS_3", "description": "Bitfield for current ingredient prime status of pumped ingredients", "address": 435, "boardName": "Pump Board 1", "boardId": 1, "paramType": "BYTE", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 112, "isPrivate": true}, {"id": 62284, "name": "Ingredient Line Status - F3", "macro": "EEP_INGR_LINE_STATUS_3", "description": "Bitfield for current ingredient prime status of pumped ingredients", "address": 435, "boardName": "Pump Board 2", "boardId": 2, "paramType": "BYTE", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 112, "isPrivate": true}, {"id": 62285, "name": "Ingredient Line Status - F4", "macro": "EEP_INGR_LINE_STATUS_3", "description": "Bitfield for current ingredient prime status of pumped ingredients", "address": 435, "boardName": "Pump Board 3", "boardId": 3, "paramType": "BYTE", "defaultParam": "0", "currentValue": "5", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 112, "isPrivate": true}, {"id": 62286, "name": "Ingredient Line Status - F5", "macro": "EEP_INGR_LINE_STATUS_3", "description": "Bitfield for current ingredient prime status of pumped ingredients", "address": 435, "boardName": "Pump Board 4", "boardId": 4, "paramType": "BYTE", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 112, "isPrivate": true}, {"id": 62287, "name": "Ingredient Line Status - F6", "macro": "EEP_INGR_LINE_STATUS_3", "description": "Bitfield for current ingredient prime status of pumped ingredients", "address": 435, "boardName": "Pump Board 5", "boardId": 5, "paramType": "BYTE", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 112, "isPrivate": true}, {"id": 62288, "name": "Ingredient Line Status -A6", "macro": "EEP_INGR_LINE_STATUS_2", "description": "Bitfield for current ingredient prime status of pumped ingredients", "address": 434, "boardName": "Pump Board 9", "boardId": 9, "paramType": "BYTE", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 112, "isPrivate": true}, {"id": 62289, "name": "Ingredient Line Status -B1", "macro": "EEP_INGR_LINE_STATUS_1", "description": "Bitfield for current ingredient prime status of pumped ingredients", "address": 433, "boardName": "Pump Board 7", "boardId": 7, "paramType": "BYTE", "defaultParam": "0", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 112, "isPrivate": true}, {"id": 62290, "name": "Ingredient Mixing Enable - C1", "macro": "EEP_MIXING_ENABLE_0", "description": "Enable ingredient mixing for A1", "address": 96, "boardName": "Master", "boardId": 65535, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 112, "isPrivate": true}, {"id": 62291, "name": "Ingredient Mixing Enable - C2", "macro": "EEP_MIXING_ENABLE_4", "description": "Enable ingredient mixing for B1", "address": 100, "boardName": "Master", "boardId": 65535, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 112, "isPrivate": true}, {"id": 62292, "name": "Ingredient Mixing Enable - C3", "macro": "EEP_MIXING_ENABLE_7", "description": "Enable ingredient mixing for C1", "address": 104, "boardName": "Master", "boardId": 65535, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 112, "isPrivate": true}, {"id": 62293, "name": "Ingredient Mixing Enable - C4", "macro": "EEP_MIXING_ENABLE_12", "description": "Enable ingredient mixing for D1", "address": 108, "boardName": "Master", "boardId": 65535, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 112, "isPrivate": true}, {"id": 62294, "name": "Ingredient Mixing Enable - C5", "macro": "EEP_MIXING_ENABLE_16", "description": "Enable ingredient mixing for E1", "address": 112, "boardName": "Master", "boardId": 65535, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 112, "isPrivate": true}, {"id": 62295, "name": "Ingredient Mixing Enable - C6", "macro": "EEP_MIXING_ENABLE_20", "description": "Enable ingredient mixing for F1", "address": 116, "boardName": "Master", "boardId": 65535, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 112, "isPrivate": true}, {"id": 62296, "name": "Ingredient Mixing Enable - D1", "macro": "EEP_MIXING_ENABLE_1", "description": "Enable ingredient mixing for A2", "address": 97, "boardName": "Master", "boardId": 65535, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 112, "isPrivate": true}, {"id": 62297, "name": "Ingredient Mixing Enable - D2", "macro": "EEP_MIXING_ENABLE_5", "description": "Enable ingredient mixing for B2", "address": 101, "boardName": "Master", "boardId": 65535, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 112, "isPrivate": true}, {"id": 62298, "name": "Ingredient Mixing Enable - D3", "macro": "EEP_MIXING_ENABLE_9", "description": "Enable ingredient mixing for C2", "address": 105, "boardName": "Master", "boardId": 65535, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 112, "isPrivate": true}, {"id": 62299, "name": "Ingredient Mixing Enable - D4", "macro": "EEP_MIXING_ENABLE_13", "description": "Enable ingredient mixing for D2", "address": 109, "boardName": "Master", "boardId": 65535, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 112, "isPrivate": true}, {"id": 62300, "name": "Ingredient Mixing Enable - D5", "macro": "EEP_MIXING_ENABLE_17", "description": "Enable ingredient mixing for E2", "address": 113, "boardName": "Master", "boardId": 65535, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 112, "isPrivate": true}, {"id": 62301, "name": "Ingredient Mixing Enable - D6", "macro": "EEP_MIXING_ENABLE_21", "description": "Enable ingredient mixing for F2", "address": 117, "boardName": "Master", "boardId": 65535, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 112, "isPrivate": true}, {"id": 62302, "name": "Ingredient Mixing Enable - E1", "macro": "EEP_MIXING_ENABLE_2", "description": "Enable ingredient mixing for A3", "address": 98, "boardName": "Master", "boardId": 65535, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 112, "isPrivate": true}, {"id": 62303, "name": "Ingredient Mixing Enable - E2", "macro": "EEP_MIXING_ENABLE_6", "description": "Enable ingredient mixing for B3", "address": 102, "boardName": "Master", "boardId": 65535, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 112, "isPrivate": true}, {"id": 62304, "name": "Ingredient Mixing Enable - E3", "macro": "EEP_MIXING_ENABLE_10", "description": "Enable ingredient mixing for C3", "address": 106, "boardName": "Master", "boardId": 65535, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 112, "isPrivate": true}, {"id": 62305, "name": "Ingredient Mixing Enable - E4", "macro": "EEP_MIXING_ENABLE_14", "description": "Enable ingredient mixing for D3", "address": 110, "boardName": "Master", "boardId": 65535, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 112, "isPrivate": true}, {"id": 62306, "name": "Ingredient Mixing Enable - E5", "macro": "EEP_MIXING_ENABLE_18", "description": "Enable ingredient mixing for E3", "address": 114, "boardName": "Master", "boardId": 65535, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 112, "isPrivate": true}, {"id": 62307, "name": "Ingredient Mixing Enable - E6", "macro": "EEP_MIXING_ENABLE_22", "description": "Enable ingredient mixing for F3", "address": 118, "boardName": "Master", "boardId": 65535, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 112, "isPrivate": true}, {"id": 62308, "name": "Ingredient Mixing Enable - F1", "macro": "EEP_MIXING_ENABLE_3", "description": "Enable ingredient mixing for A4", "address": 99, "boardName": "Master", "boardId": 65535, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 112, "isPrivate": true}, {"id": 62309, "name": "Ingredient Mixing Enable - F2", "macro": "EEP_MIXING_ENABLE_7", "description": "Enable ingredient mixing for B4", "address": 103, "boardName": "Master", "boardId": 65535, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 112, "isPrivate": true}, {"id": 62310, "name": "Ingredient Mixing Enable - F3", "macro": "EEP_MIXING_ENABLE_11", "description": "Enable ingredient mixing for C4", "address": 107, "boardName": "Master", "boardId": 65535, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 112, "isPrivate": true}, {"id": 62311, "name": "Ingredient Mixing Enable - F4", "macro": "EEP_MIXING_ENABLE_15", "description": "Enable ingredient mixing for D4", "address": 111, "boardName": "Master", "boardId": 65535, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 112, "isPrivate": true}, {"id": 62312, "name": "Ingredient Mixing Enable - F5", "macro": "EEP_MIXING_ENABLE_19", "description": "Enable ingredient mixing for E4", "address": 115, "boardName": "Master", "boardId": 65535, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 112, "isPrivate": true}, {"id": 62313, "name": "Ingredient Mixing Enable - F6", "macro": "EEP_MIXING_ENABLE_23", "description": "Enable ingredient mixing for F4", "address": 119, "boardName": "Master", "boardId": 65535, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 112, "isPrivate": true}, {"id": 62314, "name": "Ingredient Mixing Enable - G1", "macro": "EEP_MIXING_ENABLE_24", "description": "Enable ingredient mixing for G1", "address": 120, "boardName": "Master", "boardId": 65535, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 112, "isPrivate": true}, {"id": 62315, "name": "Ingredient Mixing Enable - G2", "macro": "EEP_MIXING_ENABLE_25", "description": "Enable ingredient mixing for G2", "address": 121, "boardName": "Master", "boardId": 65535, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 112, "isPrivate": true}, {"id": 62316, "name": "Ingredient Mixing Enable - G3", "macro": "EEP_MIXING_ENABLE_26", "description": "Enable ingredient mixing for G3", "address": 122, "boardName": "Master", "boardId": 65535, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 112, "isPrivate": true}, {"id": 62317, "name": "Ingredient Mixing Enable - G4", "macro": "EEP_MIXING_ENABLE_27", "description": "Enable ingredient mixing for G4", "address": 123, "boardName": "Master", "boardId": 65535, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 112, "isPrivate": true}, {"id": 62318, "name": "<PERSON>/<PERSON> Default Ingredient Speed- A1, B1, A2, B2", "macro": "EEP_OZMIN_INGR_SPEED_DEFAULT", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 416, "boardName": "Pump Board 7", "boardId": 7, "paramType": "SINGLE", "defaultParam": "34.5", "currentValue": "22.5", "newValue": null, "minParam": "7.5", "maxParam": "37.5", "machineId": 112, "isPrivate": true}, {"id": 62319, "name": "<PERSON>/<PERSON> De<PERSON>ult Ingredient Speed- A3, B3, A4, B4", "macro": "EEP_OZMIN_INGR_SPEED_DEFAULT", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 416, "boardName": "Pump Board 8", "boardId": 8, "paramType": "SINGLE", "defaultParam": "34.5", "currentValue": "22.5", "newValue": null, "minParam": "7.5", "maxParam": "37.5", "machineId": 112, "isPrivate": true}, {"id": 63647, "name": "Cleaning Concentrate Ingredient Number", "macro": "EEP_CLEANER_INGR_NUM", "description": "The ingredient number of the cleaner. Currently, this MUST be pump 3 on a pump board. (I.E A6 on pump board board 9, E2 on pump board 2)", "address": 392, "boardName": "Master", "boardId": 65535, "paramType": "BYTE", "defaultParam": "38", "currentValue": "46", "newValue": null, "minParam": "0", "maxParam": "47", "machineId": 112, "isPrivate": false}, {"id": 63648, "name": "Cleaning Concentrate Ratio", "macro": "EEP_CLEANER_RATIO", "description": "The ratio of water to cleaner for internal self cleaning. A value of “0” means that pre-diluted cleaner is being used in place of cleaner concentrate, and there will be no water used for dilution ", "address": 393, "boardName": "Master", "boardId": 65535, "paramType": "BYTE", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "30", "machineId": 112, "isPrivate": false}, {"id": 63649, "name": "Enable Cooling Board Current Measurements", "macro": "EEP_ENABLE_COOLING_CURRENT_CHECK", "description": "Enable Cooling Board Current Measurements for fans and compressor", "address": 272, "boardName": "Cooling", "boardId": 576, "paramType": "BOOLEAN", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 112, "isPrivate": false}, {"id": 63650, "name": "Global Cleaner Rinse Speed", "macro": "EEP_GLOBAL_CLEANER_RINSE_SPEED", "description": "The cleaning rate (in oz/min) that cleaner is rinsed during a self clean", "address": 388, "boardName": "Master", "boardId": 65535, "paramType": "SINGLE", "defaultParam": "15.0", "currentValue": "15.0", "newValue": null, "minParam": "5", "maxParam": "37.5", "machineId": 112, "isPrivate": false}, {"id": 63651, "name": "Global Ingredient Sensor Active Threshold", "macro": "EEP_LEVEL_THRESHOLD_GLOABL", "description": "Ingredient level (expressed in 1/20 fl. oz. increments) below which runout detection becomes active for ALL ingredients", "address": 338, "boardName": "Master", "boardId": 65535, "paramType": "WORD", "defaultParam": "100", "currentValue": "0", "newValue": null, "minParam": "10", "maxParam": "3500", "machineId": 112, "isPrivate": false}, {"id": 63652, "name": "Global Water Rinse Speed", "macro": "EEP_GLOBAL_WATER_RINSE_SPEED", "description": "The cleaning rate (in oz/min) that water is rinsed during a self clean", "address": 384, "boardName": "Master", "boardId": 65535, "paramType": "SINGLE", "defaultParam": "35.0", "currentValue": "35.0", "newValue": null, "minParam": "5", "maxParam": "37.5", "machineId": 112, "isPrivate": false}, {"id": 63653, "name": "<PERSON>/<PERSON> De<PERSON> Ingredient Speed- A5, B5, A6, B6", "macro": "EEP_OZMIN_INGR_SPEED_DEFAULT", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 416, "boardName": "Pump Board 9", "boardId": 9, "paramType": "SINGLE", "defaultParam": "34.5", "currentValue": "22.5", "newValue": null, "minParam": "7.5", "maxParam": "37.5", "machineId": 112, "isPrivate": true}, {"id": 63654, "name": "<PERSON>/<PERSON> De<PERSON>ult Ingredient Speed- C1, D1, E1, F1", "macro": "EEP_OZMIN_INGR_SPEED_DEFAULT", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 416, "boardName": "Pump Board 0", "boardId": 0, "paramType": "SINGLE", "defaultParam": "34.5", "currentValue": "22.5", "newValue": null, "minParam": "7.5", "maxParam": "37.5", "machineId": 112, "isPrivate": true}, {"id": 63655, "name": "<PERSON>/<PERSON> Default Ingredient Speed- C2, D2, E2, F2", "macro": "EEP_OZMIN_INGR_SPEED_DEFAULT", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 416, "boardName": "Pump Board 1", "boardId": 1, "paramType": "SINGLE", "defaultParam": "34.5", "currentValue": "22.5", "newValue": null, "minParam": "7.5", "maxParam": "37.5", "machineId": 112, "isPrivate": true}, {"id": 63656, "name": "<PERSON>/<PERSON> Default Ingredient Speed- C3, D3, E3, F3", "macro": "EEP_OZMIN_INGR_SPEED_DEFAULT", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 416, "boardName": "Pump Board 2", "boardId": 2, "paramType": "SINGLE", "defaultParam": "34.5", "currentValue": "22.5", "newValue": null, "minParam": "7.5", "maxParam": "37.5", "machineId": 112, "isPrivate": true}, {"id": 63657, "name": "<PERSON>/<PERSON> De<PERSON>ult Ingredient Speed- C4, D4, E4, F4", "macro": "EEP_OZMIN_INGR_SPEED_DEFAULT", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 416, "boardName": "Pump Board 3", "boardId": 3, "paramType": "SINGLE", "defaultParam": "34.5", "currentValue": "22.5", "newValue": null, "minParam": "7.5", "maxParam": "37.5", "machineId": 112, "isPrivate": true}, {"id": 63658, "name": "<PERSON>/<PERSON> De<PERSON>ult Ingredient Speed- C5, D5, E5, F5", "macro": "EEP_OZMIN_INGR_SPEED_DEFAULT", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 416, "boardName": "Pump Board 4", "boardId": 4, "paramType": "SINGLE", "defaultParam": "34.5", "currentValue": "22.5", "newValue": null, "minParam": "7.5", "maxParam": "37.5", "machineId": 112, "isPrivate": true}, {"id": 63659, "name": "<PERSON>/<PERSON> De<PERSON>ult Ingredient Speed- C6, D6, E6, F6", "macro": "EEP_OZMIN_INGR_SPEED_DEFAULT", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 416, "boardName": "Pump Board 5", "boardId": 5, "paramType": "SINGLE", "defaultParam": "34.5", "currentValue": "22.5", "newValue": null, "minParam": "7.5", "maxParam": "37.5", "machineId": 112, "isPrivate": true}, {"id": 63660, "name": "Priming Quantity - A1", "macro": "EEP_PRIMING_QTY_0", "description": "Priming quantity (expressed in 1/20 fl. oz. increments) for ingredient A1", "address": 84, "boardName": "Pump Board 7", "boardId": 7, "paramType": "BYTE", "defaultParam": "46", "currentValue": "46", "newValue": null, "minParam": "40", "maxParam": "110", "machineId": 112, "isPrivate": true}, {"id": 63661, "name": "Priming Quantity - A2", "macro": "EEP_PRIMING_QTY_2", "description": "Priming quantity (expressed in 1/20 fl. oz. increments) for ingredient A2", "address": 116, "boardName": "Pump Board 7", "boardId": 7, "paramType": "BYTE", "defaultParam": "56", "currentValue": "56", "newValue": null, "minParam": "40", "maxParam": "110", "machineId": 112, "isPrivate": true}, {"id": 63662, "name": "Priming Quantity - A3", "macro": "EEP_PRIMING_QTY_0", "description": "Priming quantity (expressed in 1/20 fl. oz. increments) for ingredient A3", "address": 84, "boardName": "Pump Board 8", "boardId": 8, "paramType": "BYTE", "defaultParam": "46", "currentValue": "46", "newValue": null, "minParam": "40", "maxParam": "110", "machineId": 112, "isPrivate": true}, {"id": 63663, "name": "Priming Quantity - A4", "macro": "EEP_PRIMING_QTY_2", "description": "Priming quantity (expressed in 1/20 fl. oz. increments) for ingredient A4", "address": 116, "boardName": "Pump Board 8", "boardId": 8, "paramType": "BYTE", "defaultParam": "57", "currentValue": "57", "newValue": null, "minParam": "40", "maxParam": "110", "machineId": 112, "isPrivate": true}, {"id": 63664, "name": "Priming Quantity - A5", "macro": "EEP_PRIMING_QTY_0", "description": "Priming quantity (expressed in 1/20 fl. oz. increments) for ingredient A5", "address": 84, "boardName": "Pump Board 9", "boardId": 9, "paramType": "BYTE", "defaultParam": "50", "currentValue": "50", "newValue": null, "minParam": "40", "maxParam": "110", "machineId": 112, "isPrivate": true}, {"id": 63665, "name": "Priming Quantity - A6", "macro": "EEP_PRIMING_QTY_2", "description": "Priming quantity (expressed in 1/20 fl. oz. increments) for ingredient A6", "address": 116, "boardName": "Pump Board 9", "boardId": 9, "paramType": "BYTE", "defaultParam": "65", "currentValue": "77", "newValue": null, "minParam": "40", "maxParam": "110", "machineId": 112, "isPrivate": true}, {"id": 63666, "name": "Priming Quantity - B1", "macro": "EEP_PRIMING_QTY_1", "description": "Priming quantity (expressed in 1/20 fl. oz. increments) for ingredient B1", "address": 100, "boardName": "Pump Board 7", "boardId": 7, "paramType": "BYTE", "defaultParam": "51", "currentValue": "51", "newValue": null, "minParam": "40", "maxParam": "110", "machineId": 112, "isPrivate": true}, {"id": 63667, "name": "Priming Quantity - B2", "macro": "EEP_PRIMING_QTY_3", "description": "Priming quantity (expressed in 1/20 fl. oz. increments) for ingredient B2", "address": 132, "boardName": "Pump Board 7", "boardId": 7, "paramType": "BYTE", "defaultParam": "55", "currentValue": "55", "newValue": null, "minParam": "40", "maxParam": "110", "machineId": 112, "isPrivate": true}, {"id": 63668, "name": "Priming Quantity - B3", "macro": "EEP_PRIMING_QTY_1", "description": "Priming quantity (expressed in 1/20 fl. oz. increments) for ingredient B3", "address": 100, "boardName": "Pump Board 8", "boardId": 8, "paramType": "BYTE", "defaultParam": "51", "currentValue": "51", "newValue": null, "minParam": "40", "maxParam": "110", "machineId": 112, "isPrivate": true}, {"id": 63669, "name": "Priming Quantity - B4", "macro": "EEP_PRIMING_QTY_3", "description": "Priming quantity (expressed in 1/20 fl. oz. increments) for ingredient B4", "address": 132, "boardName": "Pump Board 8", "boardId": 8, "paramType": "BYTE", "defaultParam": "55", "currentValue": "55", "newValue": null, "minParam": "40", "maxParam": "110", "machineId": 112, "isPrivate": true}, {"id": 63670, "name": "Priming Quantity - B5", "macro": "EEP_PRIMING_QTY_1", "description": "Priming quantity (expressed in 1/20 fl. oz. increments) for ingredient B5", "address": 100, "boardName": "Pump Board 9", "boardId": 9, "paramType": "BYTE", "defaultParam": "51", "currentValue": "51", "newValue": null, "minParam": "40", "maxParam": "110", "machineId": 112, "isPrivate": true}, {"id": 63671, "name": "Priming Quantity - B6", "macro": "EEP_PRIMING_QTY_3", "description": "Priming quantity (expressed in 1/20 fl. oz. increments) for ingredient B6", "address": 132, "boardName": "Pump Board 9", "boardId": 9, "paramType": "BYTE", "defaultParam": "58", "currentValue": "58", "newValue": null, "minParam": "40", "maxParam": "110", "machineId": 112, "isPrivate": true}, {"id": 63672, "name": "Priming Quantity - Bib 1", "macro": "EEP_BIB_PRIME_QTY_0", "description": "Priming quantity (expressed in 1/20th oz increments) for ingredient Bib 1", "address": 112, "boardName": "Nozzle", "boardId": 560, "paramType": "WORD", "defaultParam": "40", "currentValue": "40", "newValue": null, "minParam": "40", "maxParam": "400", "machineId": 112, "isPrivate": true}, {"id": 63673, "name": "Priming Quantity - Bib 2", "macro": "EEP_BIB_PRIME_QTY_1", "description": "Priming quantity (expressed in 1/20th oz increments) for ingredient Bib 2", "address": 114, "boardName": "Nozzle", "boardId": 560, "paramType": "WORD", "defaultParam": "40", "currentValue": "40", "newValue": null, "minParam": "40", "maxParam": "400", "machineId": 112, "isPrivate": true}, {"id": 63674, "name": "Priming Quantity - Bib 3", "macro": "EEP_BIB_PRIME_QTY_2", "description": "Priming quantity (expressed in 1/20th oz increments) for ingredient Bib 3", "address": 116, "boardName": "Nozzle", "boardId": 560, "paramType": "WORD", "defaultParam": "40", "currentValue": "40", "newValue": null, "minParam": "40", "maxParam": "400", "machineId": 112, "isPrivate": true}, {"id": 63675, "name": "Priming Quantity - Bib 4", "macro": "EEP_BIB_PRIME_QTY_3", "description": "Priming quantity (expressed in 1/20th oz increments) for ingredient Bib 4", "address": 118, "boardName": "Nozzle", "boardId": 560, "paramType": "WORD", "defaultParam": "40", "currentValue": "40", "newValue": null, "minParam": "40", "maxParam": "400", "machineId": 112, "isPrivate": true}, {"id": 63676, "name": "Priming Quantity - Bib 5", "macro": "EEP_BIB_PRIME_QTY_0", "description": "Priming quantity (expressed in 1/20th oz increments) for ingredient bib 5", "address": 112, "boardName": "Solenoid", "boardId": 12, "paramType": "WORD", "defaultParam": "40", "currentValue": "40", "newValue": null, "minParam": "40", "maxParam": "400", "machineId": 112, "isPrivate": true}, {"id": 63677, "name": "Priming Quantity - Bib 6", "macro": "EEP_BIB_PRIME_QTY_1", "description": "Priming quantity (expressed in 1/20th oz increments) for ingredient bib 6", "address": 114, "boardName": "Solenoid", "boardId": 12, "paramType": "WORD", "defaultParam": "40", "currentValue": "40", "newValue": null, "minParam": "40", "maxParam": "400", "machineId": 112, "isPrivate": true}, {"id": 63678, "name": "Priming Quantity - Bib 7", "macro": "EEP_BIB_PRIME_QTY_2", "description": "Priming quantity (expressed in 1/20th oz increments) for ingredient bib 7", "address": 116, "boardName": "Solenoid", "boardId": 12, "paramType": "WORD", "defaultParam": "40", "currentValue": "40", "newValue": null, "minParam": "40", "maxParam": "400", "machineId": 112, "isPrivate": true}, {"id": 63679, "name": "Priming Quantity - Bib 8", "macro": "EEP_BIB_PRIME_QTY_3", "description": "Priming quantity (expressed in 1/20th oz increments) for ingredient bib 8", "address": 118, "boardName": "Solenoid", "boardId": 12, "paramType": "WORD", "defaultParam": "40", "currentValue": "40", "newValue": null, "minParam": "40", "maxParam": "400", "machineId": 112, "isPrivate": true}, {"id": 63680, "name": "Priming Quantity - C1", "macro": "EEP_PRIMING_QTY_0", "description": "Priming quantity (expressed in 1/20 fl. oz. increments) for ingredient C1", "address": 84, "boardName": "Pump Board 0", "boardId": 0, "paramType": "BYTE", "defaultParam": "54", "currentValue": "54", "newValue": null, "minParam": "40", "maxParam": "110", "machineId": 112, "isPrivate": true}, {"id": 63681, "name": "Priming Quantity - C2", "macro": "EEP_PRIMING_QTY_0", "description": "Priming quantity (expressed in 1/20 fl. oz. increments) for ingredient C2", "address": 84, "boardName": "Pump Board 1", "boardId": 1, "paramType": "BYTE", "defaultParam": "50", "currentValue": "50", "newValue": null, "minParam": "40", "maxParam": "110", "machineId": 112, "isPrivate": true}, {"id": 63682, "name": "Priming Quantity - C3", "macro": "EEP_PRIMING_QTY_0", "description": "Priming quantity (expressed in 1/20 fl. oz. increments) for ingredient C3", "address": 84, "boardName": "Pump Board 2", "boardId": 2, "paramType": "BYTE", "defaultParam": "57", "currentValue": "57", "newValue": null, "minParam": "40", "maxParam": "110", "machineId": 112, "isPrivate": true}, {"id": 63683, "name": "Priming Quantity - C4", "macro": "EEP_PRIMING_QTY_0", "description": "Priming quantity (expressed in 1/20 fl. oz. increments) for ingredient C4", "address": 84, "boardName": "Pump Board 3", "boardId": 3, "paramType": "BYTE", "defaultParam": "50", "currentValue": "50", "newValue": null, "minParam": "40", "maxParam": "110", "machineId": 112, "isPrivate": true}, {"id": 63684, "name": "Priming Quantity - C5", "macro": "EEP_PRIMING_QTY_0", "description": "Priming quantity (expressed in 1/20 fl. oz. increments) for ingredient C5", "address": 84, "boardName": "Pump Board 4", "boardId": 4, "paramType": "BYTE", "defaultParam": "55", "currentValue": "55", "newValue": null, "minParam": "40", "maxParam": "110", "machineId": 112, "isPrivate": true}, {"id": 63685, "name": "Priming Quantity - C6", "macro": "EEP_PRIMING_QTY_0", "description": "Priming quantity (expressed in 1/20 fl. oz. increments) for ingredient C6", "address": 84, "boardName": "Pump Board 5", "boardId": 5, "paramType": "BYTE", "defaultParam": "55", "currentValue": "55", "newValue": null, "minParam": "40", "maxParam": "110", "machineId": 112, "isPrivate": true}, {"id": 63686, "name": "Priming Quantity - D1", "macro": "EEP_PRIMING_QTY_1", "description": "Priming quantity (expressed in 1/20 fl. oz. increments) for ingredient D1", "address": 100, "boardName": "Pump Board 0", "boardId": 0, "paramType": "BYTE", "defaultParam": "64", "currentValue": "64", "newValue": null, "minParam": "40", "maxParam": "110", "machineId": 112, "isPrivate": true}, {"id": 63687, "name": "Priming Quantity - D2", "macro": "EEP_PRIMING_QTY_1", "description": "Priming quantity (expressed in 1/20 fl. oz. increments) for ingredient D2", "address": 100, "boardName": "Pump Board 1", "boardId": 1, "paramType": "BYTE", "defaultParam": "48", "currentValue": "48", "newValue": null, "minParam": "40", "maxParam": "110", "machineId": 112, "isPrivate": true}, {"id": 63688, "name": "Priming Quantity - D3", "macro": "EEP_PRIMING_QTY_1", "description": "Priming quantity (expressed in 1/20 fl. oz. increments) for ingredient D3", "address": 100, "boardName": "Pump Board 2", "boardId": 2, "paramType": "BYTE", "defaultParam": "56", "currentValue": "56", "newValue": null, "minParam": "40", "maxParam": "110", "machineId": 112, "isPrivate": true}, {"id": 63689, "name": "Priming Quantity - D4", "macro": "EEP_PRIMING_QTY_1", "description": "Priming quantity (expressed in 1/20 fl. oz. increments) for ingredient D4", "address": 100, "boardName": "Pump Board 3", "boardId": 3, "paramType": "BYTE", "defaultParam": "51", "currentValue": "51", "newValue": null, "minParam": "40", "maxParam": "110", "machineId": 112, "isPrivate": true}, {"id": 63690, "name": "Priming Quantity - D5", "macro": "EEP_PRIMING_QTY_1", "description": "Priming quantity (expressed in 1/20 fl. oz. increments) for ingredient D5", "address": 100, "boardName": "Pump Board 4", "boardId": 4, "paramType": "BYTE", "defaultParam": "55", "currentValue": "55", "newValue": null, "minParam": "40", "maxParam": "110", "machineId": 112, "isPrivate": true}, {"id": 63691, "name": "Priming Quantity - D6", "macro": "EEP_PRIMING_QTY_1", "description": "Priming quantity (expressed in 1/20 fl. oz. increments) for ingredient D6", "address": 100, "boardName": "Pump Board 5", "boardId": 5, "paramType": "BYTE", "defaultParam": "52", "currentValue": "52", "newValue": null, "minParam": "40", "maxParam": "110", "machineId": 112, "isPrivate": true}, {"id": 63692, "name": "Priming Quantity - E1", "macro": "EEP_PRIMING_QTY_2", "description": "Priming quantity (expressed in 1/20 fl. oz. increments) for ingredient E1", "address": 116, "boardName": "Pump Board 0", "boardId": 0, "paramType": "BYTE", "defaultParam": "60", "currentValue": "60", "newValue": null, "minParam": "40", "maxParam": "110", "machineId": 112, "isPrivate": true}, {"id": 63693, "name": "Priming Quantity - E2", "macro": "EEP_PRIMING_QTY_2", "description": "Priming quantity (expressed in 1/20 fl. oz. increments) for ingredient E2", "address": 116, "boardName": "Pump Board 1", "boardId": 1, "paramType": "BYTE", "defaultParam": "46", "currentValue": "46", "newValue": null, "minParam": "40", "maxParam": "110", "machineId": 112, "isPrivate": true}, {"id": 63694, "name": "Priming Quantity - E3", "macro": "EEP_PRIMING_QTY_2", "description": "Priming quantity (expressed in 1/20 fl. oz. increments) for ingredient E3", "address": 116, "boardName": "Pump Board 2", "boardId": 2, "paramType": "BYTE", "defaultParam": "63", "currentValue": "63", "newValue": null, "minParam": "40", "maxParam": "110", "machineId": 112, "isPrivate": true}, {"id": 63695, "name": "Priming Quantity - E4", "macro": "EEP_PRIMING_QTY_2", "description": "Priming quantity (expressed in 1/20 fl. oz. increments) for ingredient E4", "address": 116, "boardName": "Pump Board 3", "boardId": 3, "paramType": "BYTE", "defaultParam": "48", "currentValue": "48", "newValue": null, "minParam": "40", "maxParam": "110", "machineId": 112, "isPrivate": true}, {"id": 63696, "name": "Priming Quantity - E5", "macro": "EEP_PRIMING_QTY_2", "description": "Priming quantity (expressed in 1/20 fl. oz. increments) for ingredient E5", "address": 116, "boardName": "Pump Board 4", "boardId": 4, "paramType": "BYTE", "defaultParam": "64", "currentValue": "64", "newValue": null, "minParam": "40", "maxParam": "110", "machineId": 112, "isPrivate": true}, {"id": 63697, "name": "Priming Quantity - E6", "macro": "EEP_PRIMING_QTY_2", "description": "Priming quantity (expressed in 1/20 fl. oz. increments) for ingredient E6", "address": 116, "boardName": "Pump Board 5", "boardId": 5, "paramType": "BYTE", "defaultParam": "51", "currentValue": "51", "newValue": null, "minParam": "40", "maxParam": "110", "machineId": 112, "isPrivate": true}, {"id": 63698, "name": "Priming Quantity - F1", "macro": "EEP_PRIMING_QTY_3", "description": "Priming quantity (expressed in 1/20 fl. oz. increments) for ingredient F1", "address": 132, "boardName": "Pump Board 0", "boardId": 0, "paramType": "BYTE", "defaultParam": "66", "currentValue": "66", "newValue": null, "minParam": "40", "maxParam": "110", "machineId": 112, "isPrivate": true}, {"id": 63699, "name": "Priming Quantity - F2", "macro": "EEP_PRIMING_QTY_3", "description": "Priming quantity (expressed in 1/20 fl. oz. increments) for ingredient F2", "address": 132, "boardName": "Pump Board 1", "boardId": 1, "paramType": "BYTE", "defaultParam": "46", "currentValue": "46", "newValue": null, "minParam": "40", "maxParam": "110", "machineId": 112, "isPrivate": true}, {"id": 63700, "name": "Priming Quantity - F3", "macro": "EEP_PRIMING_QTY_3", "description": "Priming quantity (expressed in 1/20 fl. oz. increments) for ingredient F3", "address": 132, "boardName": "Pump Board 2", "boardId": 2, "paramType": "BYTE", "defaultParam": "62", "currentValue": "62", "newValue": null, "minParam": "40", "maxParam": "110", "machineId": 112, "isPrivate": true}, {"id": 63701, "name": "Priming Quantity - F4", "macro": "EEP_PRIMING_QTY_3", "description": "Priming quantity (expressed in 1/20 fl. oz. increments) for ingredient F4", "address": 132, "boardName": "Pump Board 3", "boardId": 3, "paramType": "BYTE", "defaultParam": "47", "currentValue": "47", "newValue": null, "minParam": "40", "maxParam": "110", "machineId": 112, "isPrivate": true}, {"id": 63702, "name": "Priming Quantity - F5", "macro": "EEP_PRIMING_QTY_3", "description": "Priming quantity (expressed in 1/20 fl. oz. increments) for ingredient F5", "address": 132, "boardName": "Pump Board 4", "boardId": 4, "paramType": "BYTE", "defaultParam": "63", "currentValue": "63", "newValue": null, "minParam": "40", "maxParam": "110", "machineId": 112, "isPrivate": true}, {"id": 63703, "name": "Priming Quantity - F6", "macro": "EEP_PRIMING_QTY_3", "description": "Priming quantity (expressed in 1/20 fl. oz. increments) for ingredient F6", "address": 132, "boardName": "Pump Board 5", "boardId": 5, "paramType": "BYTE", "defaultParam": "48", "currentValue": "48", "newValue": null, "minParam": "40", "maxParam": "110", "machineId": 112, "isPrivate": true}, {"id": 63704, "name": "Priming Quantity - Soda Bib", "macro": "EEP_BIB_PRIME_QTY_SODA", "description": "Priming quantity (expressed in 1/20th oz increments) for the soda bib", "address": 120, "boardName": "Nozzle", "boardId": 560, "paramType": "WORD", "defaultParam": "40", "currentValue": "40", "newValue": null, "minParam": "40", "maxParam": "400", "machineId": 112, "isPrivate": true}, {"id": 63705, "name": "Priming Quantity - Water Bib", "macro": "EEP_BIB_PRIME_QTY_WATER", "description": "Priming quantity (expressed in 1/20th oz increments) for the water bib", "address": 122, "boardName": "Nozzle", "boardId": 560, "paramType": "WORD", "defaultParam": "40", "currentValue": "40", "newValue": null, "minParam": "40", "maxParam": "400", "machineId": 112, "isPrivate": true}, {"id": 63706, "name": "Refresh Line Timeout - A1", "macro": "EEP_REFRESH_TIMEOUT_0", "description": "Time (in seconds) this ingredients can remain in the lines without any dispensing operations. Set to 0 for no timeout.", "address": 336, "boardName": "Pump Board 7", "boardId": 7, "paramType": "WORD", "defaultParam": "0", "currentValue": "3600", "newValue": null, "minParam": "0", "maxParam": "10800", "machineId": 112, "isPrivate": true}, {"id": 63707, "name": "Refresh Line Timeout - A2", "macro": "EEP_REFRESH_TIMEOUT_2", "description": "Time (in seconds) this ingredients can remain in the lines without any dispensing operations. Set to 0 for no timeout.", "address": 340, "boardName": "Pump Board 7", "boardId": 7, "paramType": "WORD", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "10800", "machineId": 112, "isPrivate": true}, {"id": 63708, "name": "Refresh Line Timeout - A3", "macro": "EEP_REFRESH_TIMEOUT_0", "description": "Time (in seconds) this ingredients can remain in the lines without any dispensing operations. Set to 0 for no timeout.", "address": 336, "boardName": "Pump Board 8", "boardId": 8, "paramType": "WORD", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "10800", "machineId": 112, "isPrivate": true}, {"id": 63709, "name": "Refresh Line Timeout - A4", "macro": "EEP_REFRESH_TIMEOUT_2", "description": "Time (in seconds) this ingredients can remain in the lines without any dispensing operations. Set to 0 for no timeout.", "address": 340, "boardName": "Pump Board 8", "boardId": 8, "paramType": "WORD", "defaultParam": "0", "currentValue": "7200", "newValue": null, "minParam": "0", "maxParam": "10800", "machineId": 112, "isPrivate": true}, {"id": 63710, "name": "Refresh Line Timeout - A5", "macro": "EEP_REFRESH_TIMEOUT_0", "description": "Time (in seconds) this ingredients can remain in the lines without any dispensing operations. Set to 0 for no timeout.", "address": 336, "boardName": "Pump Board 9", "boardId": 9, "paramType": "WORD", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "10800", "machineId": 112, "isPrivate": true}, {"id": 63711, "name": "Refresh Line Timeout - A6", "macro": "EEP_REFRESH_TIMEOUT_2", "description": "Time (in seconds) this ingredients can remain in the lines without any dispensing operations. Set to 0 for no timeout.", "address": 340, "boardName": "Pump Board 9", "boardId": 9, "paramType": "WORD", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "10800", "machineId": 112, "isPrivate": true}, {"id": 63712, "name": "Refresh Line Timeout - B1", "macro": "EEP_REFRESH_TIMEOUT_1", "description": "Time (in seconds) this ingredients can remain in the lines without any dispensing operations. Set to 0 for no timeout.", "address": 338, "boardName": "Pump Board 7", "boardId": 7, "paramType": "WORD", "defaultParam": "0", "currentValue": "3600", "newValue": null, "minParam": "0", "maxParam": "10800", "machineId": 112, "isPrivate": true}, {"id": 63713, "name": "Refresh Line Timeout - B2", "macro": "EEP_REFRESH_TIMEOUT_3", "description": "Time (in seconds) this ingredients can remain in the lines without any dispensing operations. Set to 0 for no timeout.", "address": 342, "boardName": "Pump Board 7", "boardId": 7, "paramType": "WORD", "defaultParam": "0", "currentValue": "3600", "newValue": null, "minParam": "0", "maxParam": "10800", "machineId": 112, "isPrivate": true}, {"id": 63714, "name": "Refresh Line Timeout - B3", "macro": "EEP_REFRESH_TIMEOUT_1", "description": "Time (in seconds) this ingredients can remain in the lines without any dispensing operations. Set to 0 for no timeout.", "address": 338, "boardName": "Pump Board 8", "boardId": 8, "paramType": "WORD", "defaultParam": "0", "currentValue": "7200", "newValue": null, "minParam": "0", "maxParam": "10800", "machineId": 112, "isPrivate": true}, {"id": 63715, "name": "Refresh Line Timeout - B4", "macro": "EEP_REFRESH_TIMEOUT_3", "description": "Time (in seconds) this ingredients can remain in the lines without any dispensing operations. Set to 0 for no timeout.", "address": 342, "boardName": "Pump Board 8", "boardId": 8, "paramType": "WORD", "defaultParam": "0", "currentValue": "7200", "newValue": null, "minParam": "0", "maxParam": "10800", "machineId": 112, "isPrivate": true}, {"id": 63716, "name": "Refresh Line Timeout - B5", "macro": "EEP_REFRESH_TIMEOUT_1", "description": "Time (in seconds) this ingredients can remain in the lines without any dispensing operations. Set to 0 for no timeout.", "address": 338, "boardName": "Pump Board 9", "boardId": 9, "paramType": "WORD", "defaultParam": "0", "currentValue": "3600", "newValue": null, "minParam": "0", "maxParam": "10800", "machineId": 112, "isPrivate": true}, {"id": 63717, "name": "Refresh Line Timeout - B6", "macro": "EEP_REFRESH_TIMEOUT_3", "description": "Time (in seconds) this ingredients can remain in the lines without any dispensing operations. Set to 0 for no timeout.", "address": 342, "boardName": "Pump Board 9", "boardId": 9, "paramType": "WORD", "defaultParam": "0", "currentValue": "3600", "newValue": null, "minParam": "0", "maxParam": "10800", "machineId": 112, "isPrivate": true}, {"id": 63718, "name": "Refresh Line Timeout - C1", "macro": "EEP_REFRESH_TIMEOUT_0", "description": "Time (in seconds) this ingredients can remain in the lines without any dispensing operations. Set to 0 for no timeout.", "address": 336, "boardName": "Pump Board 0", "boardId": 0, "paramType": "WORD", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "10800", "machineId": 112, "isPrivate": true}, {"id": 63719, "name": "Refresh Line Timeout - C2", "macro": "EEP_REFRESH_TIMEOUT_0", "description": "Time (in seconds) this ingredients can remain in the lines without any dispensing operations. Set to 0 for no timeout.", "address": 336, "boardName": "Pump Board 1", "boardId": 1, "paramType": "WORD", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "10800", "machineId": 112, "isPrivate": true}, {"id": 63720, "name": "Refresh Line Timeout - C3", "macro": "EEP_REFRESH_TIMEOUT_0", "description": "Time (in seconds) this ingredients can remain in the lines without any dispensing operations. Set to 0 for no timeout.", "address": 336, "boardName": "Pump Board 2", "boardId": 2, "paramType": "WORD", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "10800", "machineId": 112, "isPrivate": true}, {"id": 63721, "name": "Refresh Line Timeout - C4", "macro": "EEP_REFRESH_TIMEOUT_0", "description": "Time (in seconds) this ingredients can remain in the lines without any dispensing operations. Set to 0 for no timeout.", "address": 336, "boardName": "Pump Board 3", "boardId": 3, "paramType": "WORD", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "10800", "machineId": 112, "isPrivate": true}, {"id": 63722, "name": "Refresh Line Timeout - C5", "macro": "EEP_REFRESH_TIMEOUT_0", "description": "Time (in seconds) this ingredients can remain in the lines without any dispensing operations. Set to 0 for no timeout.", "address": 336, "boardName": "Pump Board 4", "boardId": 4, "paramType": "WORD", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "10800", "machineId": 112, "isPrivate": true}, {"id": 63723, "name": "Refresh Line Timeout - C6", "macro": "EEP_REFRESH_TIMEOUT_0", "description": "Time (in seconds) this ingredients can remain in the lines without any dispensing operations. Set to 0 for no timeout.", "address": 336, "boardName": "Pump Board 5", "boardId": 5, "paramType": "WORD", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "10800", "machineId": 112, "isPrivate": true}, {"id": 63724, "name": "Refresh Line Timeout - D1", "macro": "EEP_REFRESH_TIMEOUT_1", "description": "Time (in seconds) this ingredients can remain in the lines without any dispensing operations. Set to 0 for no timeout.", "address": 338, "boardName": "Pump Board 0", "boardId": 0, "paramType": "WORD", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "10800", "machineId": 112, "isPrivate": true}, {"id": 63725, "name": "Refresh Line Timeout - D2", "macro": "EEP_REFRESH_TIMEOUT_1", "description": "Time (in seconds) this ingredients can remain in the lines without any dispensing operations. Set to 0 for no timeout.", "address": 338, "boardName": "Pump Board 1", "boardId": 1, "paramType": "WORD", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "10800", "machineId": 112, "isPrivate": true}, {"id": 63726, "name": "Refresh Line Timeout - D3", "macro": "EEP_REFRESH_TIMEOUT_1", "description": "Time (in seconds) this ingredients can remain in the lines without any dispensing operations. Set to 0 for no timeout.", "address": 338, "boardName": "Pump Board 2", "boardId": 2, "paramType": "WORD", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "10800", "machineId": 112, "isPrivate": true}, {"id": 63727, "name": "Refresh Line Timeout - D4", "macro": "EEP_REFRESH_TIMEOUT_1", "description": "Time (in seconds) this ingredients can remain in the lines without any dispensing operations. Set to 0 for no timeout.", "address": 338, "boardName": "Pump Board 3", "boardId": 3, "paramType": "WORD", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "10800", "machineId": 112, "isPrivate": true}, {"id": 63728, "name": "Refresh Line Timeout - D5", "macro": "EEP_REFRESH_TIMEOUT_1", "description": "Time (in seconds) this ingredients can remain in the lines without any dispensing operations. Set to 0 for no timeout.", "address": 338, "boardName": "Pump Board 4", "boardId": 4, "paramType": "WORD", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "10800", "machineId": 112, "isPrivate": true}, {"id": 63729, "name": "Refresh Line Timeout - D6", "macro": "EEP_REFRESH_TIMEOUT_1", "description": "Time (in seconds) this ingredients can remain in the lines without any dispensing operations. Set to 0 for no timeout.", "address": 338, "boardName": "Pump Board 5", "boardId": 5, "paramType": "WORD", "defaultParam": "0", "currentValue": "7200", "newValue": null, "minParam": "0", "maxParam": "10800", "machineId": 112, "isPrivate": true}, {"id": 63730, "name": "Refresh Line Timeout - E1", "macro": "EEP_REFRESH_TIMEOUT_2", "description": "Time (in seconds) this ingredients can remain in the lines without any dispensing operations. Set to 0 for no timeout.", "address": 340, "boardName": "Pump Board 0", "boardId": 0, "paramType": "WORD", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "10800", "machineId": 112, "isPrivate": true}, {"id": 63731, "name": "Refresh Line Timeout - E2", "macro": "EEP_REFRESH_TIMEOUT_2", "description": "Time (in seconds) this ingredients can remain in the lines without any dispensing operations. Set to 0 for no timeout.", "address": 340, "boardName": "Pump Board 1", "boardId": 1, "paramType": "WORD", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "10800", "machineId": 112, "isPrivate": true}, {"id": 63732, "name": "Refresh Line Timeout - E3", "macro": "EEP_REFRESH_TIMEOUT_2", "description": "Time (in seconds) this ingredients can remain in the lines without any dispensing operations. Set to 0 for no timeout.", "address": 340, "boardName": "Pump Board 2", "boardId": 2, "paramType": "WORD", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "10800", "machineId": 112, "isPrivate": true}, {"id": 63733, "name": "Refresh Line Timeout - E4", "macro": "EEP_REFRESH_TIMEOUT_2", "description": "Time (in seconds) this ingredients can remain in the lines without any dispensing operations. Set to 0 for no timeout.", "address": 340, "boardName": "Pump Board 3", "boardId": 3, "paramType": "WORD", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "10800", "machineId": 112, "isPrivate": true}, {"id": 63734, "name": "Refresh Line Timeout - E5", "macro": "EEP_REFRESH_TIMEOUT_2", "description": "Time (in seconds) this ingredients can remain in the lines without any dispensing operations. Set to 0 for no timeout.", "address": 340, "boardName": "Pump Board 4", "boardId": 4, "paramType": "WORD", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "10800", "machineId": 112, "isPrivate": true}, {"id": 63735, "name": "Refresh Line Timeout - E6", "macro": "EEP_REFRESH_TIMEOUT_2", "description": "Time (in seconds) this ingredients can remain in the lines without any dispensing operations. Set to 0 for no timeout.", "address": 340, "boardName": "Pump Board 5", "boardId": 5, "paramType": "WORD", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "10800", "machineId": 112, "isPrivate": true}, {"id": 63736, "name": "Refresh Line Timeout - F1", "macro": "EEP_REFRESH_TIMEOUT_3", "description": "Time (in seconds) this ingredients can remain in the lines without any dispensing operations. Set to 0 for no timeout.", "address": 342, "boardName": "Pump Board 0", "boardId": 0, "paramType": "WORD", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "10800", "machineId": 112, "isPrivate": true}, {"id": 63737, "name": "Refresh Line Timeout - F2", "macro": "EEP_REFRESH_TIMEOUT_3", "description": "Time (in seconds) this ingredients can remain in the lines without any dispensing operations. Set to 0 for no timeout.", "address": 342, "boardName": "Pump Board 1", "boardId": 1, "paramType": "WORD", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "10800", "machineId": 112, "isPrivate": true}, {"id": 63738, "name": "Refresh Line Timeout - F3", "macro": "EEP_REFRESH_TIMEOUT_3", "description": "Time (in seconds) this ingredients can remain in the lines without any dispensing operations. Set to 0 for no timeout.", "address": 342, "boardName": "Pump Board 2", "boardId": 2, "paramType": "WORD", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "10800", "machineId": 112, "isPrivate": true}, {"id": 63739, "name": "Refresh Line Timeout - F4", "macro": "EEP_REFRESH_TIMEOUT_3", "description": "Time (in seconds) this ingredients can remain in the lines without any dispensing operations. Set to 0 for no timeout.", "address": 342, "boardName": "Pump Board 3", "boardId": 3, "paramType": "WORD", "defaultParam": "0", "currentValue": "7200", "newValue": null, "minParam": "0", "maxParam": "10800", "machineId": 112, "isPrivate": true}, {"id": 63740, "name": "Refresh Line Timeout - F5", "macro": "EEP_REFRESH_TIMEOUT_3", "description": "Time (in seconds) this ingredients can remain in the lines without any dispensing operations. Set to 0 for no timeout.", "address": 342, "boardName": "Pump Board 4", "boardId": 4, "paramType": "WORD", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "10800", "machineId": 112, "isPrivate": true}, {"id": 63741, "name": "Refresh Line Timeout - F6", "macro": "EEP_REFRESH_TIMEOUT_3", "description": "Time (in seconds) this ingredients can remain in the lines without any dispensing operations. Set to 0 for no timeout.", "address": 342, "boardName": "Pump Board 5", "boardId": 5, "paramType": "WORD", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "10800", "machineId": 112, "isPrivate": true}, {"id": 63742, "name": "Static Cycle Enable", "macro": "EEP_ENABLE_STATIC_COMPRESSOR", "description": "Enable static cycling of the compressor regardless of the state of temperature sensors. (Note that if ", "address": 49, "boardName": "Cooling", "boardId": 576, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 112, "isPrivate": false}, {"id": 82576, "name": "Allow Runout A1, A2, B1, B2", "macro": "EEP_ALLOW_RUNOUT", "description": "Allow runout errors for this board's ingredients during drink requests", "address": 277, "boardName": "Pump Board 7", "boardId": 7, "paramType": "BOOLEAN", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 112, "isPrivate": false}, {"id": 82577, "name": "Allow Runout A3, A4, B3, B4", "macro": "EEP_ALLOW_RUNOUT", "description": "Allow runout errors for this board's ingredients during drink requests", "address": 277, "boardName": "Pump Board 8", "boardId": 8, "paramType": "BOOLEAN", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 112, "isPrivate": false}, {"id": 82578, "name": "Allow Runout A5, A6, B5, B6", "macro": "EEP_ALLOW_RUNOUT", "description": "Allow runout errors for this board's ingredients during drink requests", "address": 277, "boardName": "Pump Board 9", "boardId": 9, "paramType": "BOOLEAN", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 112, "isPrivate": false}, {"id": 82579, "name": "Allow Runout C1, D1, E1, F1", "macro": "EEP_ALLOW_RUNOUT", "description": "Allow runout errors for this board's ingredients during drink requests", "address": 277, "boardName": "Pump Board 0", "boardId": 0, "paramType": "BOOLEAN", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 112, "isPrivate": false}, {"id": 82580, "name": "Allow Runout C2, E2, D2, F2", "macro": "EEP_ALLOW_RUNOUT", "description": "Allow runout errors for this board's ingredients during drink requests", "address": 277, "boardName": "Pump Board 1", "boardId": 1, "paramType": "BOOLEAN", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 112, "isPrivate": false}, {"id": 82581, "name": "Allow Runout C3, D3, E3, F3", "macro": "EEP_ALLOW_RUNOUT", "description": "Allow runout errors for this board's ingredients during drink requests", "address": 277, "boardName": "Pump Board 2", "boardId": 2, "paramType": "BOOLEAN", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 112, "isPrivate": false}, {"id": 82582, "name": "Allow Runout C4, D4, E4, F4", "macro": "EEP_ALLOW_RUNOUT", "description": "Allow runout errors for this board's ingredients during drink requests", "address": 277, "boardName": "Pump Board 3", "boardId": 3, "paramType": "BOOLEAN", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 112, "isPrivate": false}, {"id": 82583, "name": "Allow Runout C5, D5, E5, F5", "macro": "EEP_ALLOW_RUNOUT", "description": "Allow runout errors for this board's ingredients during drink requests", "address": 277, "boardName": "Pump Board 4", "boardId": 4, "paramType": "BOOLEAN", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 112, "isPrivate": false}, {"id": 82584, "name": "Allow Runout C6, D6, E6, F6", "macro": "EEP_ALLOW_RUNOUT", "description": "Allow runout errors for this board's ingredients during drink requests", "address": 277, "boardName": "Pump Board 5", "boardId": 5, "paramType": "BOOLEAN", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 112, "isPrivate": false}, {"id": 82585, "name": "Compressor Normal Runtime Min", "macro": "EEP_COMPRESSOR_NORMAL_RT_MIN", "description": "Continuous time (in seconds) that the compressor can be disabled before cycling ", "address": 210, "boardName": "Cooling", "boardId": 576, "paramType": "WORD", "defaultParam": "420", "currentValue": "420", "newValue": null, "minParam": "60", "maxParam": "900", "machineId": 112, "isPrivate": false}, {"id": 82586, "name": "Enable Bottom Temperature Sensor", "macro": "EEP_ENABLE_SENSOR_BOT", "description": "Enable the bottom temperature sensor reading to toggle the compressor ON and OFF. Set to false if the bottom temperature sensor is not working properly.", "address": 232, "boardName": "Cooling", "boardId": 576, "paramType": "BOOLEAN", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 112, "isPrivate": false}, {"id": 82587, "name": "Enable Top Temperature Sensor", "macro": "EEP_ENABLE_SENSOR_TOP", "description": "Enable the top temperature sensor reading to toggle the compressor ON and OFF. Set to false if the top temperature sensor is not working properly.", "address": 233, "boardName": "Cooling", "boardId": 576, "paramType": "BOOLEAN", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 112, "isPrivate": false}, {"id": 82588, "name": "First Dispense Overpour Quantity - A1, A2, B1, B2", "macro": "EEP_STARTUP_QTY", "description": "Quantity (expressed in 1/20 fl. oz. increments) to overprime this board's ingredients beyond their priming lengths for the first pour of the day", "address": 305, "boardName": "Pump Board 7", "boardId": 7, "paramType": "BYTE", "defaultParam": "10", "currentValue": "10", "newValue": null, "minParam": "0", "maxParam": "100", "machineId": 112, "isPrivate": false}, {"id": 82589, "name": "First Dispense Overpour Quantity - A3, A4, B3, B4", "macro": "EEP_STARTUP_QTY", "description": "Quantity (expressed in 1/20 fl. oz. increments) to overprime this board's ingredients beyond their priming lengths for the first pour of the day", "address": 305, "boardName": "Pump Board 8", "boardId": 8, "paramType": "BYTE", "defaultParam": "10", "currentValue": "10", "newValue": null, "minParam": "0", "maxParam": "100", "machineId": 112, "isPrivate": false}, {"id": 82590, "name": "First Dispense Overpour Quantity - A5, A6, B5, B6", "macro": "EEP_STARTUP_QTY", "description": "Quantity (expressed in 1/20 fl. oz. increments) to overprime this board's ingredients beyond their priming lengths for the first pour of the day", "address": 305, "boardName": "Pump Board 9", "boardId": 9, "paramType": "BYTE", "defaultParam": "10", "currentValue": "10", "newValue": null, "minParam": "0", "maxParam": "100", "machineId": 112, "isPrivate": false}, {"id": 82591, "name": "First Dispense Overpour Quantity - C1, D1, E1, F1", "macro": "EEP_STARTUP_QTY", "description": "Quantity (expressed in 1/20 fl. oz. increments) to overprime this board's ingredients beyond their priming lengths for the first pour of the day", "address": 305, "boardName": "Pump Board 0", "boardId": 0, "paramType": "BYTE", "defaultParam": "10", "currentValue": "10", "newValue": null, "minParam": "0", "maxParam": "100", "machineId": 112, "isPrivate": false}, {"id": 82592, "name": "First Dispense Overpour Quantity - C2, E2, D2, F2", "macro": "EEP_STARTUP_QTY", "description": "Quantity (expressed in 1/20 fl. oz. increments) to overprime this board's ingredients beyond their priming lengths for the first pour of the day", "address": 305, "boardName": "Pump Board 1", "boardId": 1, "paramType": "BYTE", "defaultParam": "10", "currentValue": "10", "newValue": null, "minParam": "0", "maxParam": "100", "machineId": 112, "isPrivate": false}, {"id": 82593, "name": "First Dispense Overpour Quantity - C3, D3, E3, F3", "macro": "EEP_STARTUP_QTY", "description": "Quantity (expressed in 1/20 fl. oz. increments) to overprime this board's ingredients beyond their priming lengths for the first pour of the day", "address": 305, "boardName": "Pump Board 2", "boardId": 2, "paramType": "BYTE", "defaultParam": "10", "currentValue": "10", "newValue": null, "minParam": "0", "maxParam": "100", "machineId": 112, "isPrivate": false}, {"id": 82594, "name": "First Dispense Overpour Quantity - C4, D4, E4, F4", "macro": "EEP_STARTUP_QTY", "description": "Quantity (expressed in 1/20 fl. oz. increments) to overprime this board's ingredients beyond their priming lengths for the first pour of the day", "address": 305, "boardName": "Pump Board 3", "boardId": 3, "paramType": "BYTE", "defaultParam": "10", "currentValue": "10", "newValue": null, "minParam": "0", "maxParam": "100", "machineId": 112, "isPrivate": false}, {"id": 82595, "name": "First Dispense Overpour Quantity - C5, D5, E5, F5", "macro": "EEP_STARTUP_QTY", "description": "Quantity (expressed in 1/20 fl. oz. increments) to overprime this board's ingredients beyond their priming lengths for the first pour of the day", "address": 305, "boardName": "Pump Board 4", "boardId": 4, "paramType": "BYTE", "defaultParam": "10", "currentValue": "10", "newValue": null, "minParam": "0", "maxParam": "100", "machineId": 112, "isPrivate": false}, {"id": 82596, "name": "First Dispense Overpour Quantity - C6, D6, E6, F6", "macro": "EEP_STARTUP_QTY", "description": "Quantity (expressed in 1/20 fl. oz. increments) to overprime this board's ingredients beyond their priming lengths for the first pour of the day", "address": 305, "boardName": "Pump Board 5", "boardId": 5, "paramType": "BYTE", "defaultParam": "10", "currentValue": "10", "newValue": null, "minParam": "0", "maxParam": "100", "machineId": 112, "isPrivate": false}, {"id": 82597, "name": "Leave ALL Ingredients Unprimed at Startup", "macro": "EEP_ENABLE_UNPRIMED_STARTUP", "description": "At startup, leave ALL lines that were cleaned unprimed in order to save as much ingredient as possible throughout the day. This causes first dispenses of the day to be SLOW! Use with Caution.", "address": 370, "boardName": "Master", "boardId": 65535, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 112, "isPrivate": false}, {"id": 87016, "name": "Calibration Factor - Cleaner", "macro": "EEP_CLEANER_CAL_VAL", "description": "Cleaner calibration factor (Number of pulses per fluid oz)", "address": 128, "boardName": "Nozzle", "boardId": 560, "paramType": "DWORD", "defaultParam": "52720", "currentValue": "52720", "newValue": null, "minParam": "9720", "maxParam": "194400", "machineId": 112, "isPrivate": false}, {"id": 87017, "name": "Current Cleaner Level", "macro": "EEP_CLEANER_LEVEL", "description": "Current volume of cleaner concentrate (expressed in 1/20th oz increments)", "address": 134, "boardName": "Nozzle", "boardId": 560, "paramType": "WORD", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "65535", "machineId": 112, "isPrivate": true}, {"id": 87018, "name": "Enable Cleaner Tracking", "macro": "EEP_ENABLE_CLEANER_TRACKING", "description": "Enable cleaner tracking using inline paddle sensors", "address": 137, "boardName": "Nozzle", "boardId": 560, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 112, "isPrivate": false}, {"id": 87019, "name": "Ingredient number for Water", "macro": "EEP_WATER_INGR_NUM", "description": "Ingredient number for water. Used for self cleaning procedure", "address": 136, "boardName": "Nozzle", "boardId": 560, "paramType": "BYTE", "defaultParam": "45", "currentValue": "44", "newValue": null, "minParam": "40", "maxParam": "47", "machineId": 112, "isPrivate": false}, {"id": 87020, "name": "Priming Quantity - Cleaner", "macro": "EEP_CLEANER_PRIME_QTY", "description": "Priming quantity (expressed in 1/20th oz increments) for ingredient Cleaner", "address": 132, "boardName": "Nozzle", "boardId": 560, "paramType": "WORD", "defaultParam": "40", "currentValue": "40", "newValue": null, "minParam": "20", "maxParam": "400", "machineId": 112, "isPrivate": false}, {"id": 95636, "name": "Allow Retraction T1 - T4", "macro": "EEP_ALLOW_RETRACTION", "description": "Allow user-configurable retraction for this board's ingredients", "address": 276, "boardName": "Pump Board 13", "boardId": 13, "paramType": "BOOLEAN", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 112, "isPrivate": false}, {"id": 95637, "name": "Allow Retraction T5 - T8", "macro": "EEP_ALLOW_RETRACTION", "description": "Allow user-configurable retraction for this board's ingredients", "address": 276, "boardName": "Pump Board 14", "boardId": 14, "paramType": "BOOLEAN", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 112, "isPrivate": false}, {"id": 95638, "name": "Allow Runout T1 - T4", "macro": "EEP_ALLOW_RUNOUT", "description": "Allow runout errors for this board's ingredients during drink requests", "address": 277, "boardName": "Pump Board 13", "boardId": 13, "paramType": "BOOLEAN", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 112, "isPrivate": false}, {"id": 95639, "name": "Allow Runout T5 - T8", "macro": "EEP_ALLOW_RUNOUT", "description": "Allow runout errors for this board's ingredients during drink requests", "address": 277, "boardName": "Pump Board 14", "boardId": 14, "paramType": "BOOLEAN", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 112, "isPrivate": false}, {"id": 95640, "name": "Bubble Detection - T1", "macro": "EEP_BUBBLE_DETECTION_0", "description": "Flag indicating whether a bubble/air is detected in T1's dispensing line", "address": 16, "boardName": "Pump Board 13", "boardId": 13, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 112, "isPrivate": true}, {"id": 95641, "name": "Bubble Detection - T2", "macro": "EEP_BUBBLE_DETECTION_1", "description": "Flag indicating whether a bubble/air is detected in T2's dispensing line", "address": 32, "boardName": "Pump Board 13", "boardId": 13, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 112, "isPrivate": true}, {"id": 95642, "name": "Bubble Detection - T3", "macro": "EEP_BUBBLE_DETECTION_2", "description": "Flag indicating whether a bubble/air is detected in T3's dispensing line", "address": 48, "boardName": "Pump Board 13", "boardId": 13, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 112, "isPrivate": true}, {"id": 95643, "name": "Bubble Detection - T4", "macro": "EEP_BUBBLE_DETECTION_3", "description": "Flag indicating whether a bubble/air is detected in T4's dispensing line", "address": 64, "boardName": "Pump Board 13", "boardId": 13, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 112, "isPrivate": true}, {"id": 95644, "name": "Bubble Detection - T5", "macro": "EEP_BUBBLE_DETECTION_0", "description": "Flag indicating whether a bubble/air is detected in A5's dispensing line", "address": 16, "boardName": "Pump Board 14", "boardId": 14, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 112, "isPrivate": true}, {"id": 95645, "name": "Bubble Detection - T6", "macro": "EEP_BUBBLE_DETECTION_1", "description": "Flag indicating whether a bubble/air is detected in T6's dispensing line", "address": 32, "boardName": "Pump Board 14", "boardId": 14, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 112, "isPrivate": true}, {"id": 95646, "name": "Bubble Detection - T7", "macro": "EEP_BUBBLE_DETECTION_2", "description": "Flag indicating whether a bubble/air is detected in T7's dispensing line", "address": 48, "boardName": "Pump Board 14", "boardId": 14, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 112, "isPrivate": true}, {"id": 95647, "name": "Bubble Detection - T8", "macro": "EEP_BUBBLE_DETECTION_3", "description": "Flag indicating whether a bubble/air is detected in T8's dispensing line", "address": 64, "boardName": "Pump Board 14", "boardId": 14, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 112, "isPrivate": true}, {"id": 95648, "name": "Bubble Detection Threshold - T1", "macro": "EEP_BUBBLE_ROC_THRESHOLD_0", "description": "Rate of change value required to signify a runout. (Default Oat milk = 30, almond/dairy = 50, all other 75+)", "address": 368, "boardName": "Pump Board 13", "boardId": 13, "paramType": "BYTE", "defaultParam": "50", "currentValue": "50", "newValue": null, "minParam": "15", "maxParam": "150", "machineId": 112, "isPrivate": false}, {"id": 95649, "name": "Bubble Detection Threshold - T2", "macro": "EEP_BUBBLE_ROC_THRESHOLD_1", "description": "Rate of change value required to signify a runout. (Default Oat milk = 30, almond/dairy = 50, all other 75+)", "address": 369, "boardName": "Pump Board 13", "boardId": 13, "paramType": "BYTE", "defaultParam": "50", "currentValue": "50", "newValue": null, "minParam": "15", "maxParam": "150", "machineId": 112, "isPrivate": false}, {"id": 95650, "name": "Bubble Detection Threshold - T3", "macro": "EEP_BUBBLE_ROC_THRESHOLD_2", "description": "Rate of change value required to signify a runout. (Default Oat milk = 30, almond/dairy = 50, all other 75+)", "address": 370, "boardName": "Pump Board 13", "boardId": 13, "paramType": "BYTE", "defaultParam": "50", "currentValue": "50", "newValue": null, "minParam": "15", "maxParam": "150", "machineId": 112, "isPrivate": false}, {"id": 95651, "name": "Bubble Detection Threshold - T4", "macro": "EEP_BUBBLE_ROC_THRESHOLD_3", "description": "Rate of change value required to signify a runout. (Default Oat milk = 30, almond/dairy = 50, all other 75+)", "address": 371, "boardName": "Pump Board 13", "boardId": 13, "paramType": "BYTE", "defaultParam": "50", "currentValue": "50", "newValue": null, "minParam": "15", "maxParam": "150", "machineId": 112, "isPrivate": false}, {"id": 95652, "name": "Bubble Detection Threshold - T5", "macro": "EEP_BUBBLE_ROC_THRESHOLD_0", "description": "Rate of change value required to signify a runout. (Default Oat milk = 30, almond/dairy = 50, all other 75+)", "address": 368, "boardName": "Pump Board 14", "boardId": 14, "paramType": "BYTE", "defaultParam": "50", "currentValue": "50", "newValue": null, "minParam": "15", "maxParam": "150", "machineId": 112, "isPrivate": false}, {"id": 95653, "name": "Bubble Detection Threshold - T6", "macro": "EEP_BUBBLE_ROC_THRESHOLD_1", "description": "Rate of change value required to signify a runout. (Default Oat milk = 30, almond/dairy = 50, all other 75+)", "address": 369, "boardName": "Pump Board 14", "boardId": 14, "paramType": "BYTE", "defaultParam": "50", "currentValue": "50", "newValue": null, "minParam": "15", "maxParam": "150", "machineId": 112, "isPrivate": false}, {"id": 95654, "name": "Bubble Detection Threshold - T7", "macro": "EEP_BUBBLE_ROC_THRESHOLD_2", "description": "Rate of change value required to signify a runout. (Default Oat milk = 30, almond/dairy = 50, all other 75+)", "address": 370, "boardName": "Pump Board 14", "boardId": 14, "paramType": "BYTE", "defaultParam": "50", "currentValue": "50", "newValue": null, "minParam": "15", "maxParam": "150", "machineId": 112, "isPrivate": false}, {"id": 95655, "name": "Bubble Detection Threshold - T8", "macro": "EEP_BUBBLE_ROC_THRESHOLD_3", "description": "Rate of change value required to signify a runout. (Default Oat milk = 30, almond/dairy = 50, all other 75+)", "address": 371, "boardName": "Pump Board 14", "boardId": 14, "paramType": "BYTE", "defaultParam": "50", "currentValue": "50", "newValue": null, "minParam": "15", "maxParam": "150", "machineId": 112, "isPrivate": false}, {"id": 95656, "name": "Dispensing Calibration Factor - T1", "macro": "EEP_INGR_CALIBRATION_FACTOR_0", "description": "Ingredient calibration factor (Number of pulses per fluid oz)", "address": 384, "boardName": "Pump Board 13", "boardId": 13, "paramType": "DWORD", "defaultParam": "48600", "currentValue": "48600", "newValue": null, "minParam": "9720", "maxParam": "194400", "machineId": 112, "isPrivate": true}, {"id": 95657, "name": "Dispensing Calibration Factor - T2", "macro": "EEP_INGR_CALIBRATION_FACTOR_1", "description": "Ingredient calibration factor (Number of pulses per fluid oz)", "address": 388, "boardName": "Pump Board 13", "boardId": 13, "paramType": "DWORD", "defaultParam": "48600", "currentValue": "48600", "newValue": null, "minParam": "9720", "maxParam": "194400", "machineId": 112, "isPrivate": true}, {"id": 95658, "name": "Dispensing Calibration Factor - T3", "macro": "EEP_INGR_CALIBRATION_FACTOR_2", "description": "Ingredient calibration factor (Number of pulses per fluid oz)", "address": 392, "boardName": "Pump Board 13", "boardId": 13, "paramType": "DWORD", "defaultParam": "48600", "currentValue": "48600", "newValue": null, "minParam": "9720", "maxParam": "194400", "machineId": 112, "isPrivate": true}, {"id": 95659, "name": "Dispensing Calibration Factor - T4", "macro": "EEP_INGR_CALIBRATION_FACTOR_3", "description": "Ingredient calibration factor (Number of pulses per fluid oz)", "address": 396, "boardName": "Pump Board 13", "boardId": 13, "paramType": "DWORD", "defaultParam": "48600", "currentValue": "48600", "newValue": null, "minParam": "9720", "maxParam": "194400", "machineId": 112, "isPrivate": true}, {"id": 95660, "name": "Dispensing Calibration Factor - T5", "macro": "EEP_INGR_CALIBRATION_FACTOR_0", "description": "Ingredient calibration factor (Number of pulses per fluid oz)", "address": 384, "boardName": "Pump Board 14", "boardId": 14, "paramType": "DWORD", "defaultParam": "48600", "currentValue": "48600", "newValue": null, "minParam": "9720", "maxParam": "194400", "machineId": 112, "isPrivate": true}, {"id": 95661, "name": "Dispensing Calibration Factor - T6", "macro": "EEP_INGR_CALIBRATION_FACTOR_1", "description": "Ingredient calibration factor (Number of pulses per fluid oz)", "address": 388, "boardName": "Pump Board 14", "boardId": 14, "paramType": "DWORD", "defaultParam": "48600", "currentValue": "48600", "newValue": null, "minParam": "9720", "maxParam": "194400", "machineId": 112, "isPrivate": true}, {"id": 95662, "name": "Dispensing Calibration Factor - T7", "macro": "EEP_INGR_CALIBRATION_FACTOR_2", "description": "Ingredient calibration factor (Number of pulses per fluid oz)", "address": 392, "boardName": "Pump Board 14", "boardId": 14, "paramType": "DWORD", "defaultParam": "48600", "currentValue": "48600", "newValue": null, "minParam": "9720", "maxParam": "194400", "machineId": 112, "isPrivate": true}, {"id": 95663, "name": "Dispensing Calibration Factor - T8", "macro": "EEP_INGR_CALIBRATION_FACTOR_3", "description": "Ingredient calibration factor (Number of pulses per fluid oz)", "address": 396, "boardName": "Pump Board 14", "boardId": 14, "paramType": "DWORD", "defaultParam": "48600", "currentValue": "48600", "newValue": null, "minParam": "9720", "maxParam": "194400", "machineId": 112, "isPrivate": true}, {"id": 95664, "name": "Enable Evaporator Plate Temperature Check", "macro": "EEP_ENABLE_EVAP_PLATE_TEMP_CEHCK", "description": "Enable Evaporator Plate temperature measurements to be reported back to the master PCB", "address": 273, "boardName": "Cooling", "boardId": 576, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 112, "isPrivate": false}, {"id": 95665, "name": "First Dispense Overpour Quantity - T1 - T4", "macro": "EEP_STARTUP_QTY", "description": "Quantity (expressed in 1/20 fl. oz. increments) to overprime this board's ingredients beyond their priming lengths for the first pour of the day", "address": 305, "boardName": "Pump Board 13", "boardId": 13, "paramType": "BYTE", "defaultParam": "10", "currentValue": "10", "newValue": null, "minParam": "0", "maxParam": "100", "machineId": 112, "isPrivate": false}, {"id": 95666, "name": "First Dispense Overpour Quantity - T5 - T8", "macro": "EEP_STARTUP_QTY", "description": "Quantity (expressed in 1/20 fl. oz. increments) to overprime this board's ingredients beyond their priming lengths for the first pour of the day", "address": 305, "boardName": "Pump Board 14", "boardId": 14, "paramType": "BYTE", "defaultParam": "10", "currentValue": "10", "newValue": null, "minParam": "0", "maxParam": "100", "machineId": 112, "isPrivate": false}, {"id": 95667, "name": "Ingredient Line Status - Bib 1", "macro": "EEP_BIB_LINE_STATUS_0", "description": "Bitfield for current ingredient prime status of ingredient", "address": 144, "boardName": "Nozzle", "boardId": 560, "paramType": "BYTE", "defaultParam": "0", "currentValue": "255", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 112, "isPrivate": true}, {"id": 95668, "name": "Ingredient Line Status - Bib 2", "macro": "EEP_BIB_LINE_STATUS_1", "description": "Bitfield for current ingredient prime status of ingredient", "address": 145, "boardName": "Nozzle", "boardId": 560, "paramType": "BYTE", "defaultParam": "0", "currentValue": "255", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 112, "isPrivate": true}, {"id": 95669, "name": "Ingredient Line Status - Bib 3", "macro": "EEP_BIB_LINE_STATUS_2", "description": "Bitfield for current ingredient prime status of ingredient", "address": 146, "boardName": "Nozzle", "boardId": 560, "paramType": "BYTE", "defaultParam": "0", "currentValue": "255", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 112, "isPrivate": true}, {"id": 95670, "name": "Ingredient Line Status - Bib 4", "macro": "EEP_BIB_LINE_STATUS_3", "description": "Bitfield for current ingredient prime status of ingredient", "address": 147, "boardName": "Nozzle", "boardId": 560, "paramType": "BYTE", "defaultParam": "0", "currentValue": "255", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 112, "isPrivate": true}, {"id": 95671, "name": "Ingredient Line Status - Bib 5", "macro": "EEP_BIB_LINE_STATUS_0", "description": "Bitfield for current ingredient prime status of ingredient", "address": 144, "boardName": "Solenoid", "boardId": 12, "paramType": "BYTE", "defaultParam": "0", "currentValue": "255", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 112, "isPrivate": true}, {"id": 95672, "name": "Ingredient Line Status - Bib 6", "macro": "EEP_BIB_LINE_STATUS_1", "description": "Bitfield for current ingredient prime status of ingredient", "address": 145, "boardName": "Solenoid", "boardId": 12, "paramType": "BYTE", "defaultParam": "0", "currentValue": "255", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 112, "isPrivate": true}, {"id": 95673, "name": "Ingredient Line Status - Bib 7", "macro": "EEP_BIB_LINE_STATUS_2", "description": "Bitfield for current ingredient prime status of ingredient", "address": 146, "boardName": "Solenoid", "boardId": 12, "paramType": "BYTE", "defaultParam": "0", "currentValue": "255", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 112, "isPrivate": true}, {"id": 95674, "name": "Ingredient Line Status - Bib 8", "macro": "EEP_BIB_LINE_STATUS_3", "description": "Bitfield for current ingredient prime status of ingredient", "address": 147, "boardName": "Solenoid", "boardId": 12, "paramType": "BYTE", "defaultParam": "0", "currentValue": "255", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 112, "isPrivate": true}, {"id": 95675, "name": "Ingredient Line Status - Cleaner", "macro": "EEP_BIB_LINE_STATUS_CLEANER", "description": "Bitfield for current ingredient prime status of ingredient", "address": 150, "boardName": "Nozzle", "boardId": 560, "paramType": "BYTE", "defaultParam": "0", "currentValue": "255", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 112, "isPrivate": true}, {"id": 95676, "name": "Ingredient Line Status - Soda Bib", "macro": "EEP_BIB_LINE_STATUS_SODA", "description": "Bitfield for current ingredient prime status of ingredient", "address": 148, "boardName": "Nozzle", "boardId": 560, "paramType": "BYTE", "defaultParam": "0", "currentValue": "255", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 112, "isPrivate": true}, {"id": 95677, "name": "Ingredient Line Status - T1", "macro": "EEP_INGR_LINE_STATUS_0", "description": "Bitfield for current ingredient prime status of pumped ingredients", "address": 432, "boardName": "Pump Board 13", "boardId": 13, "paramType": "BYTE", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 112, "isPrivate": true}, {"id": 95678, "name": "Ingredient Line Status - T2", "macro": "EEP_INGR_LINE_STATUS_1", "description": "Bitfield for current ingredient prime status of pumped ingredients", "address": 433, "boardName": "Pump Board 13", "boardId": 13, "paramType": "BYTE", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 112, "isPrivate": true}, {"id": 95679, "name": "Ingredient Line Status - T3", "macro": "EEP_INGR_LINE_STATUS_2", "description": "Bitfield for current ingredient prime status of pumped ingredients", "address": 434, "boardName": "Pump Board 13", "boardId": 13, "paramType": "BYTE", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 112, "isPrivate": true}, {"id": 95680, "name": "Ingredient Line Status - T4", "macro": "EEP_INGR_LINE_STATUS_3", "description": "Bitfield for current ingredient prime status of pumped ingredients", "address": 435, "boardName": "Pump Board 13", "boardId": 13, "paramType": "BYTE", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 112, "isPrivate": true}, {"id": 95681, "name": "Ingredient Line Status - T5", "macro": "EEP_INGR_LINE_STATUS_0", "description": "Bitfield for current ingredient prime status of pumped ingredients", "address": 432, "boardName": "Pump Board 14", "boardId": 14, "paramType": "BYTE", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 112, "isPrivate": true}, {"id": 95682, "name": "Ingredient Line Status - T6", "macro": "EEP_INGR_LINE_STATUS_1", "description": "Bitfield for current ingredient prime status of pumped ingredients", "address": 433, "boardName": "Pump Board 14", "boardId": 14, "paramType": "BYTE", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 112, "isPrivate": true}, {"id": 95683, "name": "Ingredient Line Status - T7", "macro": "EEP_INGR_LINE_STATUS_2", "description": "Bitfield for current ingredient prime status of pumped ingredients", "address": 434, "boardName": "Pump Board 14", "boardId": 14, "paramType": "BYTE", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 112, "isPrivate": true}, {"id": 95684, "name": "Ingredient Line Status - T8", "macro": "EEP_INGR_LINE_STATUS_3", "description": "Bitfield for current ingredient prime status of pumped ingredients", "address": 435, "boardName": "Pump Board 14", "boardId": 14, "paramType": "BYTE", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 112, "isPrivate": true}, {"id": 95685, "name": "Ingredient Line Status - Unused Bib", "macro": "EEP_BIB_LINE_STATUS_7", "description": "Bitfield for current ingredient prime status of ingredient", "address": 151, "boardName": "Nozzle", "boardId": 560, "paramType": "BYTE", "defaultParam": "0", "currentValue": "255", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 112, "isPrivate": true}, {"id": 95686, "name": "Ingredient Line Status - Water Bib", "macro": "EEP_BIB_LINE_STATUS_WATER", "description": "Bitfield for current ingredient prime status of ingredient", "address": 149, "boardName": "Nozzle", "boardId": 560, "paramType": "BYTE", "defaultParam": "0", "currentValue": "255", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 112, "isPrivate": true}, {"id": 95687, "name": "Ingredient Line Status Enable (T1 - T4)", "macro": "EEP_INGR_LINE_STATUS_EN", "description": "Enable ingredient prime status of pumped ingredients", "address": 436, "boardName": "Pump Board 13", "boardId": 13, "paramType": "BOOLEAN", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 112, "isPrivate": false}, {"id": 95688, "name": "Ingredient Line Status Enable (T5 - T8)", "macro": "EEP_INGR_LINE_STATUS_EN", "description": "Enable ingredient prime status of pumped ingredients", "address": 436, "boardName": "Pump Board 14", "boardId": 14, "paramType": "BOOLEAN", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 112, "isPrivate": false}, {"id": 95689, "name": "Ingredient Sensor Active Threshold - T1 - T4", "macro": "EEP_LEVEL_THRESHOLD", "description": "Ingredient level (expressed in 1/20 fl. oz. increments) below which runout detection becomes active for ingredients T1 - T4", "address": 256, "boardName": "Pump Board 13", "boardId": 13, "paramType": "WORD", "defaultParam": "100", "currentValue": "100", "newValue": null, "minParam": "10", "maxParam": "3500", "machineId": 112, "isPrivate": true}, {"id": 95690, "name": "Ingredient Sensor Active Threshold - T5 - T8", "macro": "EEP_LEVEL_THRESHOLD", "description": "Ingredient level (expressed in 1/20 fl. oz. increments) below which runout detection becomes active for ingredients T4 - T8", "address": 256, "boardName": "Pump Board 14", "boardId": 14, "paramType": "WORD", "defaultParam": "100", "currentValue": "100", "newValue": null, "minParam": "10", "maxParam": "3500", "machineId": 112, "isPrivate": true}, {"id": 95691, "name": "Ingredient Sensor Enable - T1 - T4", "macro": "EEP_BUBBLE_DETECT_ENABLE", "description": "Enable the use of ingredient detection sensors for a group of 4 house mix ingredients", "address": 160, "boardName": "Pump Board 13", "boardId": 13, "paramType": "BOOLEAN", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 112, "isPrivate": false}, {"id": 95692, "name": "Ingredient Sensor Enable - T5 - T8", "macro": "EEP_BUBBLE_DETECT_ENABLE", "description": "Enable the use of ingredient detection sensors for a group of 4 house mix ingredients", "address": 160, "boardName": "Pump Board 14", "boardId": 14, "paramType": "BOOLEAN", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 112, "isPrivate": false}, {"id": 95693, "name": "<PERSON>/<PERSON> De<PERSON>ult Ingredient Speed- T1 - T4", "macro": "EEP_OZMIN_INGR_SPEED_DEFAULT", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 416, "boardName": "Pump Board 13", "boardId": 13, "paramType": "SINGLE", "defaultParam": "22.5", "currentValue": "22.5", "newValue": null, "minParam": "7.5", "maxParam": "37.5", "machineId": 112, "isPrivate": true}, {"id": 95694, "name": "<PERSON>/<PERSON> De<PERSON> Ingredient Speed- T5 - T8", "macro": "EEP_OZMIN_INGR_SPEED_DEFAULT", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 416, "boardName": "Pump Board 14", "boardId": 14, "paramType": "SINGLE", "defaultParam": "22.5", "currentValue": "22.5", "newValue": null, "minParam": "7.5", "maxParam": "37.5", "machineId": 112, "isPrivate": true}, {"id": 95695, "name": "Oz/Min Ingredient Speed - T1", "macro": "EEP_OZMIN_INGR_SPEED_0", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 400, "boardName": "Pump Board 13", "boardId": 13, "paramType": "SINGLE", "defaultParam": "22.5", "currentValue": "22.5", "newValue": null, "minParam": "7.5", "maxParam": "37.5", "machineId": 112, "isPrivate": false}, {"id": 95696, "name": "Oz/Min Ingredient Speed - T2", "macro": "EEP_OZMIN_INGR_SPEED_1", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 404, "boardName": "Pump Board 13", "boardId": 13, "paramType": "SINGLE", "defaultParam": "22.5", "currentValue": "22.5", "newValue": null, "minParam": "7.5", "maxParam": "37.5", "machineId": 112, "isPrivate": false}, {"id": 95697, "name": "Oz/Min Ingredient Speed - T3", "macro": "EEP_OZMIN_INGR_SPEED_2", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 408, "boardName": "Pump Board 13", "boardId": 13, "paramType": "SINGLE", "defaultParam": "22.5", "currentValue": "22.5", "newValue": null, "minParam": "7.5", "maxParam": "37.5", "machineId": 112, "isPrivate": false}, {"id": 95698, "name": "Oz/Min Ingredient Speed - T4", "macro": "EEP_OZMIN_INGR_SPEED_3", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 412, "boardName": "Pump Board 13", "boardId": 13, "paramType": "SINGLE", "defaultParam": "22.5", "currentValue": "22.5", "newValue": null, "minParam": "7.5", "maxParam": "37.5", "machineId": 112, "isPrivate": false}, {"id": 95699, "name": "Oz/Min Ingredient Speed - T5", "macro": "EEP_OZMIN_INGR_SPEED_0", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 400, "boardName": "Pump Board 14", "boardId": 14, "paramType": "SINGLE", "defaultParam": "22.5", "currentValue": "22.5", "newValue": null, "minParam": "7.5", "maxParam": "37.5", "machineId": 112, "isPrivate": false}, {"id": 95700, "name": "Oz/Min Ingredient Speed - T6", "macro": "EEP_OZMIN_INGR_SPEED_1", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 404, "boardName": "Pump Board 14", "boardId": 14, "paramType": "SINGLE", "defaultParam": "22.5", "currentValue": "22.5", "newValue": null, "minParam": "7.5", "maxParam": "37.5", "machineId": 112, "isPrivate": false}, {"id": 95701, "name": "Oz/Min Ingredient Speed - T7", "macro": "EEP_OZMIN_INGR_SPEED_2", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 408, "boardName": "Pump Board 14", "boardId": 14, "paramType": "SINGLE", "defaultParam": "22.5", "currentValue": "22.5", "newValue": null, "minParam": "7.5", "maxParam": "37.5", "machineId": 112, "isPrivate": false}, {"id": 95702, "name": "Oz/Min Ingredient Speed - T8", "macro": "EEP_OZMIN_INGR_SPEED_3", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 412, "boardName": "Pump Board 14", "boardId": 14, "paramType": "SINGLE", "defaultParam": "22.5", "currentValue": "22.5", "newValue": null, "minParam": "7.5", "maxParam": "37.5", "machineId": 112, "isPrivate": false}, {"id": 95703, "name": "Priming Quantity - T1", "macro": "EEP_PRIMING_QTY_0", "description": "Priming quantity (expressed in 1/20 fl. oz. increments) for ingredient T1", "address": 84, "boardName": "Pump Board 13", "boardId": 13, "paramType": "BYTE", "defaultParam": "50", "currentValue": "50", "newValue": null, "minParam": "40", "maxParam": "110", "machineId": 112, "isPrivate": true}, {"id": 95704, "name": "Priming Quantity - T3", "macro": "EEP_PRIMING_QTY_2", "description": "Priming quantity (expressed in 1/20 fl. oz. increments) for ingredient T3", "address": 116, "boardName": "Pump Board 13", "boardId": 13, "paramType": "BYTE", "defaultParam": "65", "currentValue": "65", "newValue": null, "minParam": "40", "maxParam": "110", "machineId": 112, "isPrivate": true}, {"id": 95705, "name": "Priming Quantity - T4", "macro": "EEP_PRIMING_QTY_3", "description": "Priming quantity (expressed in 1/20 fl. oz. increments) for ingredient T4", "address": 132, "boardName": "Pump Board 13", "boardId": 13, "paramType": "BYTE", "defaultParam": "58", "currentValue": "58", "newValue": null, "minParam": "40", "maxParam": "110", "machineId": 112, "isPrivate": true}, {"id": 95706, "name": "Priming Quantity - T5", "macro": "EEP_PRIMING_QTY_0", "description": "Priming quantity (expressed in 1/20 fl. oz. increments) for ingredient A5", "address": 84, "boardName": "Pump Board 14", "boardId": 14, "paramType": "BYTE", "defaultParam": "50", "currentValue": "50", "newValue": null, "minParam": "40", "maxParam": "110", "machineId": 112, "isPrivate": true}, {"id": 95707, "name": "Priming Quantity - T6", "macro": "EEP_PRIMING_QTY_1", "description": "Priming quantity (expressed in 1/20 fl. oz. increments) for ingredient T6", "address": 100, "boardName": "Pump Board 14", "boardId": 14, "paramType": "BYTE", "defaultParam": "51", "currentValue": "51", "newValue": null, "minParam": "40", "maxParam": "110", "machineId": 112, "isPrivate": true}, {"id": 95708, "name": "Priming Quantity - T7", "macro": "EEP_PRIMING_QTY_2", "description": "Priming quantity (expressed in 1/20 fl. oz. increments) for ingredient T7", "address": 116, "boardName": "Pump Board 14", "boardId": 14, "paramType": "BYTE", "defaultParam": "65", "currentValue": "65", "newValue": null, "minParam": "40", "maxParam": "110", "machineId": 112, "isPrivate": true}, {"id": 95709, "name": "Priming Quantity - T8", "macro": "EEP_PRIMING_QTY_3", "description": "Priming quantity (expressed in 1/20 fl. oz. increments) for ingredient T8", "address": 132, "boardName": "Pump Board 14", "boardId": 14, "paramType": "BYTE", "defaultParam": "58", "currentValue": "58", "newValue": null, "minParam": "40", "maxParam": "110", "machineId": 112, "isPrivate": true}, {"id": 95710, "name": "Priming Quantity -T2", "macro": "EEP_PRIMING_QTY_1", "description": "Priming quantity (expressed in 1/20 fl. oz. increments) for ingredient T2", "address": 100, "boardName": "Pump Board 13", "boardId": 13, "paramType": "BYTE", "defaultParam": "51", "currentValue": "51", "newValue": null, "minParam": "40", "maxParam": "110", "machineId": 112, "isPrivate": true}, {"id": 95711, "name": "Refresh Line Timeout - T1", "macro": "EEP_REFRESH_TIMEOUT_0", "description": "Time (in seconds) this ingredients can remain in the lines without any dispensing operations. Set to 0 for no timeout.", "address": 336, "boardName": "Pump Board 13", "boardId": 13, "paramType": "WORD", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "10800", "machineId": 112, "isPrivate": true}, {"id": 95712, "name": "Refresh Line Timeout - T2", "macro": "EEP_REFRESH_TIMEOUT_1", "description": "Time (in seconds) this ingredients can remain in the lines without any dispensing operations. Set to 0 for no timeout.", "address": 338, "boardName": "Pump Board 13", "boardId": 13, "paramType": "WORD", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "10800", "machineId": 112, "isPrivate": true}, {"id": 95713, "name": "Refresh Line Timeout - T3", "macro": "EEP_REFRESH_TIMEOUT_2", "description": "Time (in seconds) this ingredients can remain in the lines without any dispensing operations. Set to 0 for no timeout.", "address": 340, "boardName": "Pump Board 13", "boardId": 13, "paramType": "WORD", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "10800", "machineId": 112, "isPrivate": true}, {"id": 95714, "name": "Refresh Line Timeout - T4", "macro": "EEP_REFRESH_TIMEOUT_3", "description": "Time (in seconds) this ingredients can remain in the lines without any dispensing operations. Set to 0 for no timeout.", "address": 342, "boardName": "Pump Board 13", "boardId": 13, "paramType": "WORD", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "10800", "machineId": 112, "isPrivate": true}, {"id": 95715, "name": "Refresh Line Timeout - T5", "macro": "EEP_REFRESH_TIMEOUT_0", "description": "Time (in seconds) this ingredients can remain in the lines without any dispensing operations. Set to 0 for no timeout.", "address": 336, "boardName": "Pump Board 14", "boardId": 14, "paramType": "WORD", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "10800", "machineId": 112, "isPrivate": true}, {"id": 95716, "name": "Refresh Line Timeout - T6", "macro": "EEP_REFRESH_TIMEOUT_1", "description": "Time (in seconds) this ingredients can remain in the lines without any dispensing operations. Set to 0 for no timeout.", "address": 338, "boardName": "Pump Board 14", "boardId": 14, "paramType": "WORD", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "10800", "machineId": 112, "isPrivate": true}, {"id": 95717, "name": "Refresh Line Timeout - T7", "macro": "EEP_REFRESH_TIMEOUT_2", "description": "Time (in seconds) this ingredients can remain in the lines without any dispensing operations. Set to 0 for no timeout.", "address": 340, "boardName": "Pump Board 14", "boardId": 14, "paramType": "WORD", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "10800", "machineId": 112, "isPrivate": true}, {"id": 95718, "name": "Refresh Line Timeout - T8", "macro": "EEP_REFRESH_TIMEOUT_3", "description": "Time (in seconds) this ingredients can remain in the lines without any dispensing operations. Set to 0 for no timeout.", "address": 342, "boardName": "Pump Board 14", "boardId": 14, "paramType": "WORD", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "10800", "machineId": 112, "isPrivate": true}, {"id": 95719, "name": "Shutdown Prep Quantity", "macro": "EEP_SHUTDOWN_PREP_QTY", "description": "The volume of liquid to backtrack before shutdown to ensure there is no cross contamination between ingredients in the nozzle cap (in 1/20th oz increments)", "address": 394, "boardName": "Master", "boardId": 65535, "paramType": "BYTE", "defaultParam": "2", "currentValue": "2", "newValue": null, "minParam": "0", "maxParam": "60", "machineId": 112, "isPrivate": false}, {"id": 95720, "name": "Soaking Lines", "macro": "EEP_SOAKING_LINES", "description": "Bitfield of which dispensing lines are soaking with water or cleaner", "address": 240, "boardName": "Master", "boardId": 65535, "paramType": "DWORD", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "1048575", "machineId": 112, "isPrivate": true}, {"id": 95721, "name": "Startup Prep Quantity", "macro": "EEP_STARTUP_PREP_QTY", "description": "The volume of liquid to dispense before startup to ensure all ingredients are primed and ready to dispense into the first drink (in 1/20th oz increments)", "address": 395, "boardName": "Master", "boardId": 65535, "paramType": "BYTE", "defaultParam": "7", "currentValue": "7", "newValue": null, "minParam": "0", "maxParam": "60", "machineId": 112, "isPrivate": false}, {"id": 96564, "name": "Ingredient Sensor Enable - A5, A6, B5, B6", "macro": "EEP_BUBBLE_DETECT_ENABLE`", "description": "Enable the use of ingredient detection sensors for a group of 4 house mix ingredients", "address": 160, "boardName": "Pump Board 9", "boardId": 9, "paramType": "BOOLEAN", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 112, "isPrivate": false}, {"id": 96565, "name": "Pickup LEDs Always On", "macro": "EEP_OUTPUT_LEDS_ON", "description": "Force the output/pickup LEDs to remain on until a drink/order is picked up", "address": 64, "boardName": "Master", "boardId": 65535, "paramType": "BOOLEAN", "defaultParam": " 0", "currentValue": " 0", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 112, "isPrivate": false}, {"id": 96566, "name": "Ingredient Line Status - Unused Bib", "macro": "EEP_BIB_LINE_STATUS_8", "description": "Bitfield for current ingredient prime status of ingredient", "address": 151, "boardName": "Nozzle", "boardId": 560, "paramType": "BYTE", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 112, "isPrivate": true}, {"id": 96567, "name": "<PERSON><PERSON><PERSON> (2) Current Maximum", "macro": "EEP_PERIPH_CHAS_FAN2_MAX_CURRENT", "description": "ADC maximum value of normal operation for the external chassis fan (2) (Current = ADC * 5/4096)", "address": 302, "boardName": "Cooling", "boardId": 576, "paramType": "WORD", "defaultParam": "819", "currentValue": "819", "newValue": null, "minParam": "0", "maxParam": "1500", "machineId": 112, "isPrivate": false}, {"id": 96568, "name": "External Fan 4 Current Maximum", "macro": "EEP_PERIPH_EXT_FAN4_MAX_CURRENT", "description": "ADC maximum value of normal operation for the external fan (4) (Current = ADC * 5/4096)", "address": 300, "boardName": "Cooling", "boardId": 576, "paramType": "WORD", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "1500", "machineId": 112, "isPrivate": false}, {"id": 96570, "name": "<PERSON><PERSON><PERSON> (1) Current Maximum", "macro": "EEP_PERIPH_CHAS_FAN1_MAX_CURRENT", "description": "ADC maximum value of normal operation for the external chassis fan (1) (Current = ADC * 5/4096)", "address": 298, "boardName": "Cooling", "boardId": 576, "paramType": "WORD", "defaultParam": "819", "currentValue": "819", "newValue": null, "minParam": "0", "maxParam": "1500", "machineId": 112, "isPrivate": false}, {"id": 96571, "name": "Compressor Fan Current Maximum", "macro": "EEP_PERIPH_COMP_FAN_MAX_CURRENT", "description": "ADC maximum value of normal operation for the fridge’s external compressor fan (Current = ADC * 5/4096)", "address": 296, "boardName": "Cooling", "boardId": 576, "paramType": "WORD", "defaultParam": "819", "currentValue": "819", "newValue": null, "minParam": "0", "maxParam": "1500", "machineId": 112, "isPrivate": false}, {"id": 96572, "name": "Internal Fan (Right) Current Maximum", "macro": "EEP_PERIPH_INT_RGHT_MAX_CURRENT", "description": "ADC maximum value of normal operation for the fridge’s right internal fan (Current = ADC * 5/4096)", "address": 294, "boardName": "Cooling", "boardId": 576, "paramType": "WORD", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "1500", "machineId": 112, "isPrivate": false}, {"id": 96573, "name": "Internal Fan (Left) Current Maximum", "macro": "EEP_PERIPH_INT_LFT_MAX_CURRENT", "description": "ADC maximum value of normal operation for the fridge’s left internal fan (Current = ADC * 5/4096)", "address": 292, "boardName": "Cooling", "boardId": 576, "paramType": "WORD", "defaultParam": "1229", "currentValue": "1229", "newValue": null, "minParam": "0", "maxParam": "1500", "machineId": 112, "isPrivate": false}, {"id": 96574, "name": "Internal Fan (Top) Current Maximum", "macro": "EEP_PERIPH_INT_TOP_MAX_CURRENT", "description": "ADC maximum value of normal operation for the fridge’s top internal fan (Current = ADC * 5/4096)", "address": 290, "boardName": "Cooling", "boardId": 576, "paramType": "WORD", "defaultParam": "1229", "currentValue": "1229", "newValue": null, "minParam": "0", "maxParam": "1500", "machineId": 112, "isPrivate": false}, {"id": 96575, "name": "Compressor Current Maximum", "macro": "EEP_PERIPH_COMPRESSOR_MAX_CURRENT", "description": "ADC maximum value of normal operation for the fridge compressor (Current = ADC * 102.4)", "address": 288, "boardName": "Cooling", "boardId": 576, "paramType": "WORD", "defaultParam": "204", "currentValue": "204", "newValue": null, "minParam": "0", "maxParam": "1500", "machineId": 112, "isPrivate": false}, {"id": 96576, "name": "<PERSON><PERSON><PERSON> (2) Current Minimum", "macro": "EEP_PERIPH_CHAS_FAN2_MIN_CURRENT", "description": "ADC minimum value of normal operation for the external chassis fan (2) (Current = ADC * 5/4096)", "address": 270, "boardName": "Cooling", "boardId": 576, "paramType": "WORD", "defaultParam": "163", "currentValue": "163", "newValue": null, "minParam": "0", "maxParam": "1500", "machineId": 112, "isPrivate": false}, {"id": 96577, "name": "External Fan 4 Current Minimum", "macro": "EEP_PERIPH_EXT_FAN4_MIN_CURRENT", "description": "ADC minimum value of normal operation for the external fan (4) (Current = ADC * 5/4096)", "address": 268, "boardName": "Cooling", "boardId": 576, "paramType": "WORD", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "1500", "machineId": 112, "isPrivate": false}, {"id": 96578, "name": "<PERSON><PERSON><PERSON> (1) Current Minimum", "macro": "EEP_PERIPH_CHAS_FAN1_MIN_CURRENT", "description": "ADC minimum value of normal operation for the external chassis fan (1) (Current = ADC * 5/4096)", "address": 266, "boardName": "Cooling", "boardId": 576, "paramType": "WORD", "defaultParam": "163", "currentValue": "163", "newValue": null, "minParam": "0", "maxParam": "1500", "machineId": 112, "isPrivate": false}, {"id": 96579, "name": "Compressor Fan Current Minimum", "macro": "EEP_PERIPH_COMP_FAN_MIN_CURRENT", "description": "ADC minimum value of normal operation for the fridge’s external compressor fan (Current = ADC * 5/4096)", "address": 264, "boardName": "Cooling", "boardId": 576, "paramType": "WORD", "defaultParam": "327", "currentValue": "327", "newValue": null, "minParam": "0", "maxParam": "1500", "machineId": 112, "isPrivate": false}, {"id": 96580, "name": "Internal Fan (Right) Current Minimum", "macro": "EEP_PERIPH_INT_RGHT_MIN_CURRENT", "description": "ADC minimum value of normal operation for the fridge’s right internal fan (Current = ADC * 5/4096)", "address": 262, "boardName": "Cooling", "boardId": 576, "paramType": "WORD", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "1500", "machineId": 112, "isPrivate": false}, {"id": 96581, "name": "Internal Fan (Left) Current Minimum", "macro": "EEP_PERIPH_INT_LFT_MIN_CURRENT", "description": "ADC minimum value of normal operation for the fridge’s left internal fan (Current = ADC * 5/4096)", "address": 260, "boardName": "Cooling", "boardId": 576, "paramType": "WORD", "defaultParam": "491", "currentValue": "491", "newValue": null, "minParam": "0", "maxParam": "1500", "machineId": 112, "isPrivate": false}, {"id": 96582, "name": "Internal Fan (Top) Current Minimum", "macro": "EEP_PERIPH_INT_TOP_MIN_CURRENT", "description": "ADC minimum value of normal operation for the fridge’s top internal fan (Current = ADC * 5/4096)", "address": 258, "boardName": "Cooling", "boardId": 576, "paramType": "WORD", "defaultParam": "491", "currentValue": "491", "newValue": null, "minParam": "0", "maxParam": "1500", "machineId": 112, "isPrivate": false}, {"id": 96583, "name": "Compressor Current Minimum", "macro": "EEP_PERIPH_COMPRESSOR_MIN_CURRENT", "description": "ADC minimum value of normal operation for the fridge compressor (Current = ADC * 102.4)", "address": 256, "boardName": "Cooling", "boardId": 576, "paramType": "WORD", "defaultParam": "25", "currentValue": "25", "newValue": null, "minParam": "0", "maxParam": "1500", "machineId": 112, "isPrivate": false}, {"id": 96584, "name": "Compressor Minimum Off Time", "macro": "EEP_COMPRESSOR_MIN_OFFTIME", "description": "Timeout used for allowing evaporator plate to thaw before turning the compressor back on. Ignored in machines that have an evaporator plate sensor", "address": 214, "boardName": "Cooling", "boardId": 576, "paramType": "WORD", "defaultParam": "900", "currentValue": "900", "newValue": null, "minParam": "60", "maxParam": "7200", "machineId": 112, "isPrivate": false}, {"id": 96606, "name": "Bib Cleaning Quantity (every drink)", "macro": "EEP_CLEAN_BIB_QTY", "description": "Amount (in 1/20 fl oz increments) of water to dispense after a drink is dispensed to clean the bib and diffuser. This is also 1/4 of the amount of water used to clean the bib during refresh cycles if ", "address": 336, "boardName": "Master", "boardId": 65535, "paramType": "WORD", "defaultParam": "10", "currentValue": "10", "newValue": null, "minParam": "0", "maxParam": "20", "machineId": 112, "isPrivate": false}, {"id": 96607, "name": "Enable Water Rinse", "macro": "EEP_ENABLE_WATER_RINSE", "description": "Spray water into the bib during a refresh to clean out the nozzle", "address": 371, "boardName": "Master", "boardId": 65535, "paramType": "BOOLEAN", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 112, "isPrivate": false}, {"id": 96608, "name": "Shutdown Prep Volume", "macro": "EEP_SHUTDOWN_PREP_VOL", "description": "The volume of liquid to backtrack before shutdown to ensure there is no cross contamination between ingredients in the nozzle cap (in 1/20th oz increments)", "address": 394, "boardName": "Master", "boardId": 65535, "paramType": "BYTE", "defaultParam": "2", "currentValue": "2", "newValue": null, "minParam": "0", "maxParam": "60", "machineId": 112, "isPrivate": false}, {"id": 96609, "name": "Startup Prep Volume", "macro": "EEP_STARTUP_PREP_VOL", "description": "The volume of liquid to dispense before startup to ensure all ingredients are primed and ready to dispense into the first drink (in 1/20th oz increments)", "address": 395, "boardName": "Master", "boardId": 65535, "paramType": "BYTE", "defaultParam": "7", "currentValue": "7", "newValue": null, "minParam": "0", "maxParam": "60", "machineId": 112, "isPrivate": false}, {"id": 98843, "name": "Retraction Quantity C1, D1, E1, F1", "macro": "EEP_RETRACTION_QTY", "description": "Amount to retract (expressed in 1/20 fl. oz. increments) after a forward dispense ", "address": 278, "boardName": "Pump Board 0", "boardId": 0, "paramType": "BYTE", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "20", "machineId": 112, "isPrivate": false}, {"id": 98844, "name": "Retraction Quantity C2, E2, D2, F2", "macro": "EEP_RETRACTION_QTY", "description": "Amount to retract (expressed in 1/20 fl. oz. increments) after a forward dispense ", "address": 278, "boardName": "Pump Board 1", "boardId": 1, "paramType": "BYTE", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "20", "machineId": 112, "isPrivate": false}, {"id": 98845, "name": "Retraction Quantity C3, D3, E3, F3", "macro": "EEP_RETRACTION_QTY", "description": "Amount to retract (expressed in 1/20 fl. oz. increments) after a forward dispense ", "address": 278, "boardName": "Pump Board 2", "boardId": 2, "paramType": "BYTE", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "20", "machineId": 112, "isPrivate": false}, {"id": 98846, "name": "Retraction Quantity C4, D4, E4, F4", "macro": "EEP_RETRACTION_QTY", "description": "Amount to retract (expressed in 1/20 fl. oz. increments) after a forward dispense ", "address": 278, "boardName": "Pump Board 3", "boardId": 3, "paramType": "BYTE", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "20", "machineId": 112, "isPrivate": false}, {"id": 98847, "name": "Retraction Quantity C5, D5, E5, F5", "macro": "EEP_RETRACTION_QTY", "description": "Amount to retract (expressed in 1/20 fl. oz. increments) after a forward dispense ", "address": 278, "boardName": "Pump Board 4", "boardId": 4, "paramType": "BYTE", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "20", "machineId": 112, "isPrivate": false}, {"id": 98848, "name": "Retraction Quantity C6, D6, E6, F6", "macro": "EEP_RETRACTION_QTY", "description": "Amount to retract (expressed in 1/20 fl. oz. increments) after a forward dispense ", "address": 278, "boardName": "Pump Board 5", "boardId": 5, "paramType": "BYTE", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "20", "machineId": 112, "isPrivate": false}, {"id": 98849, "name": "Retraction Quantity A1, A2, B1, B2", "macro": "EEP_RETRACTION_QTY", "description": "Amount to retract (expressed in 1/20 fl. oz. increments) after a forward dispense ", "address": 278, "boardName": "Pump Board 7", "boardId": 7, "paramType": "BYTE", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "20", "machineId": 112, "isPrivate": false}, {"id": 98850, "name": "Retraction Quantity A3, A4, B3, B4", "macro": "EEP_RETRACTION_QTY", "description": "Amount to retract (expressed in 1/20 fl. oz. increments) after a forward dispense ", "address": 278, "boardName": "Pump Board 8", "boardId": 8, "paramType": "BYTE", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "20", "machineId": 112, "isPrivate": false}, {"id": 98851, "name": "Retraction Quantity A5, A6, B5, B6", "macro": "EEP_RETRACTION_QTY", "description": "Amount to retract (expressed in 1/20 fl. oz. increments) after a forward dispense ", "address": 278, "boardName": "Pump Board 9", "boardId": 9, "paramType": "BYTE", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "20", "machineId": 112, "isPrivate": false}, {"id": 98852, "name": "Startup Prime Setting - C1, D1, E1, F1", "macro": "EEP_STARTUP_PRIME_MODE", "description": "Setting to indicate what ingredients to prime at startup.\n0: Prime Shelf Stable ingredients ONLY\n1: Prime ALL ingredients regardless of refresh timer\n2: Leave ALL ingredients UNPRIMED", "address": 306, "boardName": "Pump Board 0", "boardId": 0, "paramType": "BYTE", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "2", "machineId": 112, "isPrivate": false}, {"id": 98853, "name": "Startup Prime Setting - C2, E2, D2, F2", "macro": "EEP_STARTUP_PRIME_MODE", "description": "Setting to indicate what ingredients to prime at startup.\n0: Prime Shelf Stable ingredients ONLY\n1: Prime ALL ingredients regardless of refresh timer\n2: Leave ALL ingredients UNPRIMED", "address": 306, "boardName": "Pump Board 1", "boardId": 1, "paramType": "BYTE", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "2", "machineId": 112, "isPrivate": false}, {"id": 98854, "name": "Startup Prime Setting - C3, D3, E3, F3", "macro": "EEP_STARTUP_PRIME_MODE", "description": "Setting to indicate what ingredients to prime at startup.\n0: Prime Shelf Stable ingredients ONLY\n1: Prime ALL ingredients regardless of refresh timer\n2: Leave ALL ingredients UNPRIMED", "address": 306, "boardName": "Pump Board 2", "boardId": 2, "paramType": "BYTE", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "2", "machineId": 112, "isPrivate": false}, {"id": 98855, "name": "Startup Prime Setting - C4, D4, E4, F4", "macro": "EEP_STARTUP_PRIME_MODE", "description": "Setting to indicate what ingredients to prime at startup.\n0: Prime Shelf Stable ingredients ONLY\n1: Prime ALL ingredients regardless of refresh timer\n2: Leave ALL ingredients UNPRIMED", "address": 306, "boardName": "Pump Board 3", "boardId": 3, "paramType": "BYTE", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "2", "machineId": 112, "isPrivate": false}, {"id": 98856, "name": "Startup Prime Setting - C5, D5, E5, F5", "macro": "EEP_STARTUP_PRIME_MODE", "description": "Setting to indicate what ingredients to prime at startup.\n0: Prime Shelf Stable ingredients ONLY\n1: Prime ALL ingredients regardless of refresh timer\n2: Leave ALL ingredients UNPRIMED", "address": 306, "boardName": "Pump Board 4", "boardId": 4, "paramType": "BYTE", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "2", "machineId": 112, "isPrivate": false}, {"id": 98857, "name": "Startup Prime Setting - C6, D6, E6, F6", "macro": "EEP_STARTUP_PRIME_MODE", "description": "Setting to indicate what ingredients to prime at startup.\n0: Prime Shelf Stable ingredients ONLY\n1: Prime ALL ingredients regardless of refresh timer\n2: Leave ALL ingredients UNPRIMED", "address": 306, "boardName": "Pump Board 5", "boardId": 5, "paramType": "BYTE", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "2", "machineId": 112, "isPrivate": false}, {"id": 98858, "name": "Startup Prime Setting - A1, A2, B1, B2", "macro": "EEP_STARTUP_PRIME_MODE", "description": "Setting to indicate what ingredients to prime at startup.\n0: Prime Shelf Stable ingredients ONLY\n1: Prime ALL ingredients regardless of refresh timer\n2: Leave ALL ingredients UNPRIMED", "address": 306, "boardName": "Pump Board 7", "boardId": 7, "paramType": "BYTE", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "2", "machineId": 112, "isPrivate": false}, {"id": 98859, "name": "Startup Prime Setting - A3, A4, B3, B4", "macro": "EEP_STARTUP_PRIME_MODE", "description": "Setting to indicate what ingredients to prime at startup.\n0: Prime Shelf Stable ingredients ONLY\n1: Prime ALL ingredients regardless of refresh timer\n2: Leave ALL ingredients UNPRIMED", "address": 306, "boardName": "Pump Board 8", "boardId": 8, "paramType": "BYTE", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "2", "machineId": 112, "isPrivate": false}, {"id": 98860, "name": "Startup Prime Setting - A5, A6, B5, B6", "macro": "EEP_STARTUP_PRIME_MODE", "description": "Setting to indicate what ingredients to prime at startup.\n0: Prime Shelf Stable ingredients ONLY\n1: Prime ALL ingredients regardless of refresh timer\n2: Leave ALL ingredients UNPRIMED", "address": 306, "boardName": "Pump Board 9", "boardId": 9, "paramType": "BYTE", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "2", "machineId": 112, "isPrivate": false}]