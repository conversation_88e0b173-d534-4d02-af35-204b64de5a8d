import subprocess

# Define commands here — easy to add/remove without touching main code
# Key: menu label, Value: (list_of_args, requires_serial)
COMMANDS = {
    "Import all hardware from Notion to Admin. Does not change a": (["import-all"], False),
    "Add and apply hardware to a machine": (["machine-all-hardware"], True),
    "Remove all hardware from a machine": (["remove-all-hardware"], True),

}

# Display menu
print("\nAvailable actions:")
for i, name in enumerate(COMMANDS, start=1):
    print(f"{i} - {name}")

# Get choice
choice = input("\nEnter choice number: ")

try:
    index = int(choice) - 1
    if not (0 <= index < len(COMMANDS)):
        raise ValueError
except ValueError:
    print("Invalid choice.")
    exit(1)

# Get selected command
name = list(COMMANDS.keys())[index]
args, needs_serial = COMMANDS[name]

# Add serial if needed
if needs_serial:
    serial = input("Enter serial number: ")
    args.extend(["--serial", serial])

# Run command
subprocess.run(["python", "hardware_import.py", *args])