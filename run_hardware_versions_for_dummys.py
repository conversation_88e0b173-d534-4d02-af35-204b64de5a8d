import subprocess

# Define commands here — easy to add/remove without touching main code
# Key: menu label, Value: (list_of_args, requires_serial, requires_hardware, requires_revision, requires_db_type, requires_database_id)
COMMANDS = {
    # Import/Export Commands (No CSV)
    "Import all hardware from Notion to Admin. Does not change anything on specific machines.": (["import-all"], False, False, False, False, False),

    # Settings Import Commands (No CSV)
    "Import EEPROM settings from Notion database": (["settings", "notion"], False, True, True, False, True),
    "Import Board Records from Notion database": (["boards"], False, True, True, False, True),
    "Import App Settings from Notion database": (["app-settings"], False, True, True, False, True),
    "Import specific database for hardware type": (["import-specific"], False, True, False, True, False),

    # Machine Hardware Commands
    "Add AND apply hardware to a SPECIFIC machine. This will check all hardware, remove any old revisions, and add the correct revision, and then apply it. If hardware already exists, it will not add, just apply.": (["machine-all-hardware"], True, False, False, False, False),
    "Add AND apply SPECIFIC hardware to a SPECIFIC machine": (["machine-hardware"], True, True, True, False, False),
    "Add SPECIFIC hardware to SPECIFIC machine (don't apply settings)": (["machine-hardware", "--add-only"], True, True, True, False, False),
    "Apply SPECIFIC hardware settings to SPECIFIC machine (don't add hardware)": (["machine-hardware", "--apply-only"], True, True, True, False, False),
    "Add ALL hardware to SPECIFIC machine (don't apply settings)": (["machine-all-hardware", "--add-only"], True, False, False, False, False),
    "Apply ALL hardware settings to SPECIFIC machine (don't add hardware)": (["machine-all-hardware", "--apply-only"], True, False, False, False, False),
    "Remove all hardware from a SPECIFIC machine. Just deletes it all.": (["remove-all-hardware"], True, False, False, False, False),

    # Hardware Assignment Commands
    "Add AND apply ALL hardware to ALL machines from Notion database": (["apply-hardware"], False, False, False, False, True),
    "Add ALL hardware to ALL machines (from Notion)": (["apply-hardware", "--add-only"], False, False, False, False, True),
    "Apply ALL hardware settings only (from Notion)": (["apply-hardware", "--apply-only"], False, False, False, False, True),

    # Audit Commands
    "Audit machine settings against hardware definitions": (["audit-machine"], True, False, False, False, False),

    # Version Management Commands
    "Check and update machine hardware versions against Notion": (["check-version"], True, False, False, False, False),
}

# Display menu
print("\nAvailable actions:")
for i, name in enumerate(COMMANDS, start=1):
    print(f"{i} - {name}")

# Get choice
choice = input("\nEnter choice number: ")

try:
    index = int(choice) - 1
    if not (0 <= index < len(COMMANDS)):
        raise ValueError
except ValueError:
    print("Invalid choice.")
    exit(1)

# Get selected command
name = list(COMMANDS.keys())[index]
args, needs_serial, needs_hardware, needs_revision, needs_db_type, needs_database_id = COMMANDS[name]

# Make a copy of args to avoid modifying the original
command_args = args.copy()

# Add serial if needed
if needs_serial:
    serial = str(input("Enter serial number: "))
    if len(serial) > 5:
        print("Invalid serial number. Must be 5 digits.")
        exit(1)
    elif len(serial) < 5:
        serial = "0" * (5 - len(serial)) + serial
    command_args.extend(["--serial", serial])

# Add hardware if needed
if needs_hardware:
    hardware = input("Enter hardware name (e.g., 'Cooling', 'Self Cleaning'): ")
    command_args.extend(["--hardware", hardware])

# Add revision if needed
if needs_revision:
    revision = input("Enter hardware revision (e.g., 'A', 'B', 'Rev A', 'Rev B'): ")
    command_args.extend(["--revision", revision])

# Add database type if needed
if needs_db_type:
    print("Available database types: eeprom, boards, app_settings")
    db_type = input("Enter database type: ")
    command_args.extend(["--db-type", db_type])

# Add database ID if needed
if needs_database_id:
    database_id = input("Enter Notion database ID (or press Enter to use default from .env): ")
    if database_id.strip():
        command_args.extend(["--database-id", database_id])

# Handle special cases for export and board commands
if "export" in command_args:
    csv_file = input("Enter CSV file path to export to: ")
    command_args.append(csv_file)
elif "create-board" in command_args or "update-board" in command_args:
    print("Note: Board creation/update will run in interactive mode if parameters are not provided")
    if "update-board" in command_args:
        board_id = input("Enter board ID to update: ")
        command_args.append(board_id)

# Handle settings/boards/app-settings commands that need database_id as positional argument
if any(cmd in command_args for cmd in ["settings", "boards", "app-settings"]) and "notion" in command_args:
    if not needs_database_id or not any("--database-id" in str(arg) for arg in command_args):
        database_id = input("Enter Notion database ID: ")
        # Insert database_id as positional argument after the command
        if "settings" in command_args:
            insert_index = command_args.index("notion") + 1
        elif "boards" in command_args:
            insert_index = len(command_args)
        elif "app-settings" in command_args:
            insert_index = len(command_args)
        command_args.insert(insert_index, database_id)

    # Add hardware-id for these commands
    hardware_id = input("Enter hardware ID (integer): ")
    command_args.extend(["--hardware-id", hardware_id])

# Run command
print(f"\nRunning: python hardware_import.py {' '.join(command_args)}")
subprocess.run(["python", "hardware_import.py", *command_args])