#!/usr/bin/env python3
import csv
import argparse
import os
import json
import sys
from typing import List, Dict, Any, <PERSON>ple
from dotenv import load_dotenv
from notion_client import Client
from hardware_manager import HardwareManager, HardwareType, SettingParamType, ParamType 
import requests


# Load environment variables from .env file
load_dotenv()

def is_na(value: str) -> bool:
    """Check if a value is NA/N/A (case insensitive)"""
    if not value:
        return True
    try:
        return value.strip().lower() in ['na', 'n/a', '']
    except:
        return False

def settings_are_different(existing: Dict, new: Dict) -> Tuple[bool, List[str]]:
    """
    Compare existing and new settings to determine if an update is needed.
    Returns (needs_update, changed_fields)
    """
    changes = []
    
    # Fields to compare and their mapping from CSV to API
    fields_to_check = {
        'name': 'name',
        'macro': 'macro',
        'description': 'description',
        'address': 'address',
        'board_name': 'boardName',
        'board_id': 'boardId',
        'param_type': 'paramType',
        'default_param': 'defaultParam',
        'min_param': 'minParam',
        'max_param': 'maxParam',
        'is_private': 'isPrivate'
    }
    
    for new_field, existing_field in fields_to_check.items():
        # Convert both values to strings before comparison, handling numeric types
        new_value = str(new[new_field]) if new[new_field] is not None else ''
        existing_value = str(existing.get(existing_field, '')) if existing.get(existing_field) is not None else ''
        
        
        try: 
            # Strip whitespace after converting to string
            new_value = new_value.strip()
            existing_value = existing_value.strip()
        except AttributeError:
            pass

        # For numeric fields, try to compare as numbers to avoid false positives due to string formatting
        if new_field in ['address', 'board_id']:
            try:
                if int(new_value or 0) != int(existing_value or 0):
                    changes.append(f"{existing_field}: '{existing_value}' → '{new_value}'")
            except ValueError:
                changes.append(f"{existing_field}: '{existing_value}' → '{new_value}'")
        # For float values, compare with some tolerance
        elif new_field in ['default_param', 'min_param', 'max_param'] and new.get('param_type') == 'SINGLE':
            try:
                new_float = float(new_value or 0)
                existing_float = float(existing_value or 0)
                if abs(new_float - existing_float) > 0.0001:  # Small tolerance for float comparison
                    changes.append(f"{existing_field}: '{existing_value}' → '{new_value}'")
            except ValueError:
                changes.append(f"{existing_field}: '{existing_value}' → '{new_value}'")
        # For boolean values, compare normalized boolean values
        elif new_field == 'is_private':
            # Convert to actual boolean values for comparison
            new_bool = new[new_field] if isinstance(new[new_field], bool) else str(new_value).lower() in ['true', '1', 'yes', 'y']
            existing_bool = existing.get(existing_field, False) if isinstance(existing.get(existing_field), bool) else str(existing_value).lower() in ['true', '1', 'yes', 'y']
            
            if new_bool != existing_bool:
                changes.append(f"{existing_field}: '{existing_bool}' → '{new_bool}'")
        # For all other fields, compare as strings
        elif new_value != existing_value:
            changes.append(f"{existing_field}: '{existing_value}' → '{new_value}'")
    
    return bool(changes), changes

def import_hardware_from_csv(csv_file: str, manager: HardwareManager) -> None:
    """Import hardware definitions from a CSV file, skipping existing ones
    Expected CSV format:
    name,type,rev,description
    """
    # First, get all existing hardware
    existing_hardware = manager.list_hardware(all_revisions=True)
    
    # Create a set of existing hardware identifiers (name + rev combination)
    existing_hardware_keys = {(h['name'], h['rev']) for h in existing_hardware}

    with open(csv_file, 'r') as f:
        reader = csv.DictReader(f)
        skipped_count = 0
        created_count = 0
        error_count = 0
        
        for row in reader:
            try:
                # Validate hardware type
                if row['Type'] not in [t.value for t in HardwareType]:
                    print(f"Invalid hardware type '{row['Type']}' for {row['Name']}, skipping...")
                    error_count += 1
                    continue

                # Check if hardware already exists
                if (row['Name'], row['Rev']) in existing_hardware_keys:
                    #print(f"Skipping existing hardware: {row['Name']} (Rev {row['Rev']})")
                    skipped_count += 1
                    continue

                # Create new hardware
                print(f"Creating new hardware: {row['Name']} (Rev {row['Rev']})")
                manager.create_hardware(
                    row['Name'],
                    row['Type'],
                    row['Rev'],
                    row.get('Description', '')
                )
                created_count += 1

            except Exception as e:
                print(f"Error processing hardware {row['name']}: {e}")
                error_count += 1

        # Print summary
        print("\nImport Summary:")
        print(f"Successfully created: {created_count} hardware")
        print(f"Skipped (already exists): {skipped_count} hardware")
        print(f"Errors: {error_count} hardware")

def normalize_param_type(type_str: str) -> str:
    """Normalize parameter type string to match SettingParamType enum"""
    type_mapping = {
        '8-bit uint': 'BYTE',
        '16-bit uint': 'WORD',
        '32-bit uint': 'DWORD',
        'boolean': 'BOOLEAN',
        'float': 'SINGLE'
    }
    
    normalized = type_mapping.get(type_str.lower())
    if not normalized:
        raise ValueError(f"Unsupported parameter type: {type_str}")
    return normalized

def import_settings_from_csv(csv_file: str, manager: HardwareManager, hardware_id: int, revision: str) -> None:
    """Import settings for a specific hardware from CSV file"""
    existing_settings = manager.list_hardware_settings(hardware_id)
    existing_settings_dict = {(s['name'], s['address']): s for s in existing_settings}
    
    created_count = 0
    updated_count = 0
    skipped_count = 0
    error_count = 0

    try:
        with open(csv_file, 'r', encoding='utf-8-sig') as f:
            reader = csv.DictReader(f)
            for row_num, row in enumerate(reader, start=2):
                try:
                    # Parse basic setting info using exact CSV column names
                    try:
                        setting_name = row['Setting Name']
                        setting_address = int(row['Address'])
                        setting_value = row[revision]  # Rev A, Rev B, Rev C, or Rev D
                        param_type = normalize_param_type(row['Type'])

                        if is_na(setting_value):
                            #print(f"Row {row_num}: Skipping setting '{setting_name}' - no value for {revision}")
                            skipped_count += 1
                            continue
                            
                    except KeyError as e:
                        print(f"Row {row_num}: Missing required column: {e}")
                        error_count += 1
                        continue
                    except ValueError as e:
                        print(f"Row {row_num}: Invalid address format or parameter type: {e}")
                        error_count += 1
                        continue

                    # Prepare the new setting data using exact CSV column names
                    new_setting = {
                        'name': setting_name,
                        'macro': row['Setting Macro'].split(' ')[0],  # Get macro without URL
                        'description': row['Setting Description'],
                        'address': setting_address,
                        'board_name': row['Board Name'],
                        'board_id': int(row['Board ID']),
                        'param_type': param_type,
                        'default_param': setting_value,
                        'min_param': '0' if param_type == 'boolean' else row['Min'],
                        'max_param': '1' if param_type == 'boolean' else row['Max'],
                        'is_private': row.get('Private?', '').lower() in ['true', '1', 'yes', 'y']
                    }

                    # Validate the new setting data
                    if any(is_na(new_setting[field]) for field in ['name', 'macro', 'address', 'board_name', 'board_id', 'param_type']):
                        print(f"Row {row_num}: Skipping setting '{setting_name}' - missing required fields")
                        skipped_count += 1
                        continue

                    # Check if setting exists and needs update
                    existing_setting = existing_settings_dict.get((setting_name, setting_address))
                    
                    if existing_setting:
                        needs_update, changes = settings_are_different(existing_setting, new_setting)
                        
                        if needs_update:
                            print(f"Updating setting: {setting_name} (Address {setting_address})")
                            for change in changes:
                                print(f"  • {change}")
                            
                            manager.update_hardware_setting(
                                setting_id=existing_setting['id'],
                                hardware_id=hardware_id,
                                **new_setting
                            )
                            updated_count += 1
                        else:
                            #print(f"Row {row_num}: Skipping setting (no changes needed): {setting_name} (Address {setting_address})")
                            skipped_count += 1
                    else:
                        print(f"Creating new setting: {setting_name} (Address {setting_address})")
                        manager.create_hardware_setting(
                            hardware_id=hardware_id,
                            **new_setting
                        )
                        created_count += 1

                except Exception as e:
                    print(f"Row {row_num}: Error processing setting {row.get('Setting Name', 'Unknown')}: {e}")
                    error_count += 1

    except Exception as e:
        print(f"Error reading CSV file: {e}")
        return

    # Print summary
    print("\nImport Summary:")
    print(f"Created new settings: {created_count}")
    print(f"Updated existing settings: {updated_count}")
    print(f"Skipped (no changes needed or NA): {skipped_count}")
    print(f"Errors: {error_count}")

def get_property_value(notion_client, props, prop_name, prop_type):
    """Safely extract property value with proper fallback, including rollup, relation, and formula handling"""
    try:
        prop = props.get(prop_name, {})
        prop_type = prop.get('type', prop_type)  # Get actual type from property
        # Handle formula properties
        if prop_type == 'formula':
            formula_type = prop.get('formula', {}).get('type')
            formula_value = prop.get('formula', {})
            
            if formula_type == 'number':
                return formula_value.get('number', 0)
            elif formula_type == 'string':
                return formula_value.get('string', '')
            elif formula_type == 'boolean':
                return formula_value.get('boolean', False)
            elif formula_type == 'date':
                return formula_value.get('date', '')
            return ''

        # Handle relation properties
        if prop_type == 'relation':
            relation_ids = prop.get('relation', [])
            if not relation_ids:
                return ''
            
            # Get the first related page
            related_page_id = relation_ids[0].get('id')
            if not related_page_id:
                return ''
            
            # Fetch the related page content
            try:
                related_page = notion_client.pages.retrieve(page_id=related_page_id)
                related_props = related_page.get('properties', {})
                title_prop = related_props.get('Name', related_props.get('Title', {}))
                if title_prop.get('type') == 'title':
                    return title_prop.get('title', [])[0].get('plain_text', '') if title_prop.get('title') else ''
                return ''
            except Exception as e:
                print(f"Error fetching related page {related_page_id}: {str(e)}")
                return ''

        # Handle rollup properties
        if prop_type == 'rollup':
            rollup_array = prop.get('rollup', {}).get('array', [])
            if not rollup_array:
                return ''
            
            first_value = rollup_array[0]
            if not first_value:
                return ''
                
            inner_type = first_value.get('type', '')
            if inner_type == 'formula':
                formula_type = first_value.get('formula', {}).get('type')
                formula_value = first_value.get('formula', {})
                
                if formula_type == 'number':
                    return formula_value.get('number', 0)
                elif formula_type == 'string':
                    return formula_value.get('string', '')
                elif formula_type == 'boolean':
                    return formula_value.get('boolean', False)
                elif formula_type == 'date':
                    return formula_value.get('date', '')
                return ''
            elif inner_type == 'title':
                return first_value.get('title', [])[0].get('plain_text', '') if first_value.get('title') else ''
            elif inner_type == 'rich_text':
                return first_value.get('rich_text', [])[0].get('plain_text', '') if first_value.get('rich_text') else ''
            elif inner_type == 'number':
                return first_value.get('number', 0)
            elif inner_type == 'select':
                return first_value.get('select', {}).get('name', '') if first_value.get('select') else ''
            elif inner_type == 'checkbox':
                return first_value.get('checkbox', False)
            return ''

        # Handle regular properties
        if prop_type == 'title':
            return prop.get('title', [])[0].get('plain_text', '') if prop.get('title') else ''
        elif prop_type == 'rich_text':
            return prop.get('rich_text', [])[0].get('plain_text', '') if prop.get('rich_text') else ''
        elif prop_type == 'number':
            return prop.get('number', 0)
        elif prop_type == 'select':
            return prop.get('select', {}).get('name', '') if prop.get('select') else ''
        elif prop_type == 'checkbox':
            return prop.get('checkbox', False)
        return ''
    except (IndexError, KeyError, TypeError) as e:
        print(f"Error extracting {prop_name} ({prop_type}): {str(e)}")
        return '' if prop_type in ['title', 'rich_text', 'select', 'rollup', 'relation', 'formula'] else 0 if prop_type == 'number' else False

def get_notion_db_id_for_hardware_eeprom(hardware_name: str) -> str:
    """Get Notion database ID for hardware EEPROM settings from .env"""
    # Convert hardware name to env var format (e.g., "Self Cleaning" -> "SELF_CLEANING_EEPROM_ID")
    env_var = f"NOTION_{hardware_name.replace(' ', '_').upper()}_EEPROM_ID"
    db_id = os.getenv(env_var)
    if not db_id:
        print(f"No Notion database ID found for {hardware_name} EEPROM settings (env var: {env_var})")
    return db_id

def get_notion_db_id_for_hardware_boards(hardware_name: str) -> str:
    """Get Notion database ID for hardware boards from .env"""
    # Convert hardware name to env var format (e.g., "Self Cleaning" -> "SELF_CLEANING_BOARDS_ID")
    env_var = f"NOTION_{hardware_name.replace(' ', '_').upper()}_BOARDS_ID"
    db_id = os.getenv(env_var)
    if not db_id:
        print(f"No Notion database ID found for {hardware_name} boards (env var: {env_var})")
    return db_id

def get_notion_db_id_for_hardware_app_settings(hardware_name: str) -> str:
    """Get Notion database ID for hardware app settings from .env"""
    # Convert hardware name to env var format (e.g., "Self Cleaning" -> "SELF_CLEANING_APP_SETTINGS_ID")
    env_var = f"NOTION_{hardware_name.replace(' ', '_').upper()}_APP_SETTINGS_ID"
    db_id = os.getenv(env_var)
    if not db_id:
        print(f"No Notion database ID found for {hardware_name} app settings (env var: {env_var})")
    return db_id

def get_hardware_list(manager: HardwareManager) -> List[Dict[str, Any]]:
    """Get all hardware including all revisions"""
    return manager.list_hardware(all_revisions=True)

def import_settings_for_all_revisions(manager: HardwareManager) -> None:
    """Import hardware, settings, boards, and app settings from Notion for all hardware revisions"""
    try:
        print("\n" + "="*80)
        print(" HARDWARE IMPORT PROCESS ".center(80, "="))
        print("="*80 + "\n")
        
        # First, import all hardware from Notion
        notion_token = os.getenv("NOTION_TOKEN")
        if not notion_token:
            raise ValueError("NOTION_TOKEN is required in .env file")
        
        notion = Client(auth=notion_token)
        
        # Get the hardware database ID
        hardware_db_id = os.getenv("NOTION_HARDWARE_REVISIONS_ID")
        if not hardware_db_id:
            raise ValueError("NOTION_HARDWARE_REVISIONS_ID is required in .env file")
        
        print("STEP 1: Importing hardware from Notion...")
        hardware_stats = import_hardware_from_notion(hardware_db_id, manager)
        
        print(f"\n📊 Hardware Import Summary:")
        print(f"  +{hardware_stats['created']} created, {hardware_stats['skipped']} skipped, {hardware_stats['errors']} errors")
        
        # Now get all hardware names from environment variables for settings import
        print("\n" + "-"*80)
        print("STEP 2: Importing settings, boards, and app settings for all hardware...")
        print("-"*80 + "\n")
        
        # Get all existing hardware
        all_hardware = manager.list_hardware(all_revisions=True)

        # Create lookup dictionaries
        existing_hardware_by_name = {}
        for hw in all_hardware:
            if hw['name'].upper() not in existing_hardware_by_name:
                existing_hardware_by_name[hw['name'].upper()] = []
            existing_hardware_by_name[hw['name'].upper()].append(hw)
        
        # Get hardware names from environment variables
        hardware_names = set()
        for key in os.environ:
            if key.startswith("NOTION_") and ("_EEPROM_ID" in key or "_BOARDS_ID" in key or "_APP_SETTINGS_ID" in key):
                # Extract hardware name from the environment variable name
                # Format: NOTION_HARDWARE_NAME_TYPE_ID
                parts = key.split("_")
                if len(parts) >= 3:
                    hardware_name = ""
                    for part in parts[1:]:
                        if part.upper() == "EEPROM" or part == "BOARDS" or part == "APP":
                            hardware_name = hardware_name[:-1]
                            break
                        hardware_name += part + " "
                    hardware_names.add(hardware_name)
        
        # Track slot numbers validation issues
        slot_numbers_issues = []
        
        # Process each hardware type
        for hardware_name in sorted(hardware_names):
            print("\n" + "-"*80)
            print(f" PROCESSING: {hardware_name} ".center(80, "-"))
            print("-"*80)
            
            # Get Notion database IDs for this hardware
            eeprom_db_id = get_notion_db_id_for_hardware_eeprom(hardware_name)
            boards_db_id = get_notion_db_id_for_hardware_boards(hardware_name)
            app_settings_db_id = get_notion_db_id_for_hardware_app_settings(hardware_name)
            
            # Check if hardware exists
            if hardware_name not in existing_hardware_by_name:
                print(f"Hardware '{hardware_name}' does not exist in the system. Skipping...")
                continue
            
            # Get revisions for this hardware
            revisions = existing_hardware_by_name.get(hardware_name, [])
            
            if not revisions:
                print(f"No revisions found for hardware '{hardware_name}'")
                continue
            
            # Process each revision
            for hardware in sorted(revisions, key=lambda x: x['rev']):
                print(f"\n📌 {hardware_name} Rev {hardware['rev']} [ID: {hardware['id']}]")
                
                # Map revision to Notion property name (e.g., Rev A)
                rev_property = f"Rev {hardware['rev']}"
                
                # Track overall changes for this revision
                revision_summary = {
                    "eeprom": {"created": 0, "updated": 0, "skipped": 0, "errors": 0},
                    "boards": {"created": 0, "updated": 0, "skipped": 0, "errors": 0},
                    "app_settings": {"created": 0, "updated": 0, "skipped": 0, "errors": 0}
                }
                
                # Import EEPROM settings if database ID exists
                if eeprom_db_id:
                    #print(f"\n  Importing EEPROM settings for {hardware_name} Rev {hardware['rev']}...")
                    eeprom_stats = import_settings_from_notion(
                        notion_database_id=eeprom_db_id, manager=manager, hardware_id=hardware['id'], revision_property=rev_property, quiet=True
                    )
                    revision_summary['eeprom'] = eeprom_stats
                else:
                    print(f"  No EEPROM settings database found for {hardware_name}")
                
                # Import boards if database ID exists
                if boards_db_id:
                    #print(f"\n  Importing boards for {hardware_name} Rev {hardware['rev']}...")
                    boards_stats = import_boards_from_notion(
                        boards_db_id, manager, hardware['id'], rev_property, quiet=True
                    )
                    revision_summary['boards'] = boards_stats
                    
                    # Get boards to check slot numbers
                    boards = manager.list_hardware_boards(hardware['id'])
                    for board in boards:
                        if board.get('slotNumbers'):
                            # Validate slot numbers
                            try:
                                slot_numbers = board['slotNumbers']
                                numbers = slot_numbers.split(',')
                                for num in numbers:
                                    num = num.strip()
                                    if num:
                                        int(num)
                            except ValueError as e:
                                issue = f"{hardware_name} Rev {hardware['rev']} - Board {board['id']} (Protocol {board['protocolId']}, Type {board['typeId']}): Invalid slot number format: {e}"
                                slot_numbers_issues.append(issue)
                                print(f"  ⚠️ {issue}")
                else:
                    print(f"  No boards database found for {hardware_name}")
                
                # Import app settings if database ID exists
                if app_settings_db_id:
                    #print(f"\n  Importing app settings for {hardware_name} Rev {hardware['rev']}...")
                    app_settings_stats = import_app_settings_from_notion(
                        app_settings_db_id, manager, hardware['id'], rev_property, quiet=True
                    )
                    revision_summary['app_settings'] = app_settings_stats
                else:
                    print(f"  No app settings database found for {hardware_name}")
                
                # Print revision summary
                print(f"\n  📊 Summary for {hardware_name} Rev {hardware['rev']}:")
                print(f"    EEPROM Settings: +{revision_summary['eeprom']['created']} created, ~{revision_summary['eeprom']['updated']} updated, {revision_summary['eeprom']['errors']} errors")
                print(f"    Boards: +{revision_summary['boards']['created']} created, ~{revision_summary['boards']['updated']} updated, {revision_summary['boards']['errors']} errors")
                print(f"    App Settings: +{revision_summary['app_settings']['created']} created, ~{revision_summary['app_settings']['updated']} updated, {revision_summary['app_settings']['errors']} errors")

        print("\n" + "="*80)
        print(" IMPORT PROCESS COMPLETE ".center(80, "="))
        print("="*80)
        
        # Print slot numbers validation issues if any
        print("\n")
    except Exception as e:
        print(f"\n❌ Error during import process: {e}")
        print("\nImport process failed. Please check the error message above and try again.")

def string_to_bool(s):
    """Convert string to boolean value"""
    if s.upper() == "TRUE":
        return True
    elif s.upper() == "FALSE":
        return False
    else:
        raise ValueError("Invalid boolean string")

def get_settings_from_notion(notion_client: Client, database_id: str, 
                           hardware_filter: str = None, revision_property: str = "Rev A") -> List[Dict[str, Any]]:
    """Fetch settings from Notion database"""
    filter_params = None
    if hardware_filter:
        filter_params = {
            "and": [
                {
                    "property": "Setting Name",
                    "rollup": {
                        "contains": hardware_filter
                    }
                }
            ]
        }
    
    results = []
    has_more = True
    next_cursor = None

    
    while has_more:
        query_params = {
            "database_id": database_id,
            "start_cursor": next_cursor
        }
        if filter_params:
            query_params["filter"] = filter_params
            
        response = notion_client.databases.query(**query_params)
        
        for page in response.get('results', []):
            try:
                props = page['properties']
                
                # Get the revision value first to check if it's N/A
                default_param = str(get_property_value(notion_client, props, revision_property, 'rich_text'))
                
                # Skip if the value for this revision is N/A
                if is_na(default_param):
                    #print(f"Skipping setting - no value for {revision_property}")
                    continue
                
                # Get the param type and normalize it
                param_type = get_property_value(notion_client, props, 'Type', 'select')
                try:
                    normalized_param_type = normalize_param_type(param_type)
                except ValueError as e:
                    print(f"Skipping setting with invalid parameter type: {param_type} - {str(e)}")
                    continue

                # Get board_id, ensuring 0 is treated as a valid value
                board_id = get_property_value(notion_client, props, 'Board ID', 'number')
                
                # Get is_private as a boolean directly
                is_private = get_property_value(notion_client, props, 'Private?', 'select')
                if is_private.lower() == 'true':
                    is_private = True
                else:
                    is_private = False
                
                # Map Notion properties to our CSV format with safe property extraction
                setting = {
                    'name': get_property_value(notion_client, props, 'Setting Name', 'rollup'),
                    'macro': get_property_value(notion_client, props, 'Setting Macro', 'rich_text'),
                    'description': get_property_value(notion_client, props, 'Setting Description', 'content'),
                    'address': get_property_value(notion_client, props, 'Address', 'formula'),
                    'board_name': get_property_value(notion_client, props, 'Board Name', 'rich_text'),
                    'board_id': board_id,  # This could be 0, which is valid
                    'param_type': normalized_param_type,
                    'default_param': default_param,
                    'min_param': str(get_property_value(notion_client, props, 'Min', 'number')),
                    'max_param': str(get_property_value(notion_client, props, 'Max', 'number')),
                    'is_private': is_private  # Store as a boolean directly
                }

                # Handle min/max for boolean type
                if normalized_param_type == 'BOOLEAN':
                    setting['min_param'] = '0'
                    setting['max_param'] = '1'

                # Only append if we have the minimum required data - special check for board_id to allow 0
                if (setting['name'] and setting['macro'] and setting['address'] and 
                    setting['board_name'] and setting['board_id'] is not None and setting['param_type']):
                    results.append(setting)
                else:
                    missing_fields = []
                    if not setting['name']: missing_fields.append('name')
                    if not setting['macro']: missing_fields.append('macro')
                    if not setting['address']: missing_fields.append('address')
                    if not setting['board_name']: missing_fields.append('board_name')
                    if setting['board_id'] is None: missing_fields.append('board_id')
                    if not setting['param_type']: missing_fields.append('param_type')
                    
                    print(f"Skipping incomplete Notion page: missing required fields: {', '.join(missing_fields)}")
                
            except Exception as e:
                print(f"Error processing Notion page: {str(e)}")
        
        has_more = response.get('has_more', False)
        next_cursor = response.get('next_cursor')
    return results

def import_settings_from_notion(notion_database_id: str, manager: HardwareManager, 
                              hardware_id: int, hardware_filter: str = None,
                              revision_property: str = "Rev A", quiet: bool = False) -> Dict[str, int]:
    """Import settings from Notion database for a specific hardware"""
    try:
        # Initialize Notion client
        notion_token = os.getenv("NOTION_TOKEN")
        if not notion_token:
            raise ValueError("NOTION_TOKEN is required in .env file")
        
        notion = Client(auth=notion_token)

        # Fetch settings from Notion
        settings = get_settings_from_notion(notion_client=notion, database_id=notion_database_id, hardware_filter=hardware_filter, revision_property=revision_property)
        if not settings:
            return {"created": 0, "updated": 0, "skipped": 0, "errors": 0}
        
        # Get existing settings
        existing_settings = manager.list_hardware_settings(hardware_id)

        # Create a more precise dictionary for matching settings
        # Use a tuple of (name, macro, board_name, board_id, address) as the key
        existing_settings_dict = {
            (s['name'], s['macro'], s['boardName'], s['boardId'], s['address']): s 
            for s in existing_settings
        }
        
        created_count = 0
        updated_count = 0
        skipped_count = 0
        error_count = 0
        
        for setting in settings:
            try:
                # Validate required fields - special check for board_id to allow 0
                required_fields = ['name', 'macro', 'description', 'address', 'board_name', 'param_type', 'default_param']
                
                has_missing = False
                for field in required_fields:
                    if not setting.get(field):
                        print(f"Skipping setting '{setting.get('name', 'Unknown')}' - missing '{field}'")
                        has_missing = True
                        break
                
                # Special check for board_id to allow 0
                if setting.get('board_id') is None:  # Only None is invalid, 0 is valid
                    print(f"Skipping setting '{setting.get('name', 'Unknown')}' - missing 'board_id'")
                    has_missing = True
                
                if has_missing:
                    skipped_count += 1
                    continue
                
                # Validate param type
                if setting['param_type'] not in [t.value for t in SettingParamType]:
                    print(f"Invalid parameter type '{setting['param_type']}' for {setting['name']}, skipping...")
                    skipped_count += 1
                    continue

                # Create a composite key for more precise matching
                setting_key = (
                    setting['name'], 
                    setting['macro'], 
                    setting['board_name'], 
                    int(setting['board_id']), 
                    int(setting['address'])
                )
                
                # Check if setting exists with this exact combination
                existing_setting = existing_settings_dict.get(setting_key)
                
                # If not found by composite key, try a more relaxed match by name and address
                if not existing_setting:
                    # Look for settings with the same name and address
                    potential_matches = [
                        s for s in existing_settings 
                        if s['name'] == setting['name'] and s['address'] == int(setting['address'])
                    ]
                    
                    if potential_matches:
                        print(f"Found {len(potential_matches)} potential matches for {setting['name']} (Address {setting['address']})")
                        print(f"Using exact matching with macro, board name, and board ID to avoid conflicts")
                
                if existing_setting:                    
                    needs_update, changes = settings_are_different(existing_setting, setting)
                    
                    if needs_update:
                        # print(f"Updating setting: {setting['name']} (Address {setting['address']}, Board {setting['board_name']}, ID {setting['board_id']})")
                        for change in changes:
                            print(f"  • {change}")
                        
                        manager.update_hardware_setting(
                            setting_id=existing_setting['id'],
                            hardware_id=hardware_id,
                            **setting
                        )
                        updated_count += 1
                    else:
                        #print(f"Skipping setting (no changes needed): {setting['name']} (Address {setting['address']}, Board {setting['board_name']}, ID {setting['board_id']})")
                        skipped_count += 1
                else:
                    # print(f"Creating new setting: {setting['name']} (Address {setting['address']}, Board {setting['board_name']}, ID {setting['board_id']})")
                    manager.create_hardware_setting(
                        hardware_id=hardware_id,
                        **setting
                    )
                    created_count += 1
                
            except Exception as e:
                print(f"Error processing setting {setting.get('name', 'Unknown')}: {e}")
                error_count += 1
        
        return {
            "created": created_count,
            "updated": updated_count,
            "skipped": skipped_count,
            "errors": error_count
        }
        
    except Exception as e:
        if not quiet:
            print(f"Error importing from Notion: {e}")
        return {
            "created": 0,
            "updated": 0,
            "skipped": 0,
            "errors": 1
        }

def export_hardware_to_csv(output_file: str, manager: HardwareManager) -> None:
    """Export all hardware revisions to a CSV file"""
    # Get all hardware including all revisions
    hardware_list = manager.list_hardware(all_revisions=True)
    
    if not hardware_list:
        print("No hardware found to export")
        return
        
    # Define CSV headers based on hardware fields
    fieldnames = ['ID', 'Name', 'Type', 'Rev', 'Description']
    
    try:
        with open(output_file, 'w', newline='') as f:
            writer = csv.DictWriter(f, fieldnames=fieldnames)
            writer.writeheader()
            
            for hw in hardware_list:
                writer.writerow({
                    'ID': hw['id'],
                    'Name': hw['name'],
                    'Type': hw['type'],
                    'Rev': hw['rev'],
                    'Description': hw.get('description', '')
                })
        
        print(f"\nExport Summary:")
        print(f"Successfully exported {len(hardware_list)} hardware revisions to {output_file}")
        
    except Exception as e:
        print(f"Error exporting to CSV: {e}")

def validate_slot_numbers(slot_numbers: str) -> Tuple[bool, str]:
    """
    Validate that slot numbers are in the correct format.
    Returns (is_valid, error_message)
    """
    if not slot_numbers:
        return True, ""
    
    try:
        # Check if it's a comma-separated list of integers
        numbers = slot_numbers.split(',')
        for num in numbers:
            num = num.strip()
            if num:  # Skip empty entries
                int(num)  # This will raise ValueError if not an integer
        return True, ""
    except ValueError:
        return False, f"Slot numbers must be comma-separated integers: '{slot_numbers}'"
    except Exception as e:
        return False, f"Invalid slot numbers format: {str(e)}"

def get_boards_from_notion(notion_client: Client, database_id: str, 
                          revision_property: str = "Rev A") -> List[Dict[str, Any]]:
    """Fetch board definitions from Notion database"""
    results = []
    has_more = True
    next_cursor = None
    
    while has_more:
        query_params = {
            "database_id": database_id,
            "start_cursor": next_cursor
        }
            
        response = notion_client.databases.query(**query_params)
        
        for page in response.get('results', []):
            try:
                props = page['properties']
                
                # Check if this board should be included for this revision
                include_for_revision = get_property_value(notion_client, props, revision_property, 'select')
                if not include_for_revision or include_for_revision.lower() != 'true':
                    #print(f"Skipping board - not included for {revision_property}")
                    continue
                
                # Get required board properties
                board_name = get_property_value(notion_client, props, 'Board Name', 'title')
                protocol_id = get_property_value(notion_client, props, 'Protocol ID', 'number')
                type_id = get_property_value(notion_client, props, 'Type ID', 'number')
                pump_rating = get_property_value(notion_client, props, 'Pump Rating', 'number') or 0
                
                # Get slot numbers from either "Slot Numbers" or "Ingredients" column
                slot_numbers = get_property_value(notion_client, props, 'Slot Numbers', 'rich_text')
                
                # If slot_numbers is empty, try to get from "Ingredients" column
                if not slot_numbers:
                    slot_numbers = get_property_value(notion_client, props, 'Ingredients', 'rich_text')
                
                # Skip if slot_numbers is "N/A"
                if slot_numbers and is_na(slot_numbers):
                    slot_numbers = None
                
                # Validate slot numbers if present
                if slot_numbers:
                    is_valid, error_msg = validate_slot_numbers(slot_numbers)
                    if not is_valid:
                        print(f"Board '{board_name}': {error_msg}")
                        slot_numbers = None
                
                # Use default values for PCB version and Application ID
                pcb_major = 255
                pcb_minor = 255
                pcb_patch = 255
                
                if "pump" in board_name.lower():
                    application_id = 5
                elif "main" in board_name.lower():
                    application_id = 1
                elif "solenoid" in board_name.lower():
                    application_id = 2
                elif "cooling" in board_name.lower():
                    application_id = 9
                elif "nozzle" in board_name.lower():
                    application_id = 6
                elif "qr" in board_name.lower():
                    application_id = 306
                else:
                    application_id = 1
                
                
                # Map Notion properties to our board format
                board = {
                    'board_name': board_name,
                    'protocol_id': int(protocol_id) if protocol_id else None,
                    'type_id': int(type_id) if type_id else None,
                    'pump_rating': int(pump_rating),
                    'pcb_major': pcb_major,
                    'pcb_minor': pcb_minor,
                    'pcb_patch': pcb_patch,
                    'application_id': application_id,
                    'slot_numbers': slot_numbers
                }
                
                # Validate required fields
                required_fields = ['board_name', 'protocol_id', 'type_id']
                missing_fields = [field for field in required_fields if board.get(field) is None]
                
                if missing_fields:
                    print(f"Skipping board '{board.get('board_name', 'Unknown')}' - missing: {', '.join(missing_fields)}")
                    continue
                
                results.append(board)
                
            except Exception as e:
                print(f"Error processing Notion board page: {str(e)}")
        
        has_more = response.get('has_more', False)
        next_cursor = response.get('next_cursor')
    
    return results

def import_boards_from_notion(notion_database_id: str, manager: HardwareManager, 
                            hardware_id: int, revision_property: str = "Rev A",
                            quiet: bool = False) -> Dict[str, int]:
    """Import boards from Notion database for a specific hardware"""
    try:
        # Initialize Notion client
        notion_token = os.getenv("NOTION_TOKEN")
        if not notion_token:
            raise ValueError("NOTION_TOKEN is required in .env file")
        
        notion = Client(auth=notion_token)
        
        boards = get_boards_from_notion(notion, notion_database_id, revision_property)
        
        if not boards:
            return {"created": 0, "updated": 0, "skipped": 0, "errors": 0}
                
        # Get existing boards
        existing_boards = manager.list_hardware_boards(hardware_id)
        
        # Create a dictionary for matching boards
        # Use a tuple of (protocol_id, type_id) as the key
        existing_boards_dict = {
            (b['protocolId'], b['typeId']): b 
            for b in existing_boards
        }
        
        created_count = 0
        updated_count = 0
        skipped_count = 0
        error_count = 0
        
        for board in boards:
            try:
                # Create a key for matching
                board_key = (board['protocol_id'], board['type_id'])

                if "pump" in board['board_name'].lower():
                    board['application_id'] = 5
                elif "main" in board['board_name'].lower():
                    board['application_id'] = 1
                elif "solenoid" in board['board_name'].lower():
                    board['application_id'] = 2
                elif "cooling" in board['board_name'].lower():
                    board['application_id'] = 9
                elif "nozzle" in board['board_name'].lower():
                    board['application_id'] = 6
                elif "qr" in board['board_name'].lower():
                    board['application_id'] = 306
                else:
                    board['application_id'] = 1
                
                # Check if board exists
                existing_board = existing_boards_dict.get(board_key)

                # Log slot numbers information
                if not quiet:
                    if board.get('slot_numbers'):
                        print(f"Board '{board['board_name']}': Using slot numbers: {board['slot_numbers']}")
                    else:
                        print(f"Board '{board['board_name']}': No slot numbers specified")
                
                if existing_board:
                    # Check if board needs update
                    needs_update = (
                        existing_board['pcbMajor'] != board['pcb_major'] or
                        existing_board['pcbMinor'] != board['pcb_minor'] or
                        existing_board['pcbPatch'] != board['pcb_patch'] or
                        existing_board['pumpRating'] != board['pump_rating'] or
                        existing_board['applicationId'] != board['application_id'] or
                        existing_board['slotNumbers'] != board['slot_numbers']
                    )
                    if needs_update:
                        if not quiet:
                            print(f"Updating board: Protocol {board['protocol_id']}, Type {board['type_id']}")
                            if existing_board['slotNumbers'] != board['slot_numbers']:
                                print(f"  - Updating slot numbers: '{existing_board['slotNumbers']}' -> '{board['slot_numbers']}'")
                        print(f"Existing = {existing_board['slotNumbers']} New = {board['slot_numbers']}")
                        manager.update_hardware_board(
                            board_id=existing_board['id'],
                            hardware_id=hardware_id,
                            protocol_id=board['protocol_id'],
                            pcb_major=board['pcb_major'],
                            pcb_minor=board['pcb_minor'],
                            pcb_patch=board['pcb_patch'],
                            pump_rating=board['pump_rating'],
                            application_id=board['application_id'],
                            type_id=board['type_id'],
                            slot_numbers=board['slot_numbers']
                        )
                        updated_count += 1
                    else:
                        skipped_count += 1
                else:
                    if not quiet:
                        print(f"Creating new board: Protocol {board['protocol_id']}, Type {board['type_id']}")
                    manager.create_hardware_board(
                        hardware_id=hardware_id,
                        protocol_id=board['protocol_id'],
                        pcb_major=board['pcb_major'],
                        pcb_minor=board['pcb_minor'],
                        pcb_patch=board['pcb_patch'],
                        pump_rating=board['pump_rating'],
                        application_id=board['application_id'],
                        type_id=board['type_id'],
                        slot_numbers=board['slot_numbers']
                    )
                    created_count += 1
                
            except Exception as e:
                if not quiet:
                    print(f"Error processing board: {e}")
                error_count += 1
        
        return {
            "created": created_count,
            "updated": updated_count,
            "skipped": skipped_count,
            "errors": error_count
        }
        
    except Exception as e:
        if not quiet:
            print(f"Error importing boards from Notion: {e}")
        return {
            "created": 0,
            "updated": 0,
            "skipped": 0,
            "errors": 1
        }

def get_app_settings_from_notion(notion_client: Client, database_id: str, 
                               revision_property: str = "Rev A") -> List[Dict[str, Any]]:
    """Fetch app settings from Notion database"""
    results = []
    has_more = True
    next_cursor = None
    
    while has_more:
        query_params = {
            "database_id": database_id,
            "start_cursor": next_cursor
        }
            
        response = notion_client.databases.query(**query_params)
        
        for page in response.get('results', []):
            try:
                props = page['properties']
                
                # Get required app setting properties
                name = get_property_value(notion_client, props, 'Name', 'title')
                description = get_property_value(notion_client, props, 'Description', 'rich_text') or ""
                macro = get_property_value(notion_client, props, 'Macro', 'rich_text')
                param_type = get_property_value(notion_client, props, 'Param Type', 'select')
                security_level = get_property_value(notion_client, props, 'Security Level', 'number') or 1
                category = get_property_value(notion_client, props, 'Category', 'rich_text') or ""
                max_value = get_property_value(notion_client, props, 'Max', 'rich_text') or ""
                min_value = get_property_value(notion_client, props, 'Min', 'rich_text') or ""
                default_value = get_property_value(notion_client, props, revision_property, 'rich_text') or ""
                
                # Skip if the value for this revision is N/A
                if is_na(default_value):
                    print(f"Skipping app setting '{name}' - value is N/A for {revision_property}")
                    continue
                
                # Normalize param type to match ParamType enum
                if param_type:
                    param_type = param_type.upper()
                    # Map to ParamType enum values if needed
                    param_type_mapping = {
                        "BOOLEAN": "BOOLEAN",
                        "BYTE": "BYTE",
                        "WORD": "WORD",
                        "DWORD": "DWORD",
                        "SINGLE": "SINGLE",
                        "STRING": "STRING"
                    }
                    param_type = param_type_mapping.get(param_type, param_type)
                
                # Map Notion properties to our app setting format
                app_setting = {
                    'name': name,
                    'description': description,
                    'macro': macro,
                    'param_type': param_type,
                    'security_level': int(security_level),
                    'category': category,
                    'max_value': max_value,
                    'min_value': min_value,
                    'default_value': default_value
                }
                
                # Validate required fields
                required_fields = ['name', 'macro', 'param_type', 'default_value']
                missing_fields = [field for field in required_fields if not app_setting.get(field)]
                
                if missing_fields:
                    print(f"Skipping app setting '{app_setting.get('name', 'Unknown')}' - missing: {', '.join(missing_fields)}")
                    continue
                
                results.append(app_setting)
                
            except Exception as e:
                print(f"Error processing Notion app setting page: {str(e)}")
        
        has_more = response.get('has_more', False)
        next_cursor = response.get('next_cursor')
    
    return results

def import_app_settings_from_notion(notion_database_id: str, manager: HardwareManager, 
                                  hardware_id: int, revision_property: str = "Rev A",
                                  quiet: bool = False) -> Dict[str, int]:
    """Import app settings from Notion database for a specific hardware"""
    try:
        # Initialize Notion client
        notion_token = os.getenv("NOTION_TOKEN")
        if not notion_token:
            raise ValueError("NOTION_TOKEN is required in .env file")
        
        notion = Client(auth=notion_token)
        
        app_settings = get_app_settings_from_notion(notion, notion_database_id, revision_property)
        
        if not app_settings:
            return {"created": 0, "updated": 0, "skipped": 0, "errors": 0}
                
        # Get existing profile values
        existing_profile_values = manager.list_hardware_profile_values(hardware_id)
        
        # Create a dictionary for matching profile values
        # Use a tuple of (name, macro) as the key
        existing_profile_values_dict = {
            (pv['name'], pv['macro']): pv 
            for pv in existing_profile_values
        }
        
        created_count = 0
        updated_count = 0
        skipped_count = 0
        error_count = 0
        
        for app_setting in app_settings:
            try:
                # Create a key for matching
                profile_key = (app_setting['name'], app_setting['macro'])
                
                # Check if profile value exists
                existing_profile_value = existing_profile_values_dict.get(profile_key)
                
                if existing_profile_value:
                    # Check if profile value needs update
                    needs_update = (
                        existing_profile_value['paramType'] != app_setting['param_type'] or
                        existing_profile_value['defaultValue'] != app_setting['default_value'] or
                        existing_profile_value['minValue'] != app_setting['min_value'] or
                        existing_profile_value['maxValue'] != app_setting['max_value'] or
                        existing_profile_value['category'] != app_setting['category'] or
                        existing_profile_value['securityLevel'] != app_setting['security_level'] or
                        existing_profile_value.get('description', '') != app_setting.get('description', '')
                    )
                    
                    if needs_update:
                        if not quiet:
                            print(f"Updating app setting: {app_setting['name']}")
                        manager.update_hardware_profile_value(
                            profile_value_id=existing_profile_value['id'],
                            hardware_id=hardware_id,
                            name=app_setting['name'],
                            macro=app_setting['macro'],
                            description=app_setting.get('description', ''),
                            param_type=app_setting['param_type'],
                            default_value=app_setting['default_value'],
                            min_value=app_setting['min_value'],
                            max_value=app_setting['max_value'],
                            category=app_setting['category'],
                            security_level=app_setting['security_level']
                        )
                        updated_count += 1
                    else:
                        skipped_count += 1
                else:
                    if not quiet:
                        print(f"Creating new app setting: {app_setting['name']}")
                    manager.create_hardware_profile_value(
                        hardware_id=hardware_id,
                        name=app_setting['name'],
                        macro=app_setting['macro'],
                        description=app_setting.get('description', ''),
                        param_type=app_setting['param_type'],
                        default_value=app_setting['default_value'],
                        min_value=app_setting['min_value'],
                        max_value=app_setting['max_value'],
                        category=app_setting['category'],
                        security_level=app_setting['security_level']
                    )
                    created_count += 1
                
            except Exception as e:
                if not quiet:
                    print(f"Error processing app setting {app_setting.get('name', 'Unknown')}: {e}")
                error_count += 1
        
        return {
            "created": created_count,
            "updated": updated_count,
            "skipped": skipped_count,
            "errors": error_count
        }
        
    except Exception as e:
        if not quiet:
            print(f"Error importing app settings from Notion: {e}")
        return {
            "created": 0,
            "updated": 0,
            "skipped": 0,
            "errors": 1
        }

def get_hardware_from_notion(notion_client: Client, database_id: str) -> List[Dict[str, Any]]:
    """Fetch hardware definitions from Notion database"""
    results = []
    has_more = True
    next_cursor = None
    
    while has_more:
        query_params = {
            "database_id": database_id,
            "start_cursor": next_cursor
        }
            
        response = notion_client.databases.query(**query_params)
        
        for page in response.get('results', []):
            try:
                props = page['properties']
                
                # Get hardware properties
                name = get_property_value(notion_client, props, 'Name', 'title')
                hardware_type = get_property_value(notion_client, props, 'Type', 'select')
                rev = get_property_value(notion_client, props, 'Rev', 'select')
                description = get_property_value(notion_client, props, 'Description', 'rich_text') or ""
                
                # Validate required fields
                if not name or not hardware_type or not rev:
                    missing_fields = []
                    if not name: missing_fields.append('Name')
                    if not hardware_type: missing_fields.append('Type')
                    if not rev: missing_fields.append('Rev')
                    
                    # print(f"Skipping hardware - missing required fields: {', '.join(missing_fields)}")
                    continue
                
                # Validate that rev is a single letter (A-Z)
                if not (len(rev) == 1 and rev.isalpha()):
                    # print(f"Skipping hardware '{name}' - revision '{rev}' is not a valid letter (A-Z)")
                    continue
                
                # Normalize hardware type to match HardwareType enum
                hardware_type = hardware_type.lower()
                if hardware_type not in [t.value for t in HardwareType]:
                    print(f"Invalid hardware type '{hardware_type}' for {name}, skipping...")
                    continue
                
                # Map Notion properties to our hardware format
                hardware = {
                    'name': name,
                    'type': hardware_type,
                    'rev': rev,
                    'description': description
                }
                
                results.append(hardware)
                
            except Exception as e:
                print(f"Error processing Notion hardware page: {str(e)}")
        
        has_more = response.get('has_more', False)
        next_cursor = response.get('next_cursor')
    
    return results

def import_hardware_from_notion(notion_database_id: str, manager: HardwareManager, quiet: bool = False) -> Dict[str, int]:
    """Import hardware definitions from Notion database, skipping existing ones"""
    try:
        # Initialize Notion client
        notion_token = os.getenv("NOTION_TOKEN")
        if not notion_token:
            raise ValueError("NOTION_TOKEN is required in .env file")
        
        notion = Client(auth=notion_token)
        
        # First, get all existing hardware
        existing_hardware = manager.list_hardware(all_revisions=True)
        
        # Create a set of existing hardware identifiers (name + rev combination)
        existing_hardware_keys = {(h['name'], h['rev']) for h in existing_hardware}
        
        hardware_list = get_hardware_from_notion(notion, notion_database_id)

        if not hardware_list:
            print("No hardware found in Notion database")
            return {"created": 0, "skipped": 0, "errors": 0}
                
        skipped_count = 0
        created_count = 0
        error_count = 0
        
        for hardware in hardware_list:
            try:
                # Check if hardware already exists
                if (hardware['name'], hardware['rev']) in existing_hardware_keys:
                    skipped_count += 1
                    continue
                
                # Create new hardware
                print(f"Creating new hardware: {hardware['name']} (Rev {hardware['rev']})")
                manager.create_hardware(
                    hardware['name'],
                    hardware['type'],
                    hardware['rev'],
                    hardware['description']
                )
                created_count += 1
                
            except Exception as e:
                print(f"Error processing hardware {hardware['name']}: {e}")
                error_count += 1
        
        return {
            "created": created_count,
            "skipped": skipped_count,
            "errors": error_count
        }
        
    except Exception as e:
        print(f"Error importing hardware from Notion: {e}")
        return {
            "created": 0,
            "skipped": 0,
            "errors": 1
        }

def import_specific_database(manager: HardwareManager, hardware_name: str, db_type: str) -> Dict[str, int]:
    """Import a specific database for a hardware type
    
    Args:
        manager: HardwareManager instance
        hardware_name: Name of the hardware (e.g., "Cooling", "Self Cleaning")
        db_type: Type of database ("eeprom", "boards", or "app_settings")
        
    Returns:
        Dict with import statistics
    """
    try:
        print(f"\nImporting {db_type} for {hardware_name}...")
        
        # Get Notion token
        notion_token = os.getenv("NOTION_TOKEN")
        if not notion_token:
            raise ValueError("NOTION_TOKEN is required in .env file")
        
        # Get the database ID based on hardware name and db type
        db_id = None
        if db_type.lower() == "eeprom":
            db_id = get_notion_db_id_for_hardware_eeprom(hardware_name)
        elif db_type.lower() == "boards":
            db_id = get_notion_db_id_for_hardware_boards(hardware_name)
        elif db_type.lower() == "app_settings":
            db_id = get_notion_db_id_for_hardware_app_settings(hardware_name)
        else:
            raise ValueError(f"Invalid database type: {db_type}. Must be 'eeprom', 'boards', or 'app_settings'")
        
        if not db_id:
            raise ValueError(f"No database ID found for {hardware_name} {db_type}")
        
        # Get all hardware to find the correct hardware ID
        all_hardware = manager.list_hardware(all_revisions=True)
        hardware_list = [h for h in all_hardware if h['name'].lower() == hardware_name.lower()]
        
        if not hardware_list:
            raise ValueError(f"Hardware '{hardware_name}' not found in the system")
        
        stats = {"created": 0, "updated": 0, "skipped": 0, "errors": 0}
        
        # Process each revision
        for hardware in sorted(hardware_list, key=lambda x: x['rev']):
            print(f"\n📌 {hardware_name} Rev {hardware['rev']} [ID: {hardware['id']}]")
            
            # Map revision to Notion property name (e.g., Rev A)
            rev_property = f"Rev {hardware['rev']}"
            
            # Import the specific database
            if db_type.lower() == "eeprom":
                print(f"  ⚙️  Importing EEPROM settings...")
                result = import_settings_from_notion(
                    db_id=db_id, manager=manager, hardware_id=hardware['id'], revision_property=rev_property, quiet=False
                )
            elif db_type.lower() == "boards":
                print(f"  🔌 Importing boards...")
                result = import_boards_from_notion(
                    db_id, manager, hardware['id'], revision_property=rev_property, quiet=False
                )
            elif db_type.lower() == "app_settings":
                print(f"  📱 Importing app settings...")
                result = import_app_settings_from_notion(
                    db_id, manager, hardware['id'], revision_property=rev_property, quiet=False
                )
            
            # Aggregate stats
            for key in stats:
                stats[key] += result[key]
        
        print(f"\n📊 Import Summary for {hardware_name} {db_type}:")
        print(f"  +{stats['created']} created, {stats['updated']} updated, {stats['skipped']} skipped, {stats['errors']} errors")
        
        return stats
        
    except Exception as e:
        print(f"\n❌ Error importing {db_type} for {hardware_name}: {e}")
        return {"created": 0, "updated": 0, "skipped": 0, "errors": 1}

def get_machine_hardware_assignments_from_notion(notion_client: Client, database_id: str) -> List[Dict[str, Any]]:
    """Fetch machine hardware assignments from Notion database
    
    Args:
        notion_client: Initialized Notion client
        database_id: Notion database ID for machine hardware assignments
        
    Returns:
        List of machine hardware assignments
    """
    results = []
    has_more = True
    next_cursor = None
    
    print(f"Fetching machine hardware assignments from Notion...")
    
    while has_more:
        query_params = {
            "database_id": database_id,
            "start_cursor": next_cursor
        }
            
        response = notion_client.databases.query(**query_params)
        
        for page in response.get('results', []):
            try:
                props = page['properties']
                
                # Check if machine is active
                active_machine = get_property_value(notion_client, props, 'Machine Status', 'title')
                if active_machine != "In Work" and active_machine != "Shipped" and active_machine != "Ready Inventory":
                    # print(f"Skipping row - machine SN {get_property_value(notion_client, props, 'Serial Number', 'title')} is {active_machine}")
                    continue

                # Get machine identifier
                serial_number = get_property_value(notion_client, props, 'Serial Number', 'title')
                
                if not serial_number:
                    # print(f"Skipping row - missing Serial Number")
                    continue
                
                # Get hardware revisions
                lower_unit_type = get_property_value(notion_client, props, 'Lower Unit Type', 'select')
                if lower_unit_type == "Large":
                    lower_unit_type = "B"
                if lower_unit_type == "Medium":
                    lower_unit_type = "A"
                lower_unit_rev = get_property_value(notion_client, props, 'Lower Unit Revision', 'select')
                if "Gen 2" not in lower_unit_rev:
                    # print(f"Skipping row {serial_number} - Gen 1 machine")
                    continue
                upper_unit_type = get_property_value(notion_client, props, 'Upper Unit Type', 'select')
                if upper_unit_type == "Lite":
                    upper_unit_type = "A"
                self_cleaning_rev = get_property_value(notion_client, props, 'Self Cleaning Rev', 'select')
                cooling_system_rev = get_property_value(notion_client, props, 'Cooling System Rev', 'select')
                nozzle_rev = get_property_value(notion_client, props, 'Nozzle Rev', 'select')
                sauce_tray_rev = get_property_value(notion_client, props, 'Sauce Tray Rev', 'select')
                if sauce_tray_rev == "None":
                    sauce_tray_rev = None

                # Create machine entry
                machine = {
                    'serial_number': serial_number,
                    'hardware_assignments': []
                }
                
                # Add hardware assignments if they exist
                if lower_unit_type:
                    machine['hardware_assignments'].append({
                        'hardware_name': 'Lower Model',
                        'hardware_rev': lower_unit_type
                    })
                
                if upper_unit_type:
                    machine['hardware_assignments'].append({
                        'hardware_name': 'Upper Model',
                        'hardware_rev': upper_unit_type
                    })
                
                if cooling_system_rev:
                    machine['hardware_assignments'].append({
                        'hardware_name': 'Cooling',
                        'hardware_rev': cooling_system_rev[0]
                    })
                
                if nozzle_rev:
                    machine['hardware_assignments'].append({
                        'hardware_name': 'Nozzle',
                        'hardware_rev': nozzle_rev[0]
                    })
                
                if sauce_tray_rev:
                    machine['hardware_assignments'].append({
                        'hardware_name': 'Sauce Tray',
                        'hardware_rev': sauce_tray_rev[0]
                    })
                
                if self_cleaning_rev:
                    machine['hardware_assignments'].append({
                        'hardware_name': 'Self Cleaning',
                        'hardware_rev': self_cleaning_rev[0]
                    })
               
                
                # Only add machine if it has at least one hardware assignment
                if machine['hardware_assignments']:
                    results.append(machine)
                else:
                    print(f"Skipping machine {serial_number} - no hardware assignments")
                
            except Exception as e:
                print(f"Error processing Notion machine page: {str(e)}")
        
        has_more = response.get('has_more', False)
        next_cursor = response.get('next_cursor')
    
    print(f"Found {len(results)} machines with hardware assignments")
    return results

def apply_hardware_assignments_from_notion(notion_database_id: str, manager: HardwareManager, 
                                         add_hardware: bool = True, apply_hardware: bool = True) -> Dict[str, int]:
    """Apply hardware assignments from Notion database to machines
    
    Args:
        notion_database_id: Notion database ID for machine hardware assignments
        manager: HardwareManager instance
        add_hardware: Whether to add hardware to machines
        apply_hardware: Whether to apply hardware settings
        
    Returns:
        Dict with statistics
    """
    try:
        # Initialize Notion client
        notion_token = os.getenv("NOTION_TOKEN")
        if not notion_token:
            raise ValueError("NOTION_TOKEN is required in .env file")
        
        notion = Client(auth=notion_token)
        print('here0')
        # Get all machine hardware assignments from Notion
        machines = get_machine_hardware_assignments_from_notion(notion, notion_database_id)
        print('here')
        if not machines:
            print("No machines with hardware assignments found in Notion database")
            return {"machines": 0, "added": 0, "applied": 0, "removed": 0, "skipped": 0, "errors": 0}
        print('here1')
        # Get all hardware to find the correct hardware IDs
        all_hardware = manager.list_hardware(all_revisions=True)
        
        # Create lookup dictionaries for hardware
        hardware_lookup = {}  # (name, rev) -> id
        hardware_name_lookup = {}  # id -> name
        hardware_rev_lookup = {}   # id -> rev
        
        for hw in all_hardware:
            key = (hw['name'].lower(), hw['rev'].lower())
            hardware_lookup[key] = hw['id']
            hardware_name_lookup[hw['id']] = hw['name']
            hardware_rev_lookup[hw['id']] = hw['rev']
        print('here2')
        # Get list of machines with their serial numbers
        serial_to_admin = manager.list_machines()
        
        machine_count = 0
        added_count = 0
        applied_count = 0
        skipped_count = 0
        error_count = 0
        removed_count = 0
        
        # Determine what operations we're performing
        operations = []
        if add_hardware:
            operations.append("adding")
        if apply_hardware:
            operations.append("applying")
        
        print(f"\n{', '.join(operations).capitalize()} hardware assignments to machines...")
        
        for machine in machines:
            try:
                serial_number = machine['serial_number']
                
                # Check if the serial number exists in our system
                if serial_number[-5:] not in serial_to_admin:
                    print(f"\n⚠️ Machine with serial number {serial_number} not found in system. Skipping...")
                    skipped_count += len(machine['hardware_assignments'])
                    continue
                
                machine_id = serial_to_admin[serial_number[-5:]]
                print(f"\n📌 Machine: SN: {serial_number}, Admin ID: {machine_id}")
                
                # Get current hardware on the machine
                machine_hardware = manager.list_hardware(machine_id=machine_id)
                
                machine_added = 0
                machine_applied = 0
                machine_skipped = 0
                machine_errors = 0
                machine_removed = 0
                
                for assignment in machine['hardware_assignments']:
                    try:
                        hardware_name = assignment['hardware_name']
                        hardware_rev = assignment['hardware_rev']
                        
                        # Look up hardware ID
                        lookup_key = (hardware_name.lower(), hardware_rev.lower())
                        if lookup_key not in hardware_lookup:
                            print(f"  ⚠️ Hardware not found: {hardware_name} Rev {hardware_rev}. Skipping...")
                            machine_skipped += 1
                            skipped_count += 1
                            continue
                        
                        hardware_id = hardware_lookup[lookup_key]
                        
                        # Check if this hardware is already added to the machine
                        hardware_already_added = any(hw['id'] == hardware_id for hw in machine_hardware)
                        
                        if add_hardware:
                            # Check if a different revision of this hardware is already on the machine
                            different_revs = [
                                hw for hw in machine_hardware 
                                if hw['name'].lower() == hardware_name.lower() and hw['id'] != hardware_id
                            ]
                            
                            # Remove any different revisions of this hardware
                            for old_hw in different_revs:
                                try:
                                    print(f"  🗑️ Removing {old_hw['name']} Rev {old_hw['rev']} [ID: {old_hw['id']}] from machine")
                                    manager.remove_hardware_from_machine(machine_id, old_hw['id'])
                                    machine_removed += 1
                                    removed_count += 1
                                except Exception as e:
                                    print(f"  ❌ Error removing old hardware: {e}")
                                    machine_errors += 1
                                    error_count += 1
                            
                            # Add the new hardware if it's not already added
                            if not hardware_already_added:
                                try:
                                    print(f"  ➕ Adding {hardware_name} Rev {hardware_rev} [ID: {hardware_id}] to machine")
                                    manager.add_hardware_to_machine(machine_id, hardware_id)
                                    machine_added += 1
                                    added_count += 1
                                except Exception as e:
                                    print(f"  ❌ Error adding hardware to machine: {e}")
                                    machine_errors += 1
                                    error_count += 1
                                    continue
                            elif add_hardware and not apply_hardware:
                                # If we're only adding hardware and not applying it, count already added hardware
                                print(f"  ✓ {hardware_name} Rev {hardware_rev} [ID: {hardware_id}] already added to machine")
                        
                        # Apply hardware to machine if requested
                        if apply_hardware:
                            if not hardware_already_added and not add_hardware:
                                print(f"  ⚠️ Cannot apply {hardware_name} Rev {hardware_rev} [ID: {hardware_id}] - not added to machine")
                                machine_skipped += 1
                                skipped_count += 1
                                continue
                                
                            try:
                                print(f"  ✅ Applying {hardware_name} Rev {hardware_rev} [ID: {hardware_id}]")
                                manager.apply_settings(hardware_id, machine_id)
                                manager.apply_boards(hardware_id, machine_id)
                                manager.apply_profile_values(hardware_id, machine_id)
                                machine_applied += 1
                                applied_count += 1
                            except Exception as e:
                                print(f"  ❌ Error applying hardware: {e}")
                                machine_errors += 1
                                error_count += 1
                        
                    except Exception as e:
                        print(f"  ❌ Error processing {hardware_name} Rev {hardware_rev}: {e}")
                        machine_errors += 1
                        error_count += 1
                
                # Build summary message based on operations performed
                summary_parts = []
                if add_hardware:
                    summary_parts.append(f"{machine_added} added")
                    summary_parts.append(f"{machine_removed} removed")
                if apply_hardware:
                    summary_parts.append(f"{machine_applied} applied")
                summary_parts.append(f"{machine_skipped} skipped")
                summary_parts.append(f"{machine_errors} errors")
                
                print(f"  📊 Machine Summary: {', '.join(summary_parts)}")
                
                # Count machine as updated if any operation was successful
                if (add_hardware and (machine_added > 0 or machine_removed > 0)) or \
                   (apply_hardware and machine_applied > 0):
                    machine_count += 1
                
            except Exception as e:
                print(f"Error processing machine {machine.get('serial_number', 'Unknown')}: {e}")
                error_count += 1
        
        # Build overall summary based on operations performed
        print(f"\n📊 Overall Hardware Assignment Summary:")
        print(f"  {machine_count} machines updated")
        if add_hardware:
            print(f"  {added_count} hardware assignments added")
            print(f"  {removed_count} old hardware revisions removed")
        if apply_hardware:
            print(f"  {applied_count} hardware assignments applied")
        print(f"  {skipped_count} hardware assignments skipped")
        print(f"  {error_count} errors")
        
        result = {
            "machines": machine_count,
            "skipped": skipped_count,
            "errors": error_count
        }
        
        if add_hardware:
            result["added"] = added_count
            result["removed"] = removed_count
        
        if apply_hardware:
            result["applied"] = applied_count
            
        return result
        
    except Exception as e:
        print(f"Error applying hardware assignments from Notion: {e}")
        result = {
            "machines": 0,
            "skipped": 0,
            "errors": 1
        }
        
        if add_hardware:
            result["added"] = 0
            result["removed"] = 0
        
        if apply_hardware:
            result["applied"] = 0
            
        return result

def add_and_apply_hardware_to_machine(manager: HardwareManager, serial_number: str,
                                    hardware_name: str, hardware_revision: str,
                                    add_hardware: bool = True, apply_hardware: bool = True) -> Dict[str, int]:
    """Add and apply specific hardware to a specific machine by serial number

    Args:
        manager: HardwareManager instance
        serial_number: Machine serial number
        hardware_name: Hardware name (e.g., 'Cooling', 'Self Cleaning')
        hardware_revision: Hardware revision (e.g., 'A', 'B')
        add_hardware: Whether to add hardware to machine
        apply_hardware: Whether to apply hardware settings

    Returns:
        Dict with statistics
    """
    stats = {"added": 0, "applied": 0, "removed": 0, "skipped": 0, "errors": 0}

    try:
        # Initialize Notion client
        notion_token = os.getenv("NOTION_TOKEN")
        if not notion_token:
            raise ValueError("NOTION_TOKEN is required in .env file")

        notion_database_id = os.getenv("NOTION_MACHINE_HARDWARE_ID")
        if not notion_database_id:
            raise ValueError("NOTION_MACHINE_HARDWARE_ID is required in .env file")

        notion = Client(auth=notion_token)

        print(f"🔍 Looking for machine with serial number: {serial_number}")

        # Get all machines and find the one with matching serial number
        machines = manager.list_machines()
        machine_id = None

        # Try exact match first
        if serial_number in machines:
            machine_id = int(machines[serial_number])
        else:
            # Try matching last 5 digits
            serial_suffix = serial_number[-5:]
            if serial_suffix in machines:
                machine_id = int(machines[serial_suffix])

        if not machine_id:
            print(f"❌ Machine with serial number {serial_number} not found")
            print(f"Available machines: {list(machines.keys())}")
            stats["errors"] = 1
            return stats

        # Get machine info to display name
        try:
            machine_info = manager.get_machine_info(machine_id)
            machine_name = machine_info.get('name', f'Machine {machine_id}')
            print(f"✅ Found machine: {machine_name} [ID: {machine_id}]")
        except:
            print(f"✅ Found machine ID: {machine_id}")

        # Get all hardware to find the correct hardware ID
        all_hardware = manager.list_hardware(all_revisions=True)

        # Find the specific hardware
        target_hardware = None
        for hw in all_hardware:
            if hw['name'].lower() == hardware_name.lower() and hw['rev'].lower() == hardware_revision.lower():
                target_hardware = hw
                break

        if not target_hardware:
            print(f"❌ Hardware '{hardware_name}' Rev '{hardware_revision}' not found")
            stats["errors"] = 1
            return stats

        hardware_id = target_hardware['id']
        print(f"✅ Found hardware: {hardware_name} Rev {hardware_revision} [ID: {hardware_id}]")

        # Get current machine hardware
        machine_hardware = manager.list_hardware(machine_id=machine_id)

        # Check if this hardware is already added to the machine
        hardware_already_added = any(hw['id'] == hardware_id for hw in machine_hardware)

        # Remove old revisions of the same hardware type if adding new hardware
        if add_hardware:
            old_revisions = [hw for hw in machine_hardware if hw['name'].lower() == hardware_name.lower() and hw['id'] != hardware_id]
            for old_hw in old_revisions:
                try:
                    print(f"  🗑️ Removing old revision: {old_hw['name']} Rev {old_hw['rev']} [ID: {old_hw['id']}]")
                    manager.remove_hardware_from_machine(machine_id, old_hw['id'])
                    stats["removed"] += 1
                except Exception as e:
                    print(f"  ❌ Error removing old hardware: {e}")
                    stats["errors"] += 1

        # Add hardware to machine if requested and not already added
        if add_hardware:
            if not hardware_already_added:
                try:
                    print(f"  ➕ Adding {hardware_name} Rev {hardware_revision} [ID: {hardware_id}] to machine")
                    manager.add_hardware_to_machine(machine_id, hardware_id)
                    stats["added"] += 1
                except Exception as e:
                    print(f"  ❌ Error adding hardware to machine: {e}")
                    stats["errors"] += 1
                    return stats
            else:
                print(f"  ✓ {hardware_name} Rev {hardware_revision} [ID: {hardware_id}] already added to machine")
                if not apply_hardware:  # Only count as skipped if we're not applying
                    stats["skipped"] += 1

        # Apply hardware to machine if requested
        if apply_hardware:
            if not hardware_already_added and not add_hardware:
                print(f"  ⚠️ Cannot apply {hardware_name} Rev {hardware_revision} [ID: {hardware_id}] - not added to machine")
                stats["skipped"] += 1
                return stats

            try:
                print(f"  ✅ Applying {hardware_name} Rev {hardware_revision} [ID: {hardware_id}]")
                manager.apply_settings(hardware_id, machine_id)
                manager.apply_boards(hardware_id, machine_id)
                manager.apply_profile_values(hardware_id, machine_id)
                stats["applied"] += 1
            except Exception as e:
                print(f"  ❌ Error applying hardware: {e}")
                stats["errors"] += 1

        # Print summary
        print(f"\n📊 Summary for machine {serial_number}:")
        if add_hardware:
            print(f"  Added: {stats['added']}")
            print(f"  Removed: {stats['removed']}")
        if apply_hardware:
            print(f"  Applied: {stats['applied']}")
        if stats['skipped'] > 0:
            print(f"  Skipped: {stats['skipped']}")
        if stats['errors'] > 0:
            print(f"  Errors: {stats['errors']}")

        return stats

    except Exception as e:
        print(f"❌ Error processing hardware for machine {serial_number}: {e}")
        stats["errors"] = 1
        return stats

def add_all_hardware_to_machine(manager: HardwareManager, serial_number: str,
                              add_hardware: bool = True, apply_hardware: bool = True) -> Dict[str, int]:
    """Add and apply all hardware to a specific machine by serial number
    
    Args:
        manager: HardwareManager instance
        serial_number: Machine serial number
        add_hardware: Whether to add hardware to machine
        apply_hardware: Whether to apply hardware settings
        
    Returns:
        Dict with operation statistics
    """
    try:
        print("\n" + "="*80)
        print(f" PROCESSING MACHINE: {serial_number} ".center(80, "="))
        print("="*80)
        
        # Initialize Notion client
        notion_token = os.getenv("NOTION_TOKEN")
        if not notion_token:
            raise ValueError("NOTION_TOKEN is required in .env file")
        
        notion = Client(auth=notion_token)
        
        # Get the database ID for machine hardware assignments
        notion_database_id = os.getenv("NOTION_MACHINE_HARDWARE_ID")
        if not notion_database_id:
            raise ValueError("NOTION_MACHINE_HARDWARE_ID is required in .env file")
        
        print("Fetching machine hardware assignments from Notion...")
        
        # Get all machine hardware assignments from Notion
        machines = get_machine_hardware_assignments_from_notion(notion, notion_database_id)
        
        # Find the specific machine
        target_machine = None
        for machine in machines:
            if machine['serial_number'] == serial_number or machine['serial_number'][-5:] == serial_number[-5:]:
                target_machine = machine
                break
        
        if not target_machine:
            print(f"❌ Machine with serial number {serial_number} not found in Notion database")
            return {"added": 0, "applied": 0, "removed": 0, "skipped": 0, "errors": 1}
        
        print(f"✅ Found machine in Notion: {target_machine['serial_number']}")
        
        # Get all hardware to find the correct hardware IDs
        all_hardware = manager.list_hardware(all_revisions=True)
        
        # Create lookup dictionaries for hardware
        hardware_lookup = {}  # (name, rev) -> id
        hardware_name_lookup = {}  # id -> name
        hardware_rev_lookup = {}   # id -> rev
        
        for hw in all_hardware:
            key = (hw['name'].lower(), hw['rev'].lower())
            hardware_lookup[key] = hw['id']
            hardware_name_lookup[hw['id']] = hw['name']
            hardware_rev_lookup[hw['id']] = hw['rev']
        
        # Get list of machines with their serial numbers
        serial_to_admin = manager.list_machines()
        
        # Check if the serial number exists in our system
        machine_id = None
        for serial, admin_id in serial_to_admin.items():
            if serial_number == serial:
                machine_id = admin_id
                break
        
        if not machine_id:
            print(f"❌ Machine with serial number ending in '{serial_number}' not found in system")
            return {"added": 0, "applied": 0, "removed": 0, "skipped": 0, "errors": 1}
        
        print(f"📌 Machine: SN: {serial_number}, Admin ID: {machine_id}")
        
        # Get current hardware on the machine
        machine_hardware = manager.list_hardware(machine_id=machine_id)
        
        stats = {"added": 0, "applied": 0, "removed": 0, "skipped": 0, "errors": 0}
        
        print(f"Processing {len(target_machine['hardware_assignments'])} hardware assignments...")
        
        # Process each hardware assignment
        for assignment in target_machine['hardware_assignments']:
            try:
                hardware_name = assignment['hardware_name']
                hardware_rev = assignment['hardware_rev']
                
                print(f"\n--- Processing {hardware_name} Rev {hardware_rev} ---")
                
                # Find the hardware ID
                hardware_key = (hardware_name.lower(), hardware_rev.lower())
                hardware_id = hardware_lookup.get(hardware_key)
                
                if not hardware_id:
                    print(f"❌ Hardware '{hardware_name}' Rev '{hardware_rev}' not found in system")
                    stats["errors"] += 1
                    continue
                
                # Check if this hardware is already on the machine
                hardware_already_added = any(
                    hw['id'] == hardware_id for hw in machine_hardware
                )
                
                if add_hardware:
                    # Check if a different revision of this hardware is already on the machine
                    different_revs = [
                        hw for hw in machine_hardware 
                        if hw['name'].lower() == hardware_name.lower() and hw['id'] != hardware_id
                    ]
                    
                    print(f"  Debug: Found {len(different_revs)} different revisions of {hardware_name} on machine")
                    for hw in machine_hardware:
                        print(f"  Debug: Machine has {hw['name']} Rev {hw['rev']} [ID: {hw['id']}]")
                    
                    # Remove any different revisions of this hardware
                    for old_hw in different_revs:
                        try:
                            print(f"  🗑️ Removing {old_hw['name']} Rev {old_hw['rev']} [ID: {old_hw['id']}] from machine")
                            manager.remove_hardware_from_machine(machine_id, old_hw['id'])
                            stats["removed"] += 1
                            # Update machine_hardware list to reflect the removal
                            machine_hardware = [hw for hw in machine_hardware if hw['id'] != old_hw['id']]
                        except Exception as e:
                            print(f"  ❌ Error removing old hardware: {e}")
                            stats["errors"] += 1
                    
                    # Add the new hardware if it's not already added
                    if not hardware_already_added:
                        try:
                            print(f"  ➕ Adding {hardware_name} Rev {hardware_rev} [ID: {hardware_id}] to machine")
                            manager.add_hardware_to_machine(machine_id, hardware_id)
                            stats["added"] += 1
                            # Update machine_hardware list to reflect the addition
                            new_hw = {'id': hardware_id, 'name': hardware_name, 'rev': hardware_rev}
                            machine_hardware.append(new_hw)
                        except Exception as e:
                            print(f"  ❌ Error adding hardware to machine: {e}")
                            stats["errors"] += 1
                            continue
                    else:
                        print(f"  ✓ {hardware_name} Rev {hardware_rev} [ID: {hardware_id}] already added to machine")
                        if not apply_hardware:  # Only count as skipped if we're not applying
                            stats["skipped"] += 1
                
                # Apply hardware to machine if requested
                if apply_hardware:
                    if not hardware_already_added and not add_hardware:
                        print(f"  ⚠️ Cannot apply {hardware_name} Rev {hardware_rev} [ID: {hardware_id}] - not added to machine")
                        stats["skipped"] += 1
                        continue
                        
                    try:
                        print(f"  ✅ Applying {hardware_name} Rev {hardware_rev} [ID: {hardware_id}]")
                        manager.apply_settings(hardware_id, machine_id)
                        manager.apply_boards(hardware_id, machine_id)
                        manager.apply_profile_values(hardware_id, machine_id)
                        stats["applied"] += 1
                    except Exception as e:
                        print(f"  ❌ Error applying hardware: {e}")
                        stats["errors"] += 1
                
            except Exception as e:
                print(f"❌ Error processing {assignment.get('hardware_name', 'Unknown')} Rev {assignment.get('hardware_rev', 'Unknown')}: {e}")
                stats["errors"] += 1
        
        # Build summary message based on operations performed
        print("\n" + "-"*80)
        print(" OPERATION SUMMARY ".center(80, "-"))
        print("-"*80)
        if add_hardware:
            print(f"  Hardware Added: +{stats['added']}")
            print(f"  Old Revisions Removed: {stats['removed']}")
        if apply_hardware:
            print(f"  Hardware Applied: +{stats['applied']}")
        print(f"  Skipped: {stats['skipped']}")
        print(f"  Errors: {stats['errors']}")
        
        print("\n" + "="*80)
        print(" OPERATION COMPLETE ".center(80, "="))
        print("="*80)
        
        return stats
        
    except Exception as e:
        print(f"\n❌ Error processing machine {serial_number}: {e}")
        import traceback
        traceback.print_exc()
        return {"added": 0, "applied": 0, "removed": 0, "skipped": 0, "errors": 1}

def validate_command_line_slot_numbers(slot_numbers: str) -> str:
    """
    Validate slot numbers from command line arguments.
    Returns the validated slot numbers or raises ValueError if invalid.
    """
    if not slot_numbers:
        return slot_numbers
        
    # Check if it's a comma-separated list of integers
    numbers = slot_numbers.split(',')
    for num in numbers:
        num = num.strip()
        if num:  # Skip empty entries
            try:
                int(num)
            except ValueError:
                raise ValueError(f"Invalid slot number: '{num}' is not an integer")
    
    return slot_numbers

def get_notion_hardware_version(notion_client: Client, hardware_name: str, hardware_rev: str) -> str:
    """Get the version number for a specific hardware from Notion profile values database"""
    try:
        # Get the hardware revisions database to find the profile values database
        hardware_revisions_db_id = os.getenv("NOTION_HARDWARE_REVISIONS_ID")
        if not hardware_revisions_db_id:
            raise ValueError("NOTION_HARDWARE_REVISIONS_ID is required in .env file")

        # Query for the specific hardware
        filter_params = {
            "and": [
                {
                    "property": "Name",
                    "title": {
                        "equals": hardware_name
                    }
                },
                {
                    "property": "Rev",
                    "select": {
                        "equals": hardware_rev
                    }
                }
            ]
        }

        response = notion_client.databases.query(
            database_id=hardware_revisions_db_id,
            filter=filter_params
        )

        if not response.get('results'):
            print(f"⚠️ Hardware '{hardware_name}' Rev '{hardware_rev}' not found in Notion")
            return None

        # Get the profile values database ID from the hardware record
        hardware_record = response['results'][0]
        props = hardware_record['properties']

        # Look for profile values database relation
        profile_db_relation = None
        for prop_name, prop_data in props.items():
            if 'relation' in prop_data and 'app' in prop_name.lower():
                profile_db_relation = prop_data['relation']
                break

        if not profile_db_relation or not profile_db_relation:
            print(f"⚠️ No profile values database found for {hardware_name} Rev {hardware_rev}")
            return None

        # Get the profile values database ID (this is a bit tricky with Notion API)
        # For now, we'll assume there's a direct property with the version number
        version_property = None
        for prop_name, prop_data in props.items():
            if 'version' in prop_name.lower() and prop_data.get('type') == 'number':
                version_property = prop_data
                break

        if version_property and version_property.get('number') is not None:
            return str(version_property['number'])

        print(f"⚠️ No version number found for {hardware_name} Rev {hardware_rev}")
        return None

    except Exception as e:
        print(f"❌ Error getting Notion hardware version: {e}")
        return None

def save_machine_data_snapshot(manager: HardwareManager, machine_id: int, hardware_id: int,
                              snapshot_dir: str) -> Dict[str, str]:
    """Save a snapshot of current machine data (settings, boards, profile values)"""
    import json
    from datetime import datetime

    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    snapshot_files = {}

    try:
        # Create snapshot directory if it doesn't exist
        os.makedirs(snapshot_dir, exist_ok=True)

        # Save current settings
        settings = manager.get_machine_settings(machine_id)
        settings_file = os.path.join(snapshot_dir, f"machine_{machine_id}_settings_{timestamp}.json")
        with open(settings_file, 'w') as f:
            json.dump(settings, f, indent=2)
        snapshot_files['settings'] = settings_file

        # Save current boards
        boards = manager.get_machine_boards(machine_id)
        boards_file = os.path.join(snapshot_dir, f"machine_{machine_id}_boards_{timestamp}.json")
        with open(boards_file, 'w') as f:
            json.dump(boards, f, indent=2)
        snapshot_files['boards'] = boards_file

        # Save current profile values
        profile_values = manager.get_machine_profile_values(machine_id)
        profile_values_file = os.path.join(snapshot_dir, f"machine_{machine_id}_profile_values_{timestamp}.json")
        with open(profile_values_file, 'w') as f:
            json.dump(profile_values, f, indent=2)
        snapshot_files['profile_values'] = profile_values_file

        print(f"📸 Snapshot saved to {snapshot_dir}")
        return snapshot_files

    except Exception as e:
        print(f"❌ Error saving machine data snapshot: {e}")
        return {}

def compare_machine_data(before_files: Dict[str, str], after_files: Dict[str, str],
                        output_file: str) -> None:
    """Compare before and after machine data and output differences to a file"""
    import json
    from datetime import datetime

    try:
        differences = {
            'timestamp': datetime.now().isoformat(),
            'settings': {'added': [], 'removed': [], 'modified': []},
            'boards': {'added': [], 'removed': [], 'modified': []},
            'profile_values': {'added': [], 'removed': [], 'modified': []}
        }

        # Compare each data type
        for data_type in ['settings', 'boards', 'profile_values']:
            if data_type in before_files and data_type in after_files:
                # Load before and after data
                with open(before_files[data_type], 'r') as f:
                    before_data = json.load(f)
                with open(after_files[data_type], 'r') as f:
                    after_data = json.load(f)

                # Create lookup dictionaries
                if data_type == 'settings':
                    before_dict = {(item.get('name', ''), item.get('address', 0)): item for item in before_data}
                    after_dict = {(item.get('name', ''), item.get('address', 0)): item for item in after_data}
                elif data_type == 'boards':
                    before_dict = {item.get('id', 0): item for item in before_data}
                    after_dict = {item.get('id', 0): item for item in after_data}
                elif data_type == 'profile_values':
                    before_dict = {(item.get('detail', {}).get('name', ''), item.get('detail', {}).get('macro', '')): item for item in before_data}
                    after_dict = {(item.get('detail', {}).get('name', ''), item.get('detail', {}).get('macro', '')): item for item in after_data}

                # Find added items
                for key in after_dict:
                    if key not in before_dict:
                        differences[data_type]['added'].append(after_dict[key])

                # Find removed items
                for key in before_dict:
                    if key not in after_dict:
                        differences[data_type]['removed'].append(before_dict[key])

                # Find modified items
                for key in before_dict:
                    if key in after_dict:
                        if before_dict[key] != after_dict[key]:
                            differences[data_type]['modified'].append({
                                'key': key,
                                'before': before_dict[key],
                                'after': after_dict[key]
                            })

        # Write differences to output file
        with open(output_file, 'w') as f:
            json.dump(differences, f, indent=2)

        # Print summary
        print(f"\n📊 Comparison Results:")
        for data_type in ['settings', 'boards', 'profile_values']:
            added = len(differences[data_type]['added'])
            removed = len(differences[data_type]['removed'])
            modified = len(differences[data_type]['modified'])
            print(f"  {data_type.title()}: +{added} -{removed} ~{modified}")

        print(f"📄 Detailed diff saved to: {output_file}")

    except Exception as e:
        print(f"❌ Error comparing machine data: {e}")

def check_and_update_machine_version(manager: HardwareManager, serial_number: str,
                                   snapshot_dir: str = "./snapshots") -> Dict[str, any]:
    """Check machine hardware versions against Notion and update if needed"""
    try:
        # Initialize Notion client
        notion_token = os.getenv("NOTION_TOKEN")
        if not notion_token:
            raise ValueError("NOTION_TOKEN is required in .env file")

        notion = Client(auth=notion_token)

        print(f"\n🔍 Checking version sync for machine: {serial_number}")

        # Get machine by serial number
        machines = manager.list_machines()
        machine_id = None

        if serial_number in machines:
            machine_id = int(machines[serial_number])
        else:
            serial_suffix = serial_number[-5:]
            if serial_suffix in machines:
                machine_id = int(machines[serial_suffix])

        if not machine_id:
            return {"error": f"Machine with serial number {serial_number} not found"}

        # Get machine info
        machine_info = manager.get_machine_info(machine_id)
        machine_name = machine_info.get('name', f'Machine {machine_id}')
        print(f"✅ Found machine: {machine_name} [ID: {machine_id}]")

        # Get hardware assigned to this machine
        machine_hardware = manager.list_hardware(machine_id=machine_id)
        if not machine_hardware:
            return {"status": "no_hardware", "message": "No hardware assigned to this machine"}

        results = {
            "machine_id": machine_id,
            "machine_name": machine_name,
            "serial_number": serial_number,
            "hardware_checks": [],
            "updates_applied": []
        }

        for hw in machine_hardware:
            hardware_name = hw['name']
            hardware_rev = hw['rev']
            hardware_id = hw['id']

            print(f"\n🔧 Checking {hardware_name} Rev {hardware_rev}...")

            # Get Notion version for this hardware
            notion_version = get_notion_hardware_version(notion, hardware_name, hardware_rev)
            if not notion_version:
                results["hardware_checks"].append({
                    "hardware": f"{hardware_name} Rev {hardware_rev}",
                    "status": "version_not_found",
                    "notion_version": None,
                    "machine_version": None
                })
                continue

            # Get machine's current profile values for this hardware
            machine_profile_values = manager.get_machine_profile_values(machine_id)

            # Look for version profile value
            machine_version = None
            for pv in machine_profile_values:
                detail = pv.get('detail', {})
                if (detail.get('name', '').lower() == 'version' or
                    detail.get('macro', '').lower() == 'version' or
                    'version' in detail.get('name', '').lower()):
                    machine_version = pv.get('value', pv.get('currentValue'))
                    break

            print(f"  Notion version: {notion_version}")
            print(f"  Machine version: {machine_version}")

            # Check if versions match
            if str(notion_version) != str(machine_version):
                print(f"  ⚠️ Version mismatch detected!")

                # Save snapshot before applying changes
                print(f"  📸 Saving pre-update snapshot...")
                before_snapshot = save_machine_data_snapshot(
                    manager, machine_id, hardware_id,
                    os.path.join(snapshot_dir, f"machine_{serial_number}")
                )

                # Apply hardware to machine
                print(f"  🔄 Applying {hardware_name} Rev {hardware_rev} to machine...")
                try:
                    manager.apply_settings(hardware_id, machine_id)
                    manager.apply_boards(hardware_id, machine_id)
                    manager.apply_profile_values(hardware_id, machine_id)
                    print(f"  ✅ Hardware applied successfully")

                    # Save snapshot after applying changes
                    print(f"  📸 Saving post-update snapshot...")
                    after_snapshot = save_machine_data_snapshot(
                        manager, machine_id, hardware_id,
                        os.path.join(snapshot_dir, f"machine_{serial_number}")
                    )

                    # Generate diff report
                    from datetime import datetime
                    diff_file = os.path.join(
                        snapshot_dir, f"machine_{serial_number}",
                        f"diff_{hardware_name}_{hardware_rev}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
                    )
                    compare_machine_data(before_snapshot, after_snapshot, diff_file)

                    results["updates_applied"].append({
                        "hardware": f"{hardware_name} Rev {hardware_rev}",
                        "notion_version": notion_version,
                        "old_machine_version": machine_version,
                        "diff_file": diff_file
                    })

                except Exception as e:
                    print(f"  ❌ Error applying hardware: {e}")
                    results["hardware_checks"].append({
                        "hardware": f"{hardware_name} Rev {hardware_rev}",
                        "status": "update_failed",
                        "error": str(e)
                    })
            else:
                print(f"  ✅ Versions match - no update needed")
                results["hardware_checks"].append({
                    "hardware": f"{hardware_name} Rev {hardware_rev}",
                    "status": "up_to_date",
                    "notion_version": notion_version,
                    "machine_version": machine_version
                })

        return results

    except Exception as e:
        print(f"❌ Error checking machine version: {e}")
        return {"error": str(e)}

def remove_all_hardware_from_machine(manager: HardwareManager, serial_number: str) -> Dict[str, int]:
    """Remove all hardware from a specific machine by serial number
    
    Args:
        manager: HardwareManager instance
        serial_number: Machine serial number
        
    Returns:
        Dict with operation statistics
    """
    try:
        print("\n" + "="*80)
        print(f" REMOVING ALL HARDWARE FROM MACHINE: {serial_number} ".center(80, "="))
        print("="*80)
        
        # Get list of machines with their serial numbers
        serial_to_admin = manager.list_machines()
        
        # Check if the serial number exists in our system
        machine_id = None
        for serial, admin_id in serial_to_admin.items():
            if serial_number == serial:
                machine_id = admin_id
                break
        
        if not machine_id:
            print(f"❌ Machine with serial number ending in '{serial_number}' not found in system")
            return {"removed": 0, "errors": 1}
                # Get list of machines with their serial numbers
        
        print(f"📌 Machine: SN: {serial_number}, Admin ID: {machine_id}")
        
        # Get current hardware on the machine
        machine_hardware = manager.list_hardware(machine_id=machine_id)
        
        if not machine_hardware:
            print("✅ No hardware found on machine - nothing to remove")
            return {"removed": 0, "errors": 0}
        
        print(f"Found {len(machine_hardware)} hardware items to remove:")
        for hw in machine_hardware:
            print(f"  - {hw['name']} Rev {hw['rev']} [ID: {hw['id']}]")
        
        stats = {"removed": 0, "errors": 0}
        
        # Remove each hardware item
        for hw in machine_hardware:
            try:
                print(f"🗑️ Removing {hw['name']} Rev {hw['rev']} [ID: {hw['id']}] from machine")
                manager.remove_hardware_from_machine(machine_id, hw['id'])
                stats["removed"] += 1
            except Exception as e:
                print(f"❌ Error removing {hw['name']} Rev {hw['rev']} [ID: {hw['id']}]: {e}")
                stats["errors"] += 1
        
        print("\n" + "-"*80)
        print(" REMOVAL SUMMARY ".center(80, "-"))
        print("-"*80)
        print(f"  Hardware Removed: {stats['removed']}")
        print(f"  Errors: {stats['errors']}")
        
        print("\n" + "="*80)
        print(" REMOVAL COMPLETE ".center(80, "="))
        print("="*80)
        
        return stats
        
    except Exception as e:
        print(f"\n❌ Error removing hardware from machine {serial_number}: {e}")
        import traceback
        traceback.print_exc()
        return {"removed": 0, "errors": 1}

def main():
    parser = argparse.ArgumentParser(description="Hardware Import/Export Tool")
    
    # Add common arguments
    parser.add_argument("--api-key", help="API key for authentication")
    parser.add_argument("--api-endpoint", help="API endpoint URL")
    
    # Create subparsers for different commands
    subparsers = parser.add_subparsers(dest="command", help="Command to execute")
    
    # Hardware import/export commands
    hardware_parser = subparsers.add_parser("hardware", help="Hardware import/export")
    hardware_subparsers = hardware_parser.add_subparsers(dest="action", help="Action to perform")
    
    # CSV import
    csv_import_parser = hardware_subparsers.add_parser("import", help="Import hardware from CSV")
    csv_import_parser.add_argument("csv_file", help="CSV file to import")
    
    # Notion import
    notion_import_parser = hardware_subparsers.add_parser("import-notion", help="Import hardware from Notion")
    notion_import_parser.add_argument("--database-id", help="Notion database ID (defaults to NOTION_HARDWARE_REVISIONS_ID from .env)")
    
    # CSV export
    csv_export_parser = hardware_subparsers.add_parser("export", help="Export hardware to CSV")
    csv_export_parser.add_argument("csv_file", help="CSV file to export to")
    
    # Settings import commands
    settings_parser = subparsers.add_parser("settings", help="Settings import")
    settings_subparsers = settings_parser.add_subparsers(dest="source", help="Source to import from")
    
    # CSV settings import
    csv_parser = settings_subparsers.add_parser("csv", help="Import settings from CSV file")
    csv_parser.add_argument("csv_file", help="CSV file containing settings definitions")
    csv_parser.add_argument("--hardware-id", type=int, required=True,
                           help="Hardware ID to associate settings with")
    csv_parser.add_argument("--revision", required=True,
                           help="Hardware revision column to use (e.g., 'Rev A', 'Rev B')")
    
    # Notion settings import
    notion_parser = settings_subparsers.add_parser("notion", help="Import settings from Notion database")
    notion_parser.add_argument("database_id", help="Notion database ID")
    notion_parser.add_argument("--hardware-id", type=int, required=True,
                             help="Hardware ID to associate settings with")
    notion_parser.add_argument("--hardware-filter", help="Filter settings by hardware name")
    notion_parser.add_argument("--revision", default="Rev A",
                             help="Hardware revision property to use (e.g., 'Rev A', 'Rev B')")
    
    # Boards import command
    boards_parser = subparsers.add_parser("boards", help="Boards import")
    boards_parser.add_argument("database_id", help="Notion database ID for boards")
    boards_parser.add_argument("--hardware-id", type=int, required=True,
                             help="Hardware ID to associate boards with")
    boards_parser.add_argument("--revision", default="Rev A",
                             help="Hardware revision property to use (e.g., 'Rev A', 'Rev B')")
    
    # App settings import command
    app_settings_parser = subparsers.add_parser("app-settings", help="App settings import")
    app_settings_parser.add_argument("database_id", help="Notion database ID for app settings")
    app_settings_parser.add_argument("--hardware-id", type=int, required=True,
                                   help="Hardware ID to associate app settings with")
    app_settings_parser.add_argument("--revision", default="Rev A",
                                   help="Hardware revision property to use (e.g., 'Rev A', 'Rev B')")

    # Add new command for importing all settings
    import_all_parser = subparsers.add_parser("import-all", 
                                            help="Import settings and boards from Notion for all hardware revisions")

    # Add new command for importing a specific database
    specific_db_parser = subparsers.add_parser("import-specific", 
                                             help="Import a specific database for a hardware type")
    specific_db_parser.add_argument("--hardware", required=True,
                                  help="Hardware name (e.g., 'Cooling', 'Self Cleaning')")
    specific_db_parser.add_argument("--db-type", required=True, choices=["eeprom", "boards", "app_settings"],
                                  help="Database type to import")

    # Add command for applying hardware to machines from Notion
    apply_hardware_parser = subparsers.add_parser("apply-hardware", 
                                                help="Apply hardware to machines from Notion database")
    apply_hardware_parser.add_argument("--database-id", 
                                     help="Notion database ID (defaults to NOTION_MACHINE_HARDWARE_ID from .env)")
    apply_hardware_parser.add_argument("--add-only", action="store_true",
                                     help="Only add hardware to machines, don't apply settings")
    apply_hardware_parser.add_argument("--apply-only", action="store_true",
                                     help="Only apply hardware settings, don't add hardware to machines")

    # Add command for adding and applying hardware to a specific machine
    machine_hardware_parser = subparsers.add_parser("machine-hardware", 
                                                  help="Add and apply specific hardware to a specific machine")
    machine_hardware_parser.add_argument("--serial", required=True,
                                       help="Machine serial number")
    machine_hardware_parser.add_argument("--hardware", required=True,
                                       help="Hardware name (e.g., 'Cooling', 'Self Cleaning')")
    machine_hardware_parser.add_argument("--revision", required=True,
                                       help="Hardware revision (e.g., 'A', 'B')")
    machine_hardware_parser.add_argument("--add-only", action="store_true",
                                       help="Only add hardware to machine, don't apply settings")
    machine_hardware_parser.add_argument("--apply-only", action="store_true",
                                       help="Only apply hardware settings, don't add hardware to machine")

    # Add command for adding and applying all hardware to a specific machine
    all_machine_hardware_parser = subparsers.add_parser("machine-all-hardware", 
                                                      help="Add and apply all hardware to a specific machine")
    all_machine_hardware_parser.add_argument("--serial", required=True,
                                           help="Machine serial number")
    all_machine_hardware_parser.add_argument("--add-only", action="store_true",
                                           help="Only add hardware to machine, don't apply settings")
    all_machine_hardware_parser.add_argument("--apply-only", action="store_true",
                                           help="Only apply hardware settings, don't add hardware to machine")

    # Add command for removing all hardware from a machine
    remove_all_parser = subparsers.add_parser("remove-all-hardware", 
                                            help="Remove all hardware from a specific machine")
    remove_all_parser.add_argument("--serial", required=True,
                                 help="Machine serial number")

    # Create board command
    create_board_parser = subparsers.add_parser("create-board", help="Create a new hardware board")
    create_board_parser.add_argument("--hardware-id", type=int, help="Hardware ID")
    create_board_parser.add_argument("--protocol-id", type=int, help="Protocol ID")
    create_board_parser.add_argument("--pcb-major", type=int, help="PCB Major Version")
    create_board_parser.add_argument("--pcb-minor", type=int, help="PCB Minor Version")
    create_board_parser.add_argument("--pcb-patch", type=int, help="PCB Patch Version")
    create_board_parser.add_argument("--pump-rating", type=int, default=0, help="Pump Rating")
    create_board_parser.add_argument("--application-id", type=int, help="Application ID")
    create_board_parser.add_argument("--type-id", type=int, help="Type ID")
    create_board_parser.add_argument("--slot-numbers", type=str, help="Comma-separated list of slot numbers or ingredient IDs")

    # Update board command
    update_board_parser = subparsers.add_parser("update-board", help="Update an existing hardware board")
    update_board_parser.add_argument("id", type=int, help="Board ID")
    update_board_parser.add_argument("--hardware-id", type=int, help="Hardware ID")
    update_board_parser.add_argument("--protocol-id", type=int, help="Protocol ID")
    update_board_parser.add_argument("--pcb-major", type=int, help="PCB Major Version")
    update_board_parser.add_argument("--pcb-minor", type=int, help="PCB Minor Version")
    update_board_parser.add_argument("--pcb-patch", type=int, help="PCB Patch Version")
    update_board_parser.add_argument("--pump-rating", type=int, help="Pump Rating")
    update_board_parser.add_argument("--application-id", type=int, help="Application ID")
    update_board_parser.add_argument("--type-id", type=int, help="Type ID")
    update_board_parser.add_argument("--slot-numbers", type=str, help="Comma-separated list of slot numbers or ingredient IDs")

    # Audit machine command
    audit_parser = subparsers.add_parser("audit-machine", help="Audit machine settings against hardware definitions")
    audit_parser.add_argument("--machine-id", type=int, help="Machine ID to audit")
    audit_parser.add_argument("--serial", help="Machine serial number to audit")
    audit_parser.add_argument("--non-interactive", action="store_true", help="Run audit without interactive prompts")

    # Version check and update command
    version_check_parser = subparsers.add_parser("check-version", help="Check and update machine hardware versions against Notion")
    version_check_parser.add_argument("--serial", required=True, help="Machine serial number to check")
    version_check_parser.add_argument("--snapshot-dir", default="./snapshots", help="Directory to save snapshots (default: ./snapshots)")

    args = parser.parse_args()
    
    # Get API credentials from arguments or environment variables
    api_key = args.api_key or os.getenv("API_KEY")
    api_endpoint = args.api_endpoint or os.getenv("API_ENDPOINT")
    
    if not api_key or not api_endpoint:
        print("Error: API_KEY and API_ENDPOINT are required (either as arguments or in .env file)")
        return 1
    
    try:
        # Initialize hardware manager
        manager = HardwareManager(api_key, api_endpoint)
        
        if args.command == "import-all":
            import_settings_for_all_revisions(manager)
        elif args.command == "hardware":
            if args.action == "import":
                import_hardware_from_csv(args.csv_file, manager)
            elif args.action == "import-notion":
                database_id = args.database_id or os.getenv("NOTION_HARDWARE_REVISIONS_ID")
                if not database_id:
                    print("Error: No Notion database ID provided. Either specify --database-id or set NOTION_HARDWARE_REVISIONS_ID in .env")
                    return 1
                import_hardware_from_notion(database_id, manager)
            elif args.action == "export":
                export_hardware_to_csv(args.csv_file, manager)
        elif args.command == "settings":
            if args.source == "csv":
                import_settings_from_csv(args.csv_file, manager, args.hardware_id, args.revision)
            elif args.source == "notion":
                import_settings_from_notion(args.database_id, manager, args.hardware_id, args.hardware_filter, args.revision)
        elif args.command == "boards":
            import_boards_from_notion(args.database_id, manager, args.hardware_id, args.revision)
        elif args.command == "app-settings":
            import_app_settings_from_notion(args.database_id, manager, args.hardware_id, args.revision)
        elif args.command == "import-specific":
            import_specific_database(manager, args.hardware, args.db_type)
        elif args.command == "apply-hardware":
            database_id = args.database_id or os.getenv("NOTION_MACHINE_HARDWARE_ID")
            if not database_id:
                print("Error: No Notion database ID provided. Either specify --database-id or set NOTION_MACHINE_HARDWARE_ID in .env")
                return 1
            
            # Determine whether to add and/or apply hardware based on arguments
            add_hardware = not args.apply_only
            apply_hardware = not args.add_only
            
            # If both --add-only and --apply-only are specified, that's an error
            if args.add_only and args.apply_only:
                print("Error: Cannot specify both --add-only and --apply-only")
                return 1
            
            apply_hardware_assignments_from_notion(database_id, manager, add_hardware, apply_hardware)
        elif args.command == "machine-hardware":
            # Determine whether to add and/or apply hardware based on arguments
            add_hardware = not args.apply_only
            apply_hardware = not args.add_only
            
            # If both --add-only and --apply-only are specified, that's an error
            if args.add_only and args.apply_only:
                print("Error: Cannot specify both --add-only and --apply-only")
                return 1
                
            add_and_apply_hardware_to_machine(
                manager, 
                args.serial, 
                args.hardware, 
                args.revision,
                add_hardware,
                apply_hardware
            )
        elif args.command == "machine-all-hardware":
            # Determine whether to add and/or apply hardware based on arguments
            add_hardware = not args.apply_only
            apply_hardware = not args.add_only
            
            # If both --add-only and --apply-only are specified, that's an error
            if args.add_only and args.apply_only:
                print("Error: Cannot specify both --add-only and --apply-only")
                return 1
                
            add_all_hardware_to_machine(
                manager, 
                args.serial,
                add_hardware,
                apply_hardware
            )
        elif args.command == "remove-all-hardware":
            remove_all_hardware_from_machine(manager, args.serial)
        elif args.command == "create-board":
            # Validate slot numbers if provided
            if args.slot_numbers:
                try:
                    args.slot_numbers = validate_command_line_slot_numbers(args.slot_numbers)
                except ValueError as e:
                    print(f"Error: {e}")
                    return 1
                    
            # Check if we need to run in interactive mode
            if not all([args.hardware_id, args.protocol_id, args.pcb_major, args.pcb_minor, args.pcb_patch,
                       args.application_id, args.type_id]):
                if not args.hardware_id:
                    args.hardware_id = int(input("Hardware ID: "))
                board = interactive_create_hardware_board(manager, args.hardware_id)
            else:
                board = manager.create_hardware_board(
                    args.hardware_id, args.protocol_id, args.pcb_major, args.pcb_minor,
                    args.pcb_patch, args.application_id, args.type_id, args.pump_rating, 
                    args.slot_numbers
                )
                print(json.dumps(board, indent=2))
        elif args.command == "update-board":
            # Validate slot numbers if provided
            if args.slot_numbers:
                try:
                    args.slot_numbers = validate_command_line_slot_numbers(args.slot_numbers)
                except ValueError as e:
                    print(f"Error: {e}")
                    return 1
                    
            # Check if we need to run in interactive mode
            if not all([args.hardware_id, args.protocol_id, args.pcb_major, args.pcb_minor, args.pcb_patch,
                       args.application_id, args.type_id]):
                board = interactive_update_hardware_board(manager, args.id)
            else:
                board = manager.update_hardware_board(
                    args.id, args.hardware_id, args.protocol_id, args.pcb_major, args.pcb_minor,
                    args.pcb_patch, args.application_id, args.type_id, args.pump_rating, 
                    args.slot_numbers
                )
                print(json.dumps(board, indent=2))
        elif args.command == "audit-machine":
            # Determine machine ID
            machine_id = args.machine_id

            if not machine_id and args.serial:
                # Find machine by serial number
                machines = manager.list_machines()
                if args.serial in machines:
                    machine_id = int(machines[args.serial])
                else:
                    # Try matching last 5 digits
                    serial_suffix = args.serial[-5:]
                    if serial_suffix in machines:
                        machine_id = int(machines[serial_suffix])

            if not machine_id:
                if args.serial:
                    print(f"❌ Machine with serial number {args.serial} not found")
                    machines = manager.list_machines()
                    print(f"Available machines: {list(machines.keys())}")
                else:
                    print("❌ Either --machine-id or --serial must be provided")
                return 1

            # Run audit
            interactive = not args.non_interactive
            audit_results = manager.audit_machine_settings(machine_id, interactive)

            if "error" in audit_results:
                print(f"❌ Audit failed: {audit_results['error']}")
                return 1
        elif args.command == "check-version":
            # Run version check and update
            results = check_and_update_machine_version(
                manager,
                args.serial,
                args.snapshot_dir
            )

            if "error" in results:
                print(f"❌ Version check failed: {results['error']}")
                return 1

            # Print summary
            print(f"\n🎯 Version Check Summary for {results['serial_number']}:")
            print(f"  Machine: {results['machine_name']} [ID: {results['machine_id']}]")

            if results.get('updates_applied'):
                print(f"  ✅ Updates applied: {len(results['updates_applied'])}")
                for update in results['updates_applied']:
                    print(f"    - {update['hardware']}: {update['old_machine_version']} → {update['notion_version']}")
                    print(f"      Diff report: {update['diff_file']}")

            if results.get('hardware_checks'):
                up_to_date = [hc for hc in results['hardware_checks'] if hc['status'] == 'up_to_date']
                if up_to_date:
                    print(f"  ✅ Up to date: {len(up_to_date)}")
                    for hc in up_to_date:
                        print(f"    - {hc['hardware']}: v{hc['notion_version']}")
        else:
            parser.print_help()
            return 1
            
    except Exception as e:
        print(f"Error: {e}")
        return 1
    
    return 0


if __name__ == "__main__":
    sys.exit(main())

