[{"id": 11482, "name": "ING_SLOT_LOW_THRESH", "value": "20", "profileId": 134, "detail": {"id": 1, "name": "Ingredient Slot Low Threshold", "macro": "ING_SLOT_LOW_THRESH", "description": "Percentage at which a bottle is marked low.", "paramType": "BYTE", "defaultValue": "20", "minValue": "0", "maxValue": "50", "category": "System", "securityLevel": 2}}, {"id": 11483, "name": "WEEKLY_MAINTENANCE_DAY", "value": "0", "profileId": 134, "detail": {"id": 2, "name": "Weekly Maintenance Day", "macro": "WEEKLY_MAINTENANCE_DAY", "description": "Day of the week where the shutdown procedure forces the longer weekly cleaning procedure. (1 = Monday, 2 = Tuesday, 3 = Wednesday, 4 = Thursday, 5 = Friday, 6 = Saturday, 7 = Sunday)", "paramType": "BYTE", "defaultValue": "0", "minValue": "0", "maxValue": "7", "category": "General", "securityLevel": 5}}, {"id": 11484, "name": "MULTI_CUP_TYPE_AUTOSTART", "value": "0", "profileId": 134, "detail": {"id": 3, "name": "Multi-Cup Autostart", "macro": "MULTI_CUP_TYPE_AUTOSTART", "description": "If this is set to 1, then it automatically sends all drinks with cup type of 1 to the back conveyors and sends drinks of cup type 2 to front rows. Must be turned on in conjunction with EEPROM Setting on machine.", "paramType": "BOOLEAN", "defaultValue": "0", "minValue": "0", "maxValue": "1", "category": "General", "securityLevel": 5}}, {"id": 11485, "name": "SHOT_SIZE", "value": "1.5", "profileId": 134, "detail": {"id": 4, "name": "Shot Size", "macro": "SHOT_SIZE", "description": "Single shot liquor amount (oz).", "paramType": "SINGLE", "defaultValue": "1.5", "minValue": "0", "maxValue": "10", "category": "Drink Builder - Custom", "securityLevel": 1}}, {"id": 11486, "name": "DBL_SHOT_SIZE", "value": "3", "profileId": 134, "detail": {"id": 5, "name": "Double Shot Size", "macro": "DBL_SHOT_SIZE", "description": "Double shot liquor amount (oz).", "paramType": "SINGLE", "defaultValue": "3", "minValue": "0", "maxValue": "10", "category": "Drink Builder - Custom", "securityLevel": 1}}, {"id": 11487, "name": "COMBINE_DRINKS", "value": "0", "profileId": 134, "detail": {"id": 6, "name": "Combine Drinks", "macro": "COMBINE_DRINKS", "description": "Allows two of the same drink to be combined into one Shaker.", "paramType": "BOOLEAN", "defaultValue": "0", "minValue": "0", "maxValue": "1", "category": "General", "securityLevel": 2}}, {"id": 11488, "name": "MIXER_SIZE", "value": "3.5", "profileId": 134, "detail": {"id": 7, "name": "Mixer <PERSON>", "macro": "MIXER_SIZE", "description": "Single highball mixer amount (oz).", "paramType": "SINGLE", "defaultValue": "3.5", "minValue": "0", "maxValue": "10", "category": "Drink Builder - Custom", "securityLevel": 1}}, {"id": 11489, "name": "DBL_MIXER_SIZE", "value": "4.5", "profileId": 134, "detail": {"id": 8, "name": "Double Mixer Size", "macro": "DBL_MIXER_SIZE", "description": "Double highball mixer amount (oz).", "paramType": "SINGLE", "defaultValue": "4.5", "minValue": "0", "maxValue": "10", "category": "Drink Builder - Custom", "securityLevel": 1}}, {"id": 11490, "name": "BIB_SIZE", "value": "0.58", "profileId": 134, "detail": {"id": 9, "name": "Bib Size", "macro": "BIB_SIZE", "description": "Single highball bib amount (oz).", "paramType": "SINGLE", "defaultValue": "0.58", "minValue": "0", "maxValue": "2", "category": "Drink Builder - Custom", "securityLevel": 1}}, {"id": 11491, "name": "DBL_BIB_SIZE", "value": "1.16", "profileId": 134, "detail": {"id": 10, "name": "Double Bib Size", "macro": "DBL_BIB_SIZE", "description": "Double highball bib amount (oz).", "paramType": "SINGLE", "defaultValue": "1.16", "minValue": "0", "maxValue": "4", "category": "Drink Builder - Custom", "securityLevel": 1}}, {"id": 11492, "name": "SINGLE_CUP_TYPE", "value": "18", "profileId": 134, "detail": {"id": 11, "name": "Single Cup Type", "macro": "SINGLE_CUP_TYPE", "description": "Cup type if highball is single, rocks, or short.", "paramType": "WORD", "defaultValue": "18", "minValue": "1", "maxValue": "65535", "category": "Drink Builder - Custom", "securityLevel": 1}}, {"id": 11493, "name": "DOUBLE_CUP_TYPE", "value": "19", "profileId": 134, "detail": {"id": 12, "name": "Double Cup Type", "macro": "DOUBLE_CUP_TYPE", "description": "Cup type if highball is double.", "paramType": "WORD", "defaultValue": "19", "minValue": "1", "maxValue": "65535", "category": "Drink Builder - Custom", "securityLevel": 1}}, {"id": 11494, "name": "SODA_RATIO", "value": "4.8", "profileId": 134, "detail": {"id": 13, "name": "Soda Ratio", "macro": "SODA_RATIO", "description": "Soda mixing ratio.", "paramType": "SINGLE", "defaultValue": "4.0", "minValue": "0", "maxValue": "6", "category": "System", "securityLevel": 2}}, {"id": 11495, "name": "WATER_RATIO", "value": "4.0", "profileId": 134, "detail": {"id": 14, "name": "Water Ratio", "macro": "WATER_RATIO", "description": "Juice mixing ratio.", "paramType": "SINGLE", "defaultValue": "4.0", "minValue": "0", "maxValue": "6", "category": "System", "securityLevel": 2}}, {"id": 11496, "name": "SHOT_CUP", "value": "10", "profileId": 134, "detail": {"id": 15, "name": "Shot Cup Type", "macro": "SHOT_CUP", "description": "Cup type if selected drink is a shot.", "paramType": "WORD", "defaultValue": "10", "minValue": "1", "maxValue": "65535", "category": "Drink Builder - Custom", "securityLevel": 1}}, {"id": 11497, "name": "SPECIALTY_LOOKUP", "value": "1", "profileId": 134, "detail": {"id": 16, "name": "Specialty Drink Lookup", "macro": "SPECIALTY_LOOKUP", "description": "Enables drink lookups by Recipe name or RecipeMap names.", "paramType": "BOOLEAN", "defaultValue": "1", "minValue": "0", "maxValue": "1", "category": "General", "securityLevel": 2}}, {"id": 11498, "name": "HIGHBALL_LOOKUP", "value": "0", "profileId": 134, "detail": {"id": 17, "name": "Highball Lookup", "macro": "HIGHBALL_LOOKUP", "description": "Enables drink lookups by Ingredient name or IngredientMap names.", "paramType": "BOOLEAN", "defaultValue": "0", "minValue": "0", "maxValue": "1", "category": "General", "securityLevel": 2}}, {"id": 11499, "name": "ONLY_SEND_MAKEABLE", "value": "1", "profileId": 134, "detail": {"id": 18, "name": "Only Send Makeable Drinks", "macro": "ONLY_SEND_MAKEABLE", "description": "Non-makeable drinks are added to the system, but not sent to the machine.", "paramType": "BOOLEAN", "defaultValue": "1", "minValue": "0", "maxValue": "1", "category": "General", "securityLevel": 2}}, {"id": 11500, "name": "IGNORE_SPECIALTY_MODS", "value": "0", "profileId": 134, "detail": {"id": 19, "name": "Ignore Specialty Modifiers", "macro": "IGNORE_SPECIALTY_MODS", "description": "Ignores all specialty drink modifiers.", "paramType": "BOOLEAN", "defaultValue": "0", "minValue": "0", "maxValue": "1", "category": "General", "securityLevel": 2}}, {"id": 11501, "name": "IGNORE_HIGHBALL_MODS", "value": "0", "profileId": 134, "detail": {"id": 20, "name": "Ignore Highball Modifiers", "macro": "IGNORE_HIGHBALL_MODS", "description": "Ignores all highball drink modifiers.", "paramType": "BOOLEAN", "defaultValue": "0", "minValue": "0", "maxValue": "1", "category": "General", "securityLevel": 2}}, {"id": 11502, "name": "POST_GARNISH_HIGHBALL_MODS", "value": "1", "profileId": 134, "detail": {"id": 21, "name": "Post-Garnish Highball Modifiers", "macro": "POST_GARNISH_HIGHBALL_MODS", "description": "If enabled, unmatched highball modifiers are added to the order as post-garnishes. If disabled, and drink contains unmatched modifiers, set drink to not makeable and add modifier to drink notes.", "paramType": "BOOLEAN", "defaultValue": "1", "minValue": "0", "maxValue": "1", "category": "General", "securityLevel": 2}}, {"id": 11503, "name": "POST_GARNISH_SPECIALTY_MODS", "value": "1", "profileId": 134, "detail": {"id": 22, "name": "Post-Garnish Specialty Modiifers", "macro": "POST_GARNISH_SPECIALTY_MODS", "description": "If enabled, unmatched specialty drink modifiers are added to the order as post-garnishes. If disabled, and drink contains unmatched modifiers, set drink to not makeable and add modifier to drink notes.", "paramType": "BOOLEAN", "defaultValue": "1", "minValue": "0", "maxValue": "1", "category": "General", "securityLevel": 2}}, {"id": 11504, "name": "ALLOW_UPSELLS", "value": "0", "profileId": 134, "detail": {"id": 23, "name": "Allow <PERSON>", "macro": "ALLOW_UPSELLS", "description": "Recipe ingredient will be swapped with modifier of the same ingredient category.", "paramType": "BOOLEAN", "defaultValue": "0", "minValue": "0", "maxValue": "1", "category": "General", "securityLevel": 2}}, {"id": 11505, "name": "HIGH_BALL_CUP", "value": "18", "profileId": 134, "detail": {"id": 24, "name": "Highball Cup Type", "macro": "HIGH_BALL_CUP", "description": "Cup type if highball is single, rocks, or short.", "paramType": "WORD", "defaultValue": "18", "minValue": "1", "maxValue": "65535", "category": "Drink Builder - Custom", "securityLevel": 1}}, {"id": 11506, "name": "DBL_HIGH_BALL_CUP", "value": "19", "profileId": 134, "detail": {"id": 25, "name": "Double Highball Cup Type", "macro": "DBL_HIGH_BALL_CUP", "description": "Cup type if highball is double.", "paramType": "WORD", "defaultValue": "19", "minValue": "1", "maxValue": "65535", "category": "Drink Builder - Custom", "securityLevel": 1}}, {"id": 11507, "name": "HIGH_BALL_ICE", "value": "4.0", "profileId": 134, "detail": {"id": 26, "name": "Highball Ice Amount", "macro": "HIGH_BALL_ICE", "description": "Single highball ice amount (scoops).", "paramType": "SINGLE", "defaultValue": "4.0", "minValue": "0", "maxValue": "5", "category": "Drink Builder - Custom", "securityLevel": 1}}, {"id": 11508, "name": "DBL_HIGH_BALL_ICE", "value": "5.0", "profileId": 134, "detail": {"id": 27, "name": "Double Highball Ice Amount", "macro": "DBL_HIGH_BALL_ICE", "description": "Double highball ice amount (scoops).", "paramType": "SINGLE", "defaultValue": "5.0", "minValue": "0", "maxValue": "10", "category": "Drink Builder - Custom", "securityLevel": 1}}, {"id": 11509, "name": "SINGLE_CUP_VOLUME", "value": "5", "profileId": 134, "detail": {"id": 28, "name": "Single highball cup volume (oz)", "macro": "SINGLE_CUP_VOLUME", "description": "Single highball drink volume.", "paramType": "SINGLE", "defaultValue": "5", "minValue": "4", "maxValue": "6", "category": "Drink Builder - Custom", "securityLevel": 1}}, {"id": 11510, "name": "DOUBLE_CUP_VOLUME", "value": "10", "profileId": 134, "detail": {"id": 29, "name": "Double highball cup volume (oz)", "macro": "DOUBLE_CUP_VOLUME", "description": "Double highball drink volume.", "paramType": "SINGLE", "defaultValue": "10", "minValue": "8", "maxValue": "12", "category": "Drink Builder - Custom", "securityLevel": 1}}, {"id": 11511, "name": "PARSER_HEADER", "value": "", "profileId": 134, "detail": {"id": 30, "name": "Ticket parser header delimeter", "macro": "PARSER_HEADER", "description": "Marks the start of the order data when parsing VPC tickets.", "paramType": "STRING", "defaultValue": "", "minValue": "", "maxValue": "", "category": "Ticket <PERSON>rser", "securityLevel": 1}}, {"id": 11512, "name": "PARSER_MOD_DIVIDER", "value": "", "profileId": 134, "detail": {"id": 31, "name": "Ticket parser modifier delimeter", "macro": "PARSER_MOD_DIVIDER", "description": "Marks the start of drink modifiers when parsing VPC tickets.", "paramType": "STRING", "defaultValue": "", "minValue": "", "maxValue": "", "category": "Ticket <PERSON>rser", "securityLevel": 1}}, {"id": 11513, "name": "PARSER_SERVER", "value": "", "profileId": 134, "detail": {"id": 32, "name": "Ticket parser server delimeter", "macro": "PARSER_SERVER", "description": "Marks the server name field when parsing VPC tickets.", "paramType": "STRING", "defaultValue": "", "minValue": "", "maxValue": "", "category": "Ticket <PERSON>rser", "securityLevel": 1}}, {"id": 11514, "name": "PARSER_ORDER", "value": "#,Order ID:", "profileId": 134, "detail": {"id": 33, "name": "Ticket parser order number delimeter", "macro": "PARSER_ORDER", "description": "Marks the order number field when parsing VPC tickets.", "paramType": "STRING", "defaultValue": "#,Order ID:", "minValue": "", "maxValue": "", "category": "Ticket <PERSON>rser", "securityLevel": 1}}, {"id": 11515, "name": "PARSER_TABLE", "value": "TAB,TABLE,SEAT,TAKE OUT", "profileId": 134, "detail": {"id": 34, "name": "Ticket parser table number delimeter", "macro": "PARSER_TABLE", "description": "Marks the table number field when parsing VPC tickets.", "paramType": "STRING", "defaultValue": "TAB,TABLE,SEAT,TAKE OUT", "minValue": "", "maxValue": "", "category": "Ticket <PERSON>rser", "securityLevel": 1}}, {"id": 11516, "name": "PARSER_ITEMS_STX", "value": "FIRE,ALCOHOL,DRINK", "profileId": 134, "detail": {"id": 35, "name": "Ticket parser start of drinks/items delimeter", "macro": "PARSER_ITEMS_STX", "description": "Marks the start of the drink data when parsing VPC tickets.", "paramType": "STRING", "defaultValue": "FIRE,ALCOHOL,DRINK", "minValue": "", "maxValue": "", "category": "Ticket <PERSON>rser", "securityLevel": 1}}, {"id": 11517, "name": "PARSER_LINE_CNT", "value": "3", "profileId": 134, "detail": {"id": 36, "name": "Ticket parser start of drinks/items line count", "macro": "PARSER_LINE_CNT", "description": "Number of lines to start of drink data when parsing VPC tickets.", "paramType": "BYTE", "defaultValue": "3", "minValue": "0", "maxValue": "12", "category": "Ticket <PERSON>rser", "securityLevel": 1}}, {"id": 11518, "name": "PARSER_ITEMS_ETX", "value": "---------------------,SALE,SUBTOTAL,IPHONE,IPAD", "profileId": 134, "detail": {"id": 37, "name": "Ticket parser end of drinks/items delimeter", "macro": "PARSER_ITEMS_ETX", "description": "Marks the end of the drink data when parsing VPC tickets.", "paramType": "STRING", "defaultValue": "---------------------,SALE,SUBTOTAL,IPHONE,IPAD", "minValue": "", "maxValue": "", "category": "Ticket <PERSON>rser", "securityLevel": 1}}, {"id": 11519, "name": "PARSER_FIND_DATE", "value": "0", "profileId": 134, "detail": {"id": 38, "name": "Ticket parser find date", "macro": "PARSER_FIND_DATE", "description": "Look for ticket date on individual line when parsing VPC ticket data.", "paramType": "BOOLEAN", "defaultValue": "0", "minValue": "0", "maxValue": "1", "category": "Ticket <PERSON>rser", "securityLevel": 1}}, {"id": 11520, "name": "PARSER_DATE_FORMAT", "value": "%B %d, %Y %I:%M:%S %p", "profileId": 134, "detail": {"id": 39, "name": "Ticket parser date format", "macro": "PARSER_DATE_FORMAT", "description": "Ticket date/time format. Only valid if Find Date is enabled.", "paramType": "STRING", "defaultValue": "%B %d, %Y %I:%M:%S %p", "minValue": "", "maxValue": "", "category": "Ticket <PERSON>rser", "securityLevel": 1}}, {"id": 11521, "name": "PARSER_VOIDED", "value": "VOID,REPRINT", "profileId": 134, "detail": {"id": 40, "name": "Ticket parser voided marker", "macro": "PARSER_VOIDED", "description": "Voids the order if this marker is found when parsing VPC ticket data.", "paramType": "STRING", "defaultValue": "VOID,REPRINT", "minValue": "", "maxValue": "", "category": "Ticket <PERSON>rser", "securityLevel": 1}}, {"id": 11522, "name": "PARSER_CHANGED", "value": "CHANGED", "profileId": 134, "detail": {"id": 41, "name": "Ticket parser changed marker", "macro": "PARSER_CHANGED", "description": "Marks the order as a change ticket if this marker is found when parsing VPC ticket data.", "paramType": "STRING", "defaultValue": "CHANGED", "minValue": "", "maxValue": "", "category": "Ticket <PERSON>rser", "securityLevel": 1}}, {"id": 11523, "name": "PARSER_CURRENCY", "value": "S/,$", "profileId": 134, "detail": {"id": 42, "name": "Ticket parser currency marker", "macro": "PARSER_CURRENCY", "description": "Denotes the ticket currency, used to mark the end of VPC ticket data.", "paramType": "STRING", "defaultValue": "S/,$", "minValue": "", "maxValue": "", "category": "Ticket <PERSON>rser", "securityLevel": 1}}, {"id": 11524, "name": "TEXTRACT_LINE_OFFSET", "value": "0.03", "profileId": 134, "detail": {"id": 43, "name": "Textract Line Offset", "macro": "TEXTRACT_LINE_OFFSET", "description": "Line item x-axis offset when using Textract. Default is 0.03.", "paramType": "SINGLE", "defaultValue": "0.03", "minValue": "0", "maxValue": "2", "category": "Ticket <PERSON>rser", "securityLevel": 1}}, {"id": 11525, "name": "TEXTRACT_MOD_OFFSET", "value": "0.225", "profileId": 134, "detail": {"id": 44, "name": "Textract Modifier Offset", "macro": "TEXTRACT_MOD_OFFSET", "description": "Line item modifier x-axis offset when using Textract. Default is 0.225.", "paramType": "SINGLE", "defaultValue": "0.225", "minValue": "0", "maxValue": "2", "category": "Ticket <PERSON>rser", "securityLevel": 1}}, {"id": 11526, "name": "TEXTRACT_SEAT_OFFSET", "value": "0.5", "profileId": 134, "detail": {"id": 45, "name": "Textract Seat Offset", "macro": "TEXTRACT_SEAT_OFFSET", "description": "Seat x-axis offset when using Textract.", "paramType": "SINGLE", "defaultValue": "0.5", "minValue": "0", "maxValue": "2", "category": "Ticket <PERSON>rser", "securityLevel": 1}}, {"id": 11527, "name": "PARSER_STATES", "value": "ORDER,TABLE,ITEMS,FINISHED", "profileId": 134, "detail": {"id": 46, "name": "Ticket parser states", "macro": "PARSER_STATES", "description": "Order of state processor when parsing VPC tickets.", "paramType": "STRING", "defaultValue": "ORDER,TABLE,ITEMS,FINISHED", "minValue": "", "maxValue": "", "category": "Ticket <PERSON>rser", "securityLevel": 1}}, {"id": 11528, "name": "PARSER_QUANTITY", "value": " ", "profileId": 134, "detail": {"id": 47, "name": "Ticket parser quantity delimeter", "macro": "PARSER_QUANTITY", "description": "Line item quantity delimeter when parsing VPC tickets.", "paramType": "STRING", "defaultValue": " ", "minValue": "", "maxValue": "", "category": "Ticket <PERSON>rser", "securityLevel": 1}}, {"id": 11529, "name": "PARSER_DEBUG", "value": "0", "profileId": 134, "detail": {"id": 48, "name": "Ticket parser debug", "macro": "PARSER_DEBUG", "description": "Enable debug messages when parsing VPC ticket data.", "paramType": "BOOLEAN", "defaultValue": "0", "minValue": "0", "maxValue": "1", "category": "Ticket <PERSON>rser", "securityLevel": 1}}, {"id": 11530, "name": "DRINK_BUILDER_DEBUG", "value": "0", "profileId": 134, "detail": {"id": 49, "name": "Drink builder debug", "macro": "DRINK_BUILDER_DEBUG", "description": "Enable debug messages when building drinks.", "paramType": "BOOLEAN", "defaultValue": "0", "minValue": "0", "maxValue": "1", "category": "Drink Builder", "securityLevel": 1}}, {"id": 11531, "name": "CHECK_DRINK_TIMESTAMP", "value": "1", "profileId": 134, "detail": {"id": 50, "name": "Check Drink Timestamp", "macro": "CHECK_DRINK_TIMESTAMP", "description": "Do not make multiple drinks with the same timestamp.", "paramType": "BOOLEAN", "defaultValue": "1", "minValue": "0", "maxValue": "1", "category": "Orders/Tickets", "securityLevel": 1}}, {"id": 11532, "name": "MAX_SHAKER_VOLUME", "value": "12", "profileId": 134, "detail": {"id": 51, "name": "Combine Drink Threshold", "macro": "MAX_SHAKER_VOLUME", "description": "Maximum shaker volume to limit combined drinks.", "paramType": "SINGLE", "defaultValue": "12", "minValue": "0", "maxValue": "28", "category": "Drink Builder", "securityLevel": 2}}, {"id": 11533, "name": "DRINK_BUILDER_FUZZ_FACTOR", "value": "86", "profileId": 134, "detail": {"id": 52, "name": "Drink builder <PERSON><PERSON>", "macro": "DRINK_BUILDER_FUZZ_FACTOR", "description": "Fuzzy score above which names will be considered matches.", "paramType": "BYTE", "defaultValue": "86", "minValue": "0", "maxValue": "100", "category": "Drink Builder", "securityLevel": 1}}, {"id": 11534, "name": "ADMIN_PIN", "value": "1234", "profileId": 134, "detail": {"id": 53, "name": "Admin PIN", "macro": "ADMIN_PIN", "description": "Admin PIN code.", "paramType": "WORD", "defaultValue": "1234", "minValue": "0", "maxValue": "9999", "category": "Security", "securityLevel": 1}}, {"id": 11535, "name": "MANAGER_PIN", "value": "1234", "profileId": 134, "detail": {"id": 54, "name": "Manager <PERSON><PERSON>", "macro": "MANAGER_PIN", "description": "Manager PIN code.", "paramType": "WORD", "defaultValue": "1234", "minValue": "0", "maxValue": "9999", "category": "Security", "securityLevel": 2}}, {"id": 11536, "name": "CHECK_TICKET_DUPLICATION", "value": "1", "profileId": 134, "detail": {"id": 55, "name": "Duplicate ticket detection", "macro": "CHECK_TICKET_DUPLICATION", "description": "Do not make duplicate tickets based on ticket number and table number.", "paramType": "BOOLEAN", "defaultValue": "1", "minValue": "0", "maxValue": "1", "category": "Orders/Tickets", "securityLevel": 1}}, {"id": 11537, "name": "SYRUP_RATIO", "value": "0.25", "profileId": 134, "detail": {"id": 56, "name": "<PERSON><PERSON><PERSON>", "macro": "SYRUP_RATIO", "description": "Amount of syrup(oz) to dispense per ounce of drink volume.", "paramType": "SINGLE", "defaultValue": "0.25", "minValue": "0", "maxValue": "5", "category": "Coffee", "securityLevel": 2}}, {"id": 11538, "name": "MILK_RATIO", "value": "0.21", "profileId": 134, "detail": {"id": 57, "name": "Milk Ratio", "macro": "MILK_RATIO", "description": "Amount of milk(oz) to dispense per ounce of drink volume.", "paramType": "SINGLE", "defaultValue": "0.21", "minValue": "0", "maxValue": "5", "category": "Coffee", "securityLevel": 2}}, {"id": 11539, "name": "MAX_RECIPE_VOLUME_ADJUSTMENT", "value": "1", "profileId": 134, "detail": {"id": 58, "name": "Max. Recipe Volume Adjustment", "macro": "MAX_RECIPE_VOLUME_ADJUSTMENT", "description": "", "paramType": "SINGLE", "defaultValue": "1", "minValue": "0", "maxValue": "25", "category": "Drink Builder", "securityLevel": 1}}, {"id": 11540, "name": "LIGHT_MOD_MULTIPLIER", "value": "1.5", "profileId": 134, "detail": {"id": 59, "name": "Light Modifier Multiplier", "macro": "LIGHT_MOD_MULTIPLIER", "description": "Decreases ingredient volume when LIGHT is specified for a modifier.", "paramType": "SINGLE", "defaultValue": "1.5", "minValue": "0", "maxValue": "5", "category": "Coffee", "securityLevel": 1}}, {"id": 11541, "name": "DARK_MOD_MULTIPLIER", "value": "0.5", "profileId": 134, "detail": {"id": 60, "name": "Dark Modifier Multiplier", "macro": "DARK_MOD_MULTIPLIER", "description": "Decreases ingredient volume when DARK is specified for a modifier.", "paramType": "SINGLE", "defaultValue": "0.5", "minValue": "0", "maxValue": "5", "category": "Coffee", "securityLevel": 1}}, {"id": 11542, "name": "NO_ICE_ADJUSTMENT", "value": "2", "profileId": 134, "detail": {"id": 61, "name": "No Ice Adjustment", "macro": "NO_ICE_ADJUSTMENT", "description": "Increases drink ingredient volumes when NO ICE is specified.", "paramType": "SINGLE", "defaultValue": "2", "minValue": "0", "maxValue": "5", "category": "Ice", "securityLevel": 2}}, {"id": 11543, "name": "LITE_ICE_ADJUSTMENT", "value": "1.5", "profileId": 134, "detail": {"id": 62, "name": "Lite Ice Adjustment", "macro": "LITE_ICE_ADJUSTMENT", "description": "Increases drink ingredient volumes when LITE ICE is specified.", "paramType": "SINGLE", "defaultValue": "1.5", "minValue": "0", "maxValue": "5", "category": "Ice", "securityLevel": 2}}, {"id": 11544, "name": "EXTRA_CREAM_KEYWORD", "value": "extra cream", "profileId": 134, "detail": {"id": 63, "name": "Extra Creamer Keyword", "macro": "EXTRA_CREAM_KEYWORD", "description": "Drink modifier used to denote EXTRA creamer (more creamer should be added).", "paramType": "STRING", "defaultValue": "extra cream", "minValue": "", "maxValue": "", "category": "Coffee", "securityLevel": 1}}, {"id": 11545, "name": "LIGHT_CREAM_KEYWORD", "value": "light cream", "profileId": 134, "detail": {"id": 64, "name": "Light Creamer Keyword", "macro": "LIGHT_CREAM_KEYWORD", "description": "Drink modifier used to denote LIGHT creamer (less creamer should be used).", "paramType": "STRING", "defaultValue": "light cream", "minValue": "", "maxValue": "", "category": "Coffee", "securityLevel": 1}}, {"id": 11546, "name": "EXTRA_CREAM_MULTIPLIER", "value": "1.5", "profileId": 134, "detail": {"id": 65, "name": "Extra Creamer Multiplier", "macro": "EXTRA_CREAM_MULTIPLIER", "description": "Increases creamer volume when EXTRA_CREAM_KEYWORD is specified as a modifier.", "paramType": "SINGLE", "defaultValue": "1.5", "minValue": "0", "maxValue": "5", "category": "Coffee", "securityLevel": 1}}, {"id": 11547, "name": "LIGHT_CREAM_MULTIPLIER", "value": "0.5", "profileId": 134, "detail": {"id": 66, "name": "Light Creamer Multiplier", "macro": "LIGHT_CREAM_MULTIPLIER", "description": "Decreases creamer volume when LIGHT_CREAM_KEYWORD is specified as a modifier.", "paramType": "SINGLE", "defaultValue": "0.5", "minValue": "0", "maxValue": "5", "category": "Coffee", "securityLevel": 1}}, {"id": 11548, "name": "SMALL_CUP_KEYWORD", "value": "small", "profileId": 134, "detail": {"id": 67, "name": "Small Cup Keyword", "macro": "SMALL_CUP_KEYWORD", "description": "Drink modifier used to denote SMALL cup/volume size.", "paramType": "STRING", "defaultValue": "small", "minValue": "", "maxValue": "", "category": "<PERSON>zing", "securityLevel": 1}}, {"id": 11549, "name": "MEDIUM_CUP_KEYWORD", "value": "medium", "profileId": 134, "detail": {"id": 68, "name": "Medium Cup Keyword", "macro": "MEDIUM_CUP_KEYWORD", "description": "Drink modifier used to denote MEDIUM cup/volume size.", "paramType": "STRING", "defaultValue": "medium", "minValue": "", "maxValue": "", "category": "<PERSON>zing", "securityLevel": 1}}, {"id": 11550, "name": "LARGE_CUP_KEYWORD", "value": "large", "profileId": 134, "detail": {"id": 69, "name": "Large Cup Keyword", "macro": "LARGE_CUP_KEYWORD", "description": "Drink modifier used to denote LARGE cup/volume size.", "paramType": "STRING", "defaultValue": "large", "minValue": "", "maxValue": "", "category": "<PERSON>zing", "securityLevel": 1}}, {"id": 11551, "name": "SMALL_CUP_MULTIPLIER", "value": "1.0", "profileId": 134, "detail": {"id": 70, "name": "Small Cup Multiplier", "macro": "SMALL_CUP_MULTIPLIER", "description": "Recipe multiplier when pouring into a SMALL cup.", "paramType": "SINGLE", "defaultValue": "1.0", "minValue": "0", "maxValue": "5", "category": "<PERSON>zing", "securityLevel": 1}}, {"id": 11552, "name": "MEDIUM_CUP_MULTIPLIER", "value": "1.5", "profileId": 134, "detail": {"id": 71, "name": "Medium Cup Multiplier", "macro": "MEDIUM_CUP_MULTIPLIER", "description": "Recipe multiplier when pouring into a MEDIUM cup.", "paramType": "SINGLE", "defaultValue": "1.5", "minValue": "0", "maxValue": "5", "category": "<PERSON>zing", "securityLevel": 1}}, {"id": 11553, "name": "LARGE_CUP_MULTIPLIER", "value": "2.0", "profileId": 134, "detail": {"id": 72, "name": "Large Cup Multiplier", "macro": "LARGE_CUP_MULTIPLIER", "description": "Recipe multiplier when pouring into a LARGE cup.", "paramType": "SINGLE", "defaultValue": "2.0", "minValue": "0", "maxValue": "5", "category": "<PERSON>zing", "securityLevel": 1}}, {"id": 11554, "name": "NON_MAKE_LIST", "value": "", "profileId": 134, "detail": {"id": 73, "name": "Non-Makeable Ingredients", "macro": "NON_MAKE_LIST", "description": "List of ingredients/modifiers to discard drinks on", "paramType": "STRING", "defaultValue": "", "minValue": "", "maxValue": "", "category": "Drink Builder", "securityLevel": 1}}, {"id": 11555, "name": "SINGLE_CUP_KEYWORDS", "value": "Rocks,Short", "profileId": 134, "detail": {"id": 74, "name": "Single Cup Keywords", "macro": "SINGLE_CUP_KEYWORDS", "description": "Drink modifier used to denote SINGLE highball.", "paramType": "STRING", "defaultValue": "Rocks,Short", "minValue": "", "maxValue": "", "category": "Drink Builder - Custom", "securityLevel": 1}}, {"id": 11556, "name": "NO_ICE_KEYWORDS", "value": "No Ice", "profileId": 134, "detail": {"id": 75, "name": "No Ice Keywords", "macro": "NO_ICE_KEYWORDS", "description": "Drink modifier used to denote no ice. Will increase base/mixer volumes.", "paramType": "STRING", "defaultValue": "No Ice", "minValue": "", "maxValue": "", "category": "Ice", "securityLevel": 2}}, {"id": 11557, "name": "LITE_ICE_KEYWORDS", "value": "Lite Ice,Easy Ice", "profileId": 134, "detail": {"id": 76, "name": "Lite Ice Keywords", "macro": "LITE_ICE_KEYWORDS", "description": "Drink modifier used to denote lite ice. Will increase base/mixer volumes.", "paramType": "STRING", "defaultValue": "Lite Ice,Easy Ice", "minValue": "", "maxValue": "", "category": "Ice", "securityLevel": 2}}, {"id": 11558, "name": "NO_ICE_MULTIPLIER", "value": "0", "profileId": 134, "detail": {"id": 77, "name": "No Ice Multiplier", "macro": "NO_ICE_MULTIPLIER", "description": "Recipe multiplier for ice ingredient when no ice is requested.", "paramType": "SINGLE", "defaultValue": "0", "minValue": "0", "maxValue": "1", "category": "Ice", "securityLevel": 2}}, {"id": 11559, "name": "LITE_ICE_MULTIPLIER", "value": "0.5", "profileId": 134, "detail": {"id": 78, "name": "Lite Ice Multiplier", "macro": "LITE_ICE_MULTIPLIER", "description": "Recipe multiplier for ice ingredient when lite ice is requested.", "paramType": "SINGLE", "defaultValue": "0.5", "minValue": "0", "maxValue": "1", "category": "Ice", "securityLevel": 2}}, {"id": 11560, "name": "BASE_INGREDIENT_CATEGORIES", "value": "15, 16, 20", "profileId": 134, "detail": {"id": 79, "name": "Base Ingredient Categories", "macro": "BASE_INGREDIENT_CATEGORIES", "description": "List of ingredient categories used as drink bases.", "paramType": "STRING", "defaultValue": "15, 16, 20", "minValue": "", "maxValue": "", "category": "Drink Builder", "securityLevel": 2}}, {"id": 11561, "name": "RECIPES_FOR_GARNISH_LIST", "value": "tea,cold brew,chai,cocoa", "profileId": 134, "detail": {"id": 80, "name": "Recipes with Details", "macro": "RECIPES_FOR_GARNISH_LIST", "description": "List of recipes, by name, that require all modifiers added to the drink name. IE Cold Brew Whole Vanilla.", "paramType": "STRING", "defaultValue": "tea,cold brew,chai,cocoa", "minValue": "", "maxValue": "", "category": "Drink Builder", "securityLevel": 2}}, {"id": 11562, "name": "UPSELL_INGREDIENT_CATEGORIES", "value": "15, 16, 20", "profileId": 134, "detail": {"id": 81, "name": "Upsell Ingredient Categories", "macro": "UPSELL_INGREDIENT_CATEGORIES", "description": "List of ingredient categories allowed to swap. IE Well Tequila for Patron, or Skim Milk for Whole Milk.", "paramType": "STRING", "defaultValue": "15, 16, 20", "minValue": "", "maxValue": "", "category": "Drink Builder", "securityLevel": 2}}, {"id": 11563, "name": "MODIFIER_ZERO_KEYWORDS", "value": "No,None,Decaf", "profileId": 134, "detail": {"id": 82, "name": "Zero Keywords", "macro": "MODIFIER_ZERO_KEYWORDS", "description": "List of keywords used to specify ingredient removal.", "paramType": "STRING", "defaultValue": "No,None,Decaf", "minValue": "", "maxValue": "", "category": "Modifiers", "securityLevel": 2}}, {"id": 11564, "name": "MODIFIER_QUARTER_KEYWORDS", "value": "<PERSON><PERSON><PERSON>,Back", "profileId": 134, "detail": {"id": 83, "name": "Quarter Keywords", "macro": "MODIFIER_QUARTER_KEYWORDS", "description": "List of keywords used to specify ingredient reduction.", "paramType": "STRING", "defaultValue": "<PERSON><PERSON><PERSON>,Back", "minValue": "", "maxValue": "", "category": "Modifiers", "securityLevel": 2}}, {"id": 11565, "name": "MODIFIER_HALF_KEYWORDS", "value": "Light,Dark", "profileId": 134, "detail": {"id": 84, "name": "Half Keywords", "macro": "MODIFIER_HALF_KEYWORDS", "description": "List of keywords used to specify ingredient reduction.", "paramType": "STRING", "defaultValue": "Light,Dark", "minValue": "", "maxValue": "", "category": "Modifiers", "securityLevel": 2}}, {"id": 11566, "name": "MODIFIER_EXTRA_KEYWORDS", "value": "Extra", "profileId": 134, "detail": {"id": 85, "name": "Extra Keywords", "macro": "MODIFIER_EXTRA_KEYWORDS", "description": "List of keywords used to specify ingredient addition.", "paramType": "STRING", "defaultValue": "Extra", "minValue": "", "maxValue": "", "category": "Modifiers", "securityLevel": 2}}, {"id": 11567, "name": "MODIFIER_DOUBLE_KEYWORDS", "value": "Double,Dbl", "profileId": 134, "detail": {"id": 86, "name": "Double Keywords", "macro": "MODIFIER_DOUBLE_KEYWORDS", "description": "List of keywords used to specify ingredient addition.", "paramType": "STRING", "defaultValue": "Double,Dbl", "minValue": "", "maxValue": "", "category": "Modifiers", "securityLevel": 2}}, {"id": 11568, "name": "MODIFIER_IGNORED_KEYWORDS", "value": "Regular", "profileId": 134, "detail": {"id": 87, "name": "Ignored Keywords", "macro": "MODIFIER_IGNORED_KEYWORDS", "description": "List of keywords that should be disregarded as drink modifiers.", "paramType": "STRING", "defaultValue": "Regular", "minValue": "", "maxValue": "", "category": "Modifiers", "securityLevel": 2}}, {"id": 11569, "name": "MODIFIER_ZERO_MULTIPLIER", "value": "0", "profileId": 134, "detail": {"id": 88, "name": "Zero Multiplier", "macro": "MODIFIER_ZERO_MULTIPLIER", "description": "Factor applied to ingredients classified with Zero Keywords.", "paramType": "SINGLE", "defaultValue": "0", "minValue": "0", "maxValue": "1", "category": "Modifiers", "securityLevel": 2}}, {"id": 11570, "name": "MODIFIER_QUARTER_MULTIPLIER", "value": "0.25", "profileId": 134, "detail": {"id": 89, "name": "Quarter Multiplier", "macro": "MODIFIER_QUARTER_MULTIPLIER", "description": "Factor applied to ingredients classified with Quarter Keywords.", "paramType": "SINGLE", "defaultValue": "0.25", "minValue": "0", "maxValue": "1", "category": "Modifiers", "securityLevel": 2}}, {"id": 11571, "name": "MODIFIER_HALF_MULTIPLIER", "value": "0.5", "profileId": 134, "detail": {"id": 90, "name": "Half Multiplier", "macro": "MODIFIER_HALF_MULTIPLIER", "description": "Factor applied to ingredients classified with Half Keywords.", "paramType": "SINGLE", "defaultValue": "0.5", "minValue": "0", "maxValue": "1", "category": "Modifiers", "securityLevel": 2}}, {"id": 11572, "name": "MODIFIER_EXTRA_MULTIPLIER", "value": "1.5", "profileId": 134, "detail": {"id": 91, "name": "Extra Multiplier", "macro": "MODIFIER_EXTRA_MULTIPLIER", "description": "Factor applied to ingredients classified with Extra Keywords.", "paramType": "SINGLE", "defaultValue": "1.5", "minValue": "1", "maxValue": "2", "category": "Modifiers", "securityLevel": 2}}, {"id": 11573, "name": "MODIFIER_DOUBLE_MULTIPLIER", "value": "2", "profileId": 134, "detail": {"id": 92, "name": "Double Multiplier", "macro": "MODIFIER_DOUBLE_MULTIPLIER", "description": "Factor applied to ingredients classified with Double Keywords.", "paramType": "SINGLE", "defaultValue": "2", "minValue": "1", "maxValue": "2", "category": "Modifiers", "securityLevel": 2}}, {"id": 11574, "name": "ICE_ADJUTS_PROPORTION", "value": "0", "profileId": 134, "detail": {"id": 93, "name": "Ice Adjustment Proportion", "macro": "ICE_ADJUTS_PROPORTION", "description": "Indicates if the ice adjustment must use proportions.", "paramType": "SINGLE", "defaultValue": "0", "minValue": "0", "maxValue": "1", "category": "Ice", "securityLevel": 1}}, {"id": 11575, "name": "NO_ICE_ADDITION_ADJUSTMENT", "value": "0", "profileId": 134, "detail": {"id": 94, "name": "No Ice Addition Adjustment", "macro": "NO_ICE_ADDITION_ADJUSTMENT", "description": "Increases the max drink volume when no ice is specified.", "paramType": "SINGLE", "defaultValue": "0", "minValue": "0", "maxValue": "2", "category": "Ice", "securityLevel": 1}}, {"id": 11576, "name": "NO_ICE_ADJUSTMENT_PROPORTION", "value": "0", "profileId": 134, "detail": {"id": 95, "name": "No Ice Adjustment Proportion", "macro": "NO_ICE_ADJUSTMENT_PROPORTION", "description": "Factor that multiplies the proportion when no ice is specified.", "paramType": "SINGLE", "defaultValue": "1", "minValue": "0", "maxValue": "2", "category": "Ice", "securityLevel": 1}}, {"id": 11577, "name": "LITE_ICE_ADDITION_ADJUSTMENT", "value": "0", "profileId": 134, "detail": {"id": 96, "name": "Lite Ice Addition Adjustment", "macro": "LITE_ICE_ADDITION_ADJUSTMENT", "description": "Increases the max drink volume when lite ice is specified.", "paramType": "SINGLE", "defaultValue": "0", "minValue": "0", "maxValue": "1", "category": "Ice", "securityLevel": 1}}, {"id": 11578, "name": "LITE_ICE_ADJUSTMENT_PROPORTION", "value": "1", "profileId": 134, "detail": {"id": 97, "name": "Lite Ice Adjustment Proportion", "macro": "LITE_ICE_ADJUSTMENT_PROPORTION", "description": "Factor that multiplies the proportion when lite ice is specified.", "paramType": "SINGLE", "defaultValue": "1", "minValue": "0", "maxValue": "1", "category": "Ice", "securityLevel": 1}}, {"id": 11579, "name": "RECIPES_IGNORE_ICE_UPDATE", "value": "", "profileId": 134, "detail": {"id": 98, "name": "Recipes Ignore Ice Update", "macro": "RECIPES_IGNORE_ICE_UPDATE", "description": "List of recipes by name that will skip ingredients quantity update for ice adjustment.", "paramType": "STRING", "defaultValue": "", "minValue": "", "maxValue": "", "category": "Ice", "securityLevel": 1}}, {"id": 11580, "name": "RECIPES_UPSCALE_COFFEE_INGREDIENTS", "value": "", "profileId": 134, "detail": {"id": 99, "name": "Recipes Upscale Coffe Ingredients", "macro": "RECIPES_UPSCALE_COFFEE_INGREDIENTS", "description": "List of recipes by name that must upscale coffee ingredients after process drink size.", "paramType": "STRING", "defaultValue": "", "minValue": "", "maxValue": "", "category": "Coffee", "securityLevel": 1}}, {"id": 11581, "name": "LIMIT_ADD_ONS_ONLY", "value": "1", "profileId": 134, "detail": {"id": 100, "name": "Apply Limits to Add Ons only", "macro": "LIMIT_ADD_ONS_ONLY", "description": "Indicates if ingredient limits are applied to Add Ons only.", "paramType": "SINGLE", "defaultValue": "1", "minValue": "0", "maxValue": "1", "category": "Coffee", "securityLevel": 1}}, {"id": 11582, "name": "SKIP_CREAMER_CAP_KEYWORDS", "value": "", "profileId": 134, "detail": {"id": 101, "name": "Skip Creamer Cap Keywords", "macro": "SKIP_CREAMER_CAP_KEYWORDS", "description": "List of keywords to skip creamer cap.", "paramType": "STRING", "defaultValue": "", "minValue": "", "maxValue": "", "category": "Coffee", "securityLevel": 1}}, {"id": 11583, "name": "REDUCE_REFRESHER_WITH_LEMONADE", "value": "1", "profileId": 134, "detail": {"id": 102, "name": "Reduce Refreshers with Lemonade", "macro": "REDUCE_REFRESHER_WITH_LEMONADE", "description": "Indicates if Refreshers must be reduced if Lemonade in modifiers list.", "paramType": "SINGLE", "defaultValue": "1", "minValue": "0", "maxValue": "1", "category": "Coffee", "securityLevel": 1}}, {"id": 11584, "name": "REDUCE_TEA_WITH_LEMONADE", "value": "1", "profileId": 134, "detail": {"id": 103, "name": "Reduce Teas with Lemonade", "macro": "REDUCE_TEA_WITH_LEMONADE", "description": "Indicates if Teas must be reduced if Lemonade in modifiers list.", "paramType": "SINGLE", "defaultValue": "1", "minValue": "0", "maxValue": "1", "category": "Coffee", "securityLevel": 1}}, {"id": 11585, "name": "USE_INGREDIENTS_NOT_IN_SLOTS", "value": "1", "profileId": 134, "detail": {"id": 104, "name": "Use Ingredients not in Slots", "macro": "USE_INGREDIENTS_NOT_IN_SLOTS", "description": "Indicates if Ingredients not in slots can be used.", "paramType": "SINGLE", "defaultValue": "1", "minValue": "0", "maxValue": "1", "category": "Coffee", "securityLevel": 1}}, {"id": 11586, "name": "USE_MULTIPLIER_FOR_EXISTING_INGRDTS", "value": "0", "profileId": 134, "detail": {"id": 105, "name": "Use quantity as multiplier for existing ingredients (*=)", "macro": "USE_MULTIPLIER_FOR_EXISTING_INGRDTS", "description": "Indicates if the quantity must be used as a multiplier for existing ingredients.", "paramType": "SINGLE", "defaultValue": "0", "minValue": "0", "maxValue": "1", "category": "Coffee", "securityLevel": 1}}, {"id": 11587, "name": "SKIP_UPDATE_BASE_INGREDIENT_VOL", "value": "0", "profileId": 134, "detail": {"id": 106, "name": "Skip updating Base Ingredient Volume", "macro": "SKIP_UPDATE_BASE_INGREDIENT_VOL", "description": "Indicates if the step to update the Base Ingredient volume is skipped.", "paramType": "SINGLE", "defaultValue": "0", "minValue": "0", "maxValue": "1", "category": "Coffee", "securityLevel": 1}}, {"id": 11588, "name": "SKIP_UPDATE_RECIPE_UPSELLS", "value": "0", "profileId": 134, "detail": {"id": 107, "name": "Skip updating <PERSON><PERSON><PERSON>", "macro": "SKIP_UPDATE_RECIPE_UPSELLS", "description": "Indicates if the step to update <PERSON><PERSON><PERSON> is skipped.", "paramType": "SINGLE", "defaultValue": "0", "minValue": "0", "maxValue": "1", "category": "Coffee", "securityLevel": 1}}, {"id": 11589, "name": "SKIP_LIMIT_BY_INGREDIENT_CAT", "value": "0", "profileId": 134, "detail": {"id": 108, "name": "Skip limit items by Ingredient Category", "macro": "SKIP_LIMIT_BY_INGREDIENT_CAT", "description": "Indicates if the step to limit items bu Ingredient Category is skipped.", "paramType": "SINGLE", "defaultValue": "0", "minValue": "0", "maxValue": "1", "category": "Coffee", "securityLevel": 1}}, {"id": 11590, "name": "SKIP_UPDATE_MULTI_SLOTS", "value": "0", "profileId": 134, "detail": {"id": 109, "name": "Skip updating Multi Slots", "macro": "SKIP_UPDATE_MULTI_SLOTS", "description": "Indicates if the step to update the recipe using Multi Slots is skipped.", "paramType": "SINGLE", "defaultValue": "0", "minValue": "0", "maxValue": "1", "category": "Coffee", "securityLevel": 1}}, {"id": 11591, "name": "PARSER_ALLOW_NO_QTY_ITEMS", "value": "0", "profileId": 134, "detail": {"id": 110, "name": "Ticket Parser allows items with no numeric quantity", "macro": "PARSER_ALLOW_NO_QTY_ITEMS", "description": "Indicates if the ticket has items that do not start with a numeric quantity.", "paramType": "SINGLE", "defaultValue": "0", "minValue": "0", "maxValue": "1", "category": "Ticket <PERSON>rser", "securityLevel": 1}}, {"id": 11592, "name": "PARSER_ALLOW_CONSECUTIVE_ITEMS", "value": "0", "profileId": 134, "detail": {"id": 111, "name": "Ticket Parser allows consecutive items", "macro": "PARSER_ALLOW_CONSECUTIVE_ITEMS", "description": "Indicates if the ticket has consecutive items with no lines between them.", "paramType": "SINGLE", "defaultValue": "0", "minValue": "0", "maxValue": "1", "category": "Ticket <PERSON>rser", "securityLevel": 1}}, {"id": 11593, "name": "PARSER_ITEM_TOKENS", "value": "0", "profileId": 134, "detail": {"id": 112, "name": "Ticket Parser, list of tokens that identify and force parsing a line as an item", "macro": "PARSER_ITEM_TOKENS", "description": "List of specific tokens that help identify an item in the ticket.", "paramType": "STRING", "defaultValue": "0", "minValue": "", "maxValue": "", "category": "Ticket <PERSON>rser", "securityLevel": 1}}, {"id": 11594, "name": "PARSER_MODIFIER_TOKENS", "value": "", "profileId": 134, "detail": {"id": 113, "name": "Ticket Parser, list of tokens that identify and force parsing a line as a modifier", "macro": "PARSER_MODIFIER_TOKENS", "description": "List of specific tokens that help identify a modifier in the ticket.", "paramType": "STRING", "defaultValue": "", "minValue": "", "maxValue": "", "category": "Ticket <PERSON>rser", "securityLevel": 1}}, {"id": 11595, "name": "PARSER_ITEM_DELIMITER", "value": "", "profileId": 134, "detail": {"id": 114, "name": "Ticket Parser, list of tokens that delimit an item name from not necessary text", "macro": "PARSER_ITEM_DELIMITER", "description": "List of tokens that delimit an item name from not necessary text.", "paramType": "STRING", "defaultValue": "", "minValue": "", "maxValue": "", "category": "Ticket <PERSON>rser", "securityLevel": 1}}, {"id": 11596, "name": "TOP_UP_ONLY_KEY", "value": "0", "profileId": 134, "detail": {"id": 115, "name": "Top Up Only Mode", "macro": "TOP_UP_ONLY_KEY", "description": "When enabled, replacing house mixes will only do top ups, not full replacements (backtracking and re-priming)", "paramType": "BOOLEAN", "defaultValue": "0", "minValue": "0", "maxValue": "1", "category": "App - Ordering", "securityLevel": 1}}, {"id": 11597, "name": "QUICK_REPLACE_UI_KEY", "value": "0", "profileId": 134, "detail": {"id": 116, "name": "Quick Replace UI", "macro": "QUICK_REPLACE_UI_KEY", "description": "When enabled, on replacing an ingredient, it will first have you enter in the amount you are replacing with, then do backtrack, jostle, prime in the background", "paramType": "BOOLEAN", "defaultValue": "0", "minValue": "0", "maxValue": "1", "category": "App - UI", "securityLevel": 1}}, {"id": 11598, "name": "NON_ALCOHOLIC_UI_KEY", "value": "0", "profileId": 134, "detail": {"id": 117, "name": "Non Alcoholic UI", "macro": "NON_ALCOHOLIC_UI_KEY", "description": "When enabled, alcohol specific terminology will be replaced with generic terminology", "paramType": "BOOLEAN", "defaultValue": "0", "minValue": "0", "maxValue": "1", "category": "App - UI", "securityLevel": 1}}, {"id": 11599, "name": "TEST_POUR_ING_KEY", "value": "0", "profileId": 134, "detail": {"id": 118, "name": "Weekly Ingredient Test Pours", "macro": "TEST_POUR_ING_KEY", "description": "When enabled, every weekly shutdown procedure will trigger a test pour of ingredients", "paramType": "BOOLEAN", "defaultValue": "0", "minValue": "0", "maxValue": "1", "category": "App - Maintenance", "securityLevel": 1}}, {"id": 11600, "name": "LITE_ENABLED_KEY", "value": "1", "profileId": 134, "detail": {"id": 119, "name": "Lite Mode Enabled", "macro": "LITE_ENABLED_KEY", "description": "Enable Lite mode", "paramType": "BOOLEAN", "defaultValue": "1", "minValue": "0", "maxValue": "1", "category": "App - UI", "securityLevel": 1}}, {"id": 11601, "name": "PRIME_FIRST_SHUTDOWN_KEY", "value": "0", "profileId": 134, "detail": {"id": 120, "name": "Prime First Shutdown", "macro": "PRIME_FIRST_SHUTDOWN_KEY", "description": "When enabled, before backtracking the ingredients on shutdown, the machine will first prime the ingredients", "paramType": "BOOLEAN", "defaultValue": "0", "minValue": "0", "maxValue": "1", "category": "App - Maintenance", "securityLevel": 1}}, {"id": 11602, "name": "NO_POS_UI_ENABLED_KEY", "value": "1", "profileId": 134, "detail": {"id": 121, "name": "Non POS Mode", "macro": "NO_POS_UI_ENABLED_KEY", "description": "When enabled, you can order straight off the app like a soda fountain", "paramType": "BOOLEAN", "defaultValue": "0", "minValue": "0", "maxValue": "1", "category": "App - UI", "securityLevel": 1}}, {"id": 11603, "name": "SELF_SERVE_UI_ENABLED_KEY", "value": "0", "profileId": 134, "detail": {"id": 122, "name": "Self Serve Mode", "macro": "SELF_SERVE_UI_ENABLED_KEY", "description": "When enabled, you can order/pay from the mobile phone app, and use PIN codes to dispense", "paramType": "BOOLEAN", "defaultValue": "0", "minValue": "0", "maxValue": "1", "category": "App - UI", "securityLevel": 1}}, {"id": 11604, "name": "MAX_DRINKS_IN_ORDER_KEY", "value": "6", "profileId": 134, "detail": {"id": 123, "name": "Max Drinks in Order", "macro": "MAX_DRINKS_IN_ORDER_KEY", "description": "How many drinks can be in an order at once. 1-6 drinks allowed.", "paramType": "WORD", "defaultValue": "6", "minValue": "1", "maxValue": "99", "category": "App - Ordering", "securityLevel": 1}}, {"id": 11605, "name": "REFRESH_CONFIRMATION_DELAY_KEY", "value": "0", "profileId": 134, "detail": {"id": 124, "name": "<PERSON>f<PERSON> Allowed Delay", "macro": "REFRESH_CONFIRMATION_DELAY_KEY", "description": "How long Refresh Al<PERSON>s wait for confirmation. 0 = no delay. Max 600 seconds.", "paramType": "WORD", "defaultValue": "0", "minValue": "0", "maxValue": "600", "category": "App - Ordering", "securityLevel": 1}}, {"id": 11606, "name": "HIDE_PERCENTAGE_ENABLED_KEY", "value": "0", "profileId": 134, "detail": {"id": 125, "name": "Hide Percentage Enabled", "macro": "HIDE_PERCENTAGE_ENABLED_KEY", "description": "When enabled, Ingredient percentages will be hidden from the UI", "paramType": "BOOLEAN", "defaultValue": "0", "minValue": "0", "maxValue": "1", "category": "App - UI", "securityLevel": 1}}, {"id": 11607, "name": "OVERRIDE_BACKBAR_LITE_ENABLED_KEY", "value": "0", "profileId": 134, "detail": {"id": 126, "name": "Lite Mode Override", "macro": "OVERRIDE_BACKBAR_LITE_ENABLED_KEY", "description": "When enabled, the machine will use the Backbar Lite workflow regardless of machine configuration", "paramType": "BOOLEAN", "defaultValue": "0", "minValue": "0", "maxValue": "1", "category": "App - UI", "securityLevel": 1}}, {"id": 11608, "name": "SYNCHRONIZED_DISPENSING_ENABLED_KEY", "value": "1", "profileId": 134, "detail": {"id": 129, "name": "Synchronized Dispensing", "macro": "SYNCHRONIZED_DISPENSING_ENABLED_KEY", "description": "When enabled, each ingredient in a drink will dispense with normalized speeds", "paramType": "BOOLEAN", "defaultValue": "1", "minValue": "0", "maxValue": "1", "category": "App Settings", "securityLevel": 1}}, {"id": 11609, "name": "LOW_TEMP_NOTIFICATION_THRESHOLD_KEY", "value": "34", "profileId": 134, "detail": {"id": 130, "name": "Low Temperature Notification Threshold", "macro": "LOW_TEMP_NOTIFICATION_THRESHOLD_KEY", "description": "If average fridge temp is below this value for 1 hour, a notification will be sent", "paramType": "SINGLE", "defaultValue": "34", "minValue": "0", "maxValue": "100", "category": "App - Notifications", "securityLevel": 1}}, {"id": 11610, "name": "HIGH_TEMP_NOTIFICATION_THRESHOLD_KEY", "value": "46", "profileId": 134, "detail": {"id": 131, "name": "High Temperature Notification Threshold", "macro": "HIGH_TEMP_NOTIFICATION_THRESHOLD_KEY", "description": "If average fridge temp is above this value for 1 hour, a notification will be sent", "paramType": "SINGLE", "defaultValue": "46", "minValue": "0", "maxValue": "100", "category": "App - Notifications", "securityLevel": 1}}, {"id": 11611, "name": "SHOW_UNREFRIGERATED_INGREDIENTS_KEY", "value": "0", "profileId": 134, "detail": {"id": 132, "name": "Show unrefrigerated ingredients", "macro": "SHOW_UNREFRIGERATED_INGREDIENTS_KEY", "description": "When enabled, a thumbs up/thumbs down feedback will be shown after each ingredient runout", "paramType": "BOOLEAN", "defaultValue": "0", "minValue": "0", "maxValue": "1", "category": "App - Notifications", "securityLevel": 1}}, {"id": 11612, "name": "SHOW_FEEDBACK_AFTER_DISPENSE_KEY", "value": "1", "profileId": 134, "detail": {"id": 133, "name": "Show Feedback After Dispense", "macro": "SHOW_FEEDBACK_AFTER_DISPENSE_KEY", "description": "When enabled, a thumbs up/down dialog will appear after each dispense", "paramType": "BOOLEAN", "defaultValue": "1", "minValue": "0", "maxValue": "1", "category": "App - Notifications", "securityLevel": 1}}, {"id": 11613, "name": "SHOW_FEEDBACK_AFTER_INGREDIENT_RUNOUT_KEY", "value": "0", "profileId": 134, "detail": {"id": 134, "name": "Show Feedback after Ingredient Runouts", "macro": "SHOW_FEEDBACK_AFTER_INGREDIENT_RUNOUT_KEY", "description": "When enabled, a thumbs up/thumbs down feedback will be shown after each ingredient runout", "paramType": "BOOLEAN", "defaultValue": "0", "minValue": "0", "maxValue": "1", "category": "App - Notifications", "securityLevel": 1}}, {"id": 11614, "name": "DRINK_AUDIT_KEY", "value": "1", "profileId": 134, "detail": {"id": 135, "name": "Enable Drink Audit Feature", "macro": "DRINK_AUDIT_KEY", "description": "When enabled, a help icon appears on drinks to dispense by each ingredient", "paramType": "BOOLEAN", "defaultValue": "1", "minValue": "0", "maxValue": "1", "category": "App - UI", "securityLevel": 1}}, {"id": 11615, "name": "CHECK_NOZZLE_CAP_KEY", "value": "1", "profileId": 134, "detail": {"id": 136, "name": "Check Nozzle Cap Before Dispense", "macro": "CHECK_NOZZLE_CAP_KEY", "description": "When enabled, a warning dialog will appear before drink dispense if the nozzle cap is detected.", "paramType": "BOOLEAN", "defaultValue": "1", "minValue": "0", "maxValue": "1", "category": "App - Notifications", "securityLevel": 1}}, {"id": 11616, "name": "COLD_BREW_RUNOUT_ALERT_KEY", "value": "0", "profileId": 134, "detail": {"id": 137, "name": "Enable Cold Brew Runout <PERSON>s", "macro": "COLD_BREW_RUNOUT_ALERT_KEY", "description": "When enabled, all Cold Brew Runout alerts will be displayed. When disabled, alerts will be hidden.", "paramType": "BOOLEAN", "defaultValue": "0", "minValue": "0", "maxValue": "1", "category": "App - Notifications", "securityLevel": 1}}, {"id": 11617, "name": "NOZZLE_CAP_ALERT_KEY", "value": "1", "profileId": 134, "detail": {"id": 138, "name": "Enable Nozzle Cap Alerts", "macro": "NOZZLE_CAP_ALERT_KEY", "description": "When enabled, all nozzle cap alerts will be displayed. When disabled, alerts will be hidden.", "paramType": "BOOLEAN", "defaultValue": "1", "minValue": "0", "maxValue": "1", "category": "App - Notifications", "securityLevel": 1}}, {"id": 11618, "name": "CONCENTRATED_CLEANING_KEY", "value": "1", "profileId": 134, "detail": {"id": 139, "name": "Use Concentrated Cleaner", "macro": "CONCENTRATED_CLEANING_KEY", "description": "When enabled, the self cleaning procedure uses the inline cleaner concentrate", "paramType": "BOOLEAN", "defaultValue": "1", "minValue": "0", "maxValue": "1", "category": "App - Maintenance", "securityLevel": 1}}, {"id": 11619, "name": "SYNC_SETTINGS_TO_FIRMWARE_KEY", "value": "1", "profileId": 134, "detail": {"id": 140, "name": "Sync EEPROM to Firmware", "macro": "SYNC_SETTINGS_TO_FIRMWARE_KEY", "description": "When enabled, the EEPROM settings from Admin will automatically be written to firmware", "paramType": "BOOLEAN", "defaultValue": "1", "minValue": "0", "maxValue": "1", "category": "App - Maintenance", "securityLevel": 1}}, {"id": 11620, "name": "CLEANING_OPRY_KEY", "value": "0", "profileId": 134, "detail": {"id": 141, "name": "GC63 Opry Mills", "macro": "CLEANING_OPRY_KEY", "description": "Should be enabled at GC63 Opry Mills so that they use the bucket with internal cleaner", "paramType": "BOOLEAN", "defaultValue": "0", "minValue": "0", "maxValue": "1", "category": "App - Maintenance", "securityLevel": 1}}, {"id": 11621, "name": "DILUTED_CLEANER_KEY", "value": "0", "profileId": 134, "detail": {"id": 142, "name": "Diluted Cleaner with Stepper Motor", "macro": "DILUTED_CLEANER_KEY", "description": "Diluted Cleaner with Stepper Motor", "paramType": "BOOLEAN", "defaultValue": "1", "minValue": "0", "maxValue": "1", "category": "App - Maintenance", "securityLevel": 1}}, {"id": 17163, "name": "COOLING_SYSTEM_SETTINGS_VERSION", "value": "2.1.0", "profileId": 134, "detail": {"id": 152, "name": "Cooling System Settings Version", "macro": "COOLING_SYSTEM_SETTINGS_VERSION", "description": "Version Number for the Cooling Subsystem settings", "paramType": "STRING", "defaultValue": "2.1.0", "minValue": "", "maxValue": "", "category": "Versions", "securityLevel": 1}}, {"id": 17164, "name": "LOWER_MODEL_SETTINGS_VERSION", "value": "2.1.0", "profileId": 134, "detail": {"id": 145, "name": "Lower Model Settings Version", "macro": "LOWER_MODEL_SETTINGS_VERSION", "description": "Version Number for the Lower Model settings", "paramType": "STRING", "defaultValue": "2.1.0", "minValue": "", "maxValue": "", "category": "Versions", "securityLevel": 1}}]