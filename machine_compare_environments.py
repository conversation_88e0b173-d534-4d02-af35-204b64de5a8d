#!/usr/bin/env python3
"""
Machine Environment Comparison Tool

This script compares machine settings, boards, and profile values between stage and prod environments.
It identifies:
1. Settings that exist in one environment but not the other
2. Settings that have different properties (currentValue, defaultParam, etc.)
3. Boards that have different properties (status, application versions, etc.)
4. Profile values that have different values
"""

import sys
import os
import json
import requests
from typing import Dict, List, Tuple, Set, Optional
from dataclasses import dataclass
from enum import Enum
from dotenv import load_dotenv
import argparse
from datetime import datetime

# Load environment variables from .env file
load_dotenv()

class ComparisonResult(Enum):
    IDENTICAL = "identical"
    DIFFERENT = "different"
    MISSING_IN_STAGE = "missing_in_stage"
    MISSING_IN_PROD = "missing_in_prod"

@dataclass
class ItemDifference:
    machine_name: str
    machine_id_stage: int
    machine_id_prod: int
    item_type: str  # "settings", "board", "profile_value"
    item_identifier: str  # macro for settings/profile_values, protocolId for boards
    result: ComparisonResult
    stage_item: Dict = None
    prod_item: Dict = None
    differences: List[str] = None

class MachineManager:
    def __init__(self, api_key: str = None, api_endpoint: str = None, auth_token: str = None):
        self.api_key = api_key or os.getenv("API_KEY")
        self.api_endpoint = api_endpoint or os.getenv("API_ENDPOINT")
        self.auth_token = auth_token
        
        if not self.api_key:
            raise ValueError("API key is required. Set it in .env file or pass as argument.")
        
        if not self.api_endpoint:
            raise ValueError("API endpoint is required. Set it in .env file or pass as argument.")
        
        # Remove trailing slash if present
        if self.api_endpoint.endswith('/'):
            self.api_endpoint = self.api_endpoint[:-1]
        
        self.headers = {
            "x-api-key": self.api_key,
            "Content-Type": "application/json"
        }
        
        # Add Authorization header if auth token is provided (without Bearer prefix)
        if self.auth_token:
            self.headers["Authorization"] = self.auth_token
    
    def _make_request(self, method: str, path: str, data: Dict = None) -> Dict:
        """Make a request to the API"""
        url = f"{self.api_endpoint}{path}"
        
        try:
            if method.upper() == "GET":
                response = requests.get(url, headers=self.headers)
            elif method.upper() == "POST":
                response = requests.post(url, headers=self.headers, json=data)
            elif method.upper() == "PUT":
                response = requests.put(url, headers=self.headers, json=data)
            elif method.upper() == "DELETE":
                response = requests.delete(url, headers=self.headers)
            else:
                raise ValueError(f"Unsupported HTTP method: {method}")
            
            response.raise_for_status()
            
            if response.status_code == 204:  # No content
                return {"success": True}
            
            return response.json()
        except requests.exceptions.RequestException as e:
            print(f"Error making request to {url}: {e}")
            if hasattr(e, 'response') and e.response is not None:
                try:
                    error_data = e.response.json()
                    print(f"API Error: {error_data}")
                except:
                    print(f"API Error: {e.response.text}")
            raise e
    
    def list_machines(self) -> List[Dict]:
        """List all machines"""
        return self._make_request("GET", "/machine")
    
    def get_machine_settings(self, machine_id: int) -> List[Dict]:
        """Get settings for a specific machine"""
        return self._make_request("GET", f"/settings?machineId={machine_id}")
    
    def get_machine_boards(self, machine_id: int) -> List[Dict]:
        """Get boards for a specific machine"""
        return self._make_request("GET", f"/board?machineId={machine_id}")
    
    profile_cache = None
    def get_machine_profiles(self, machine_id: int) -> List[Dict]:
        """Get profiles for a specific machine"""
        if self.profile_cache is not None:
            # Use cached profiles if available
            return [p for p in self.profile_cache if p.get('id') == machine_id]
        all_profiles = self._make_request("GET", "/profile")
        self.profile_cache = all_profiles  # Cache the profiles for future use
        return [p for p in all_profiles if p.get('id') == machine_id]

        # try:
        #     # Try the machine-specific endpoint first
        #     return self._make_request("GET", f"/profile?machineId={machine_id}")
        # except:
        #     # If that fails, get all profiles and filter
        #     if self.profile_cache is not None:
        #         # Use cached profiles if available
        #         return [p for p in self.profile_cache if p.get('machineId') == machine_id]
        #     all_profiles = self._make_request("GET", "/profile")
        #     self.profile_cache = all_profiles  # Cache the profiles for future use
        #     return [p for p in all_profiles if p.get('machineId') == machine_id]
    
    def get_profile_values(self, profile_id: int) -> List[Dict]:
        """Get profile values for a specific profile"""
        return self._make_request("GET", f"/profilevalue?profileId={profile_id}")
    
    def get_machine_profile_values(self, machine_id: int) -> List[Dict]:
        """Get all profile values for a machine (across all profiles)"""
        profiles = self.get_machine_profiles(machine_id)
        all_profile_values = []
        
        for profile in profiles:
            profile_values = self.get_profile_values(profile['id'])
            # Add profile info to each profile value for context
            for pv in profile_values:
                pv['profile_name'] = profile.get('name', 'Unknown')
                pv['profile_id'] = profile['id']
            all_profile_values.extend(profile_values)
        
        return all_profile_values
    
    def get_machine_ingredient_slots(self, machine_id: int) -> List[Dict]:
        """Get ingredient slots for a specific machine"""
        return self._make_request("GET", f"/ingredientslot?machineId={machine_id}")

class MachineEnvironmentComparer:
    def __init__(self, stage_api_key: str = None, prod_api_key: str = None, 
                 stage_env: str = "stage", prod_env: str = "prod",
                 stage_auth_token: str = None, prod_auth_token: str = None):
        """
        Initialize the comparer with API credentials for both environments
        
        Args:
            stage_api_key: API key for stage environment
            prod_api_key: API key for prod environment  
            stage_env: Environment name for stage (used to construct endpoint)
            prod_env: Environment name for prod (used to construct endpoint)
            stage_auth_token: Auth token for stage environment
            prod_auth_token: Auth token for prod environment
        """
        # Construct endpoints using the backbar.com pattern
        stage_endpoint = f"https://api-{stage_env}.backbar.com".replace("-prod", "")
        prod_endpoint = f"https://api-{prod_env}.backbar.com".replace("-prod", "")
        
        print(f"Stage endpoint: {stage_endpoint}")
        print(f"Prod endpoint: {prod_endpoint}")
        
        self.stage_manager = MachineManager(api_key=stage_api_key, api_endpoint=stage_endpoint, auth_token=stage_auth_token)
        self.prod_manager = MachineManager(api_key=prod_api_key, api_endpoint=prod_endpoint, auth_token=prod_auth_token)
    
    def find_machine_by_name(self, machines: List[Dict], name: str) -> Optional[Dict]:
        """Find a machine by name in a list of machines"""
        for machine in machines:
            if machine.get('name') == name:
                return machine
        return None
    
    def resolve_machine_patterns(self, machine_patterns: List[str], stage_machines: List[Dict], 
                                prod_machines: List[Dict]) -> List[str]:
        """
        Resolve machine name patterns (including wildcards) to actual machine names
        
        Args:
            machine_patterns: List of machine name patterns (can include wildcards)
            stage_machines: List of machines from stage environment
            prod_machines: List of machines from prod environment
            
        Returns:
            List of actual machine names that match the patterns
        """
        # Get unique machine names from both environments
        all_machine_names = set()
        for machine in stage_machines:
            if machine.get('name'):
                all_machine_names.add(machine['name'])
        for machine in prod_machines:
            if machine.get('name'):
                all_machine_names.add(machine['name'])
        
        resolved_names = set()
        
        for pattern in machine_patterns:
            if pattern == '*':
                # Match all machines
                resolved_names.update(all_machine_names)
            elif pattern.endswith('*'):
                # Prefix wildcard matching
                prefix = pattern[:-1]  # Remove the trailing '*'
                for name in all_machine_names:
                    if name.startswith(prefix):
                        resolved_names.add(name)
            else:
                # Exact match
                if pattern in all_machine_names:
                    resolved_names.add(pattern)
        
        return sorted(list(resolved_names))
    
    def compare_setting_properties(self, stage_setting: Dict, prod_setting: Dict, 
                                 compare_fields: List[str]) -> List[str]:
        """
        Compare two settings and return list of differences
        
        Args:
            stage_setting: Setting from stage environment
            prod_setting: Setting from prod environment
            compare_fields: List of fields to compare
            
        Returns:
            List of difference descriptions
        """
        differences = []
        
        for field in compare_fields:
            stage_val = stage_setting.get(field)
            prod_val = prod_setting.get(field)
            
            if stage_val == "false":
                stage_val = "0"
            if prod_val == "false":
                prod_val = "0"
            if stage_val == "true":
                stage_val = "1"
            if prod_val == "true":
                prod_val = "1"

            if stage_val != prod_val and stage_setting.get('isPrivate') == False:
                differences.append(f"{field}: stage='{stage_val}' vs prod='{prod_val}'")
        
        return differences
    
    def compare_board_properties(self, stage_board: Dict, prod_board: Dict, 
                                compare_fields: List[str]) -> List[str]:
        """
        Compare two boards and return list of differences
        """
        differences = []
        
        for field in compare_fields:
            stage_val = stage_board.get(field)
            prod_val = prod_board.get(field)
            
            # Handle nested objects (like application, type, etc.)
            if isinstance(stage_val, dict) and isinstance(prod_val, dict):
                # For nested objects, compare relevant sub-fields
                if field in ['application', 'previous', 'scheduled']:
                    stage_version = stage_val.get('version') if stage_val else None
                    prod_version = prod_val.get('version') if prod_val else None
                    if stage_version != prod_version:
                        differences.append(f"{field}_version: stage='{stage_version}' vs prod='{prod_version}'")
                elif field == 'type':
                    stage_name = stage_val.get('name') if stage_val else None
                    prod_name = prod_val.get('name') if prod_val else None
                    if stage_name != prod_name:
                        differences.append(f"{field}_name: stage='{stage_name}' vs prod='{prod_name}'")
                elif field == 'machine':
                    # Skip machine comparison since we're already comparing by machine
                    continue
            elif stage_val != prod_val:
                differences.append(f"{field}: stage='{stage_val}' vs prod='{prod_val}'")
        
        return differences
    
    def compare_profile_value_properties(self, stage_pv: Dict, prod_pv: Dict, 
                                       compare_fields: List[str]) -> List[str]:
        """
        Compare two profile values and return list of differences
        """
        differences = []
        
        for field in compare_fields:
            # Handle nested detail object
            if field.startswith('detail.'):
                detail_field = field[7:]  # Remove 'detail.' prefix
                stage_val = stage_pv.get('detail', {}).get(detail_field) if stage_pv.get('detail') else None
                prod_val = prod_pv.get('detail', {}).get(detail_field) if prod_pv.get('detail') else None
            else:
                stage_val = stage_pv.get(field)
                prod_val = prod_pv.get(field)
            
            if stage_val != prod_val:
                differences.append(f"{field}: stage='{stage_val}' vs prod='{prod_val}'")
        
        return differences
    
    def compare_ingredient_slot_properties(self, stage_slot: Dict, prod_slot: Dict, 
                                         compare_fields: List[str]) -> List[str]:
        """
        Compare two ingredient slots and return list of differences
        """
        differences = []
        
        for field in compare_fields:
            # Handle nested ingredient object
            if field.startswith('ingredient.'):
                ingredient_field = field[11:]  # Remove 'ingredient.' prefix
                stage_val = stage_slot.get('ingredient', {}).get(ingredient_field) if stage_slot.get('ingredient') else None
                prod_val = prod_slot.get('ingredient', {}).get(ingredient_field) if prod_slot.get('ingredient') else None
            else:
                stage_val = stage_slot.get(field)
                prod_val = prod_slot.get(field)
            
            if stage_val != prod_val:
                differences.append(f"{field}: stage='{stage_val}' vs prod='{prod_val}'")
        
        return differences
    
    def compare_machine_data(self, stage_machine: Dict, prod_machine: Dict,
                           settings_fields: List[str], boards_fields: List[str], 
                           profile_values_fields: List[str], ingredient_slots_fields: List[str]) -> List[ItemDifference]:
        """
        Compare all data for a single machine between environments
        """
        differences = []
        machine_name = stage_machine['name']
        stage_machine_id = stage_machine['id']
        prod_machine_id = prod_machine['id']
        
        print(f"  Comparing settings...")
        
        # Compare Settings
        try:
            stage_settings = self.stage_manager.get_machine_settings(stage_machine_id)
            prod_settings = self.prod_manager.get_machine_settings(prod_machine_id)
            
            # Create lookups by macro
            stage_settings_lookup = {s['name']: s for s in stage_settings}
            prod_settings_lookup = {s['name']: s for s in prod_settings}
            
            all_macros = set(stage_settings_lookup.keys()) | set(prod_settings_lookup.keys())
            
            for macro in all_macros:
                stage_setting = stage_settings_lookup.get(macro)
                prod_setting = prod_settings_lookup.get(macro)
                
                if stage_setting and prod_setting:
                    # Both exist, compare properties
                    prop_differences = self.compare_setting_properties(stage_setting, prod_setting, settings_fields)
                    
                    if prop_differences:
                        differences.append(ItemDifference(
                            machine_name=machine_name,
                            machine_id_stage=stage_machine_id,
                            machine_id_prod=prod_machine_id,
                            item_type="settings",
                            item_identifier=macro,
                            result=ComparisonResult.DIFFERENT,
                            stage_item=stage_setting,
                            prod_item=prod_setting,
                            differences=prop_differences
                        ))
                elif stage_setting and not prod_setting:
                    differences.append(ItemDifference(
                        machine_name=machine_name,
                        machine_id_stage=stage_machine_id,
                        machine_id_prod=prod_machine_id,
                        item_type="settings",
                        item_identifier=macro,
                        result=ComparisonResult.MISSING_IN_PROD,
                        stage_item=stage_setting
                    ))
                elif prod_setting and not stage_setting:
                    differences.append(ItemDifference(
                        machine_name=machine_name,
                        machine_id_stage=stage_machine_id,
                        machine_id_prod=prod_machine_id,
                        item_type="settings",
                        item_identifier=macro,
                        result=ComparisonResult.MISSING_IN_STAGE,
                        prod_item=prod_setting
                    ))
        
        except Exception as e:
            print(f"    Error comparing settings: {e}")
        
        print(f"  Comparing boards...")
        
        # Compare Boards
        try:
            stage_boards = self.stage_manager.get_machine_boards(stage_machine_id)
            prod_boards = self.prod_manager.get_machine_boards(prod_machine_id)
            
            # Create lookups by protocolId
            stage_boards_lookup = {b['protocolId']: b for b in stage_boards}
            prod_boards_lookup = {b['protocolId']: b for b in prod_boards}
            
            all_protocol_ids = set(stage_boards_lookup.keys()) | set(prod_boards_lookup.keys())
            
            for protocol_id in all_protocol_ids:
                stage_board = stage_boards_lookup.get(protocol_id)
                prod_board = prod_boards_lookup.get(protocol_id)
                
                if stage_board and prod_board:
                    # Both exist, compare properties
                    prop_differences = self.compare_board_properties(stage_board, prod_board, boards_fields)
                    
                    if prop_differences:
                        differences.append(ItemDifference(
                            machine_name=machine_name,
                            machine_id_stage=stage_machine_id,
                            machine_id_prod=prod_machine_id,
                            item_type="board",
                            item_identifier=str(protocol_id),
                            result=ComparisonResult.DIFFERENT,
                            stage_item=stage_board,
                            prod_item=prod_board,
                            differences=prop_differences
                        ))
                elif stage_board and not prod_board:
                    differences.append(ItemDifference(
                        machine_name=machine_name,
                        machine_id_stage=stage_machine_id,
                        machine_id_prod=prod_machine_id,
                        item_type="board",
                        item_identifier=str(protocol_id),
                        result=ComparisonResult.MISSING_IN_PROD,
                        stage_item=stage_board
                    ))
                elif prod_board and not stage_board:
                    differences.append(ItemDifference(
                        machine_name=machine_name,
                        machine_id_stage=stage_machine_id,
                        machine_id_prod=prod_machine_id,
                        item_type="board",
                        item_identifier=str(protocol_id),
                        result=ComparisonResult.MISSING_IN_STAGE,
                        prod_item=prod_board
                    ))
        
        except Exception as e:
            print(f"    Error comparing boards: {e}")
        
        print(f"  Comparing profile values...")
        
        # Compare Profile Values
        try:
            stage_profile_values = self.stage_manager.get_machine_profile_values(stage_machine_id)
            prod_profile_values = self.prod_manager.get_machine_profile_values(prod_machine_id)
            
            # Create lookups by macro (from detail.macro)
            stage_pv_lookup = {}
            for pv in stage_profile_values:
                if pv.get('detail') and pv['detail'].get('macro'):
                    stage_pv_lookup[pv['detail']['macro']] = pv
            
            prod_pv_lookup = {}
            for pv in prod_profile_values:
                if pv.get('detail') and pv['detail'].get('macro'):
                    prod_pv_lookup[pv['detail']['macro']] = pv
            
            all_pv_macros = set(stage_pv_lookup.keys()) | set(prod_pv_lookup.keys())

            for macro in all_pv_macros:
                stage_pv = stage_pv_lookup.get(macro)
                prod_pv = prod_pv_lookup.get(macro)
                if stage_pv and prod_pv:
                    # Both exist, compare properties
                    prop_differences = self.compare_profile_value_properties(stage_pv, prod_pv, profile_values_fields)
                    
                    if prop_differences:
                        differences.append(ItemDifference(
                            machine_name=machine_name,
                            machine_id_stage=stage_machine_id,
                            machine_id_prod=prod_machine_id,
                            item_type="profile_value",
                            item_identifier=macro,
                            result=ComparisonResult.DIFFERENT,
                            stage_item=stage_pv,
                            prod_item=prod_pv,
                            differences=prop_differences
                        ))
                elif stage_pv and not prod_pv:
                    differences.append(ItemDifference(
                        machine_name=machine_name,
                        machine_id_stage=stage_machine_id,
                        machine_id_prod=prod_machine_id,
                        item_type="profile_value",
                        item_identifier=macro,
                        result=ComparisonResult.MISSING_IN_PROD,
                        stage_item=stage_pv
                    ))
                elif prod_pv and not stage_pv:
                    differences.append(ItemDifference(
                        machine_name=machine_name,
                        machine_id_stage=stage_machine_id,
                        machine_id_prod=prod_machine_id,
                        item_type="profile_value",
                        item_identifier=macro,
                        result=ComparisonResult.MISSING_IN_STAGE,
                        prod_item=prod_pv
                    ))
        
        except Exception as e:
            print(f"    Error comparing profile values: {e}")
        
        print(f"  Comparing ingredient slots...")
        
        # Compare Ingredient Slots
        try:
            stage_slots = self.stage_manager.get_machine_ingredient_slots(stage_machine_id)
            prod_slots = self.prod_manager.get_machine_ingredient_slots(prod_machine_id)
            
            # Create lookups by slotNumber
            stage_slots_lookup = {s['slotNumber']: s for s in stage_slots}
            prod_slots_lookup = {s['slotNumber']: s for s in prod_slots}
            
            all_slot_numbers = set(stage_slots_lookup.keys()) | set(prod_slots_lookup.keys())
            
            for slot_number in all_slot_numbers:
                stage_slot = stage_slots_lookup.get(slot_number)
                prod_slot = prod_slots_lookup.get(slot_number)
                
                if stage_slot and prod_slot:
                    # Both exist, compare properties
                    prop_differences = self.compare_ingredient_slot_properties(stage_slot, prod_slot, ingredient_slots_fields)
                    
                    if prop_differences:
                        differences.append(ItemDifference(
                            machine_name=machine_name,
                            machine_id_stage=stage_machine_id,
                            machine_id_prod=prod_machine_id,
                            item_type="ingredient_slot",
                            item_identifier=str(slot_number),
                            result=ComparisonResult.DIFFERENT,
                            stage_item=stage_slot,
                            prod_item=prod_slot,
                            differences=prop_differences
                        ))
                elif stage_slot and not prod_slot:
                    differences.append(ItemDifference(
                        machine_name=machine_name,
                        machine_id_stage=stage_machine_id,
                        machine_id_prod=prod_machine_id,
                        item_type="ingredient_slot",
                        item_identifier=str(slot_number),
                        result=ComparisonResult.MISSING_IN_PROD,
                        stage_item=stage_slot
                    ))
                elif prod_slot and not stage_slot:
                    differences.append(ItemDifference(
                        machine_name=machine_name,
                        machine_id_stage=stage_machine_id,
                        machine_id_prod=prod_machine_id,
                        item_type="ingredient_slot",
                        item_identifier=str(slot_number),
                        result=ComparisonResult.MISSING_IN_STAGE,
                        prod_item=prod_slot
                    ))
        
        except Exception as e:
            print(f"    Error comparing ingredient slots: {e}")
        
        return differences
    
    def compare_machines(self, machine_patterns: List[str], 
                        settings_fields: List[str], boards_fields: List[str], 
                        profile_values_fields: List[str], ingredient_slots_fields: List[str]) -> List[ItemDifference]:
        """
        Compare selected machines between stage and prod environments
        """
        print("Fetching machine lists from both environments...")
        
        # Get machine lists from both environments
        stage_machines = self.stage_manager.list_machines()
        prod_machines = self.prod_manager.list_machines()
        
        print(f"Found {len(stage_machines)} machines in stage")
        print(f"Found {len(prod_machines)} machines in prod")
        
        # Resolve machine patterns to actual machine names
        machine_names = self.resolve_machine_patterns(machine_patterns, stage_machines, prod_machines)
        
        if not machine_names:
            print("No machines matched the specified patterns")
            return []
        
        print(f"Resolved patterns {machine_patterns} to {len(machine_names)} machines: {machine_names[:5]}{'...' if len(machine_names) > 5 else ''}")
        
        # Warn if comparing a large number of machines
        if len(machine_names) > 50:
            print(f"⚠️  Warning: Comparing {len(machine_names)} machines may take a significant amount of time")
            print("   Consider using more specific patterns or running in smaller batches")
        
        all_differences = []
        
        for machine_name in machine_names:
            print(f"\nComparing machine: {machine_name}")
            
            # Find the machine in both environments
            stage_machine = self.find_machine_by_name(stage_machines, machine_name)
            prod_machine = self.find_machine_by_name(prod_machines, machine_name)
            
            if not stage_machine:
                print(f"  Machine '{machine_name}' not found in stage environment")
                continue
            
            if not prod_machine:
                print(f"  Machine '{machine_name}' not found in prod environment")
                continue
            
            print(f"  Stage machine ID: {stage_machine['id']}")
            print(f"  Prod machine ID: {prod_machine['id']}")
            
            # Compare the machine data
            machine_differences = self.compare_machine_data(
                stage_machine, prod_machine,
                settings_fields, boards_fields, profile_values_fields, ingredient_slots_fields
            )
            
            all_differences.extend(machine_differences)
            
            # Show progress summary for this machine
            settings_diffs = [d for d in machine_differences if d.item_type == "settings" and d.result == ComparisonResult.DIFFERENT]
            boards_diffs = [d for d in machine_differences if d.item_type == "board" and d.result == ComparisonResult.DIFFERENT]
            pv_diffs = [d for d in machine_differences if d.item_type == "profile_value" and d.result == ComparisonResult.DIFFERENT]
            slot_diffs = [d for d in machine_differences if d.item_type == "ingredient_slot" and d.result == ComparisonResult.DIFFERENT]
            
            print(f"  Found {len(settings_diffs)} setting differences")
            print(f"  Found {len(boards_diffs)} board differences")
            print(f"  Found {len(pv_diffs)} profile value differences")
            print(f"  Found {len(slot_diffs)} ingredient slot differences")
        
        return all_differences
    
    def print_summary(self, differences: List[ItemDifference], machine_names: List[str]):
        """
        Print a concise summary of the comparison results
        """
        print("\n" + "="*80)
        print("MACHINE COMPARISON SUMMARY")
        print("="*80)
        
        # Group differences by machine and type
        machine_diffs = {}
        for diff in differences:
            if diff.machine_name not in machine_diffs:
                machine_diffs[diff.machine_name] = {"settings": [], "board": [], "profile_value": [], "ingredient_slot": []}
            machine_diffs[diff.machine_name][diff.item_type].append(diff)
        
        # Calculate overall statistics
        total_machines = len(machine_names)
        machines_with_diffs = len(machine_diffs)
        machines_identical = total_machines - machines_with_diffs
        
        total_diffs = len(differences)
        settings_total = len([d for d in differences if d.item_type == "settings" and d.result == ComparisonResult.DIFFERENT])
        boards_total = len([d for d in differences if d.item_type == "board" and d.result == ComparisonResult.DIFFERENT])
        pv_total = len([d for d in differences if d.item_type == "profile_value" and d.result == ComparisonResult.DIFFERENT])
        slots_total = len([d for d in differences if d.item_type == "ingredient_slot" and d.result == ComparisonResult.DIFFERENT])
        
        print(f"Compared {total_machines} machines")
        print(f"Machines with differences: {machines_with_diffs}")
        print(f"Machines identical: {machines_identical}")
        print(f"Total differences found: {total_diffs}")
        print(f"  - Settings: {settings_total}")
        print(f"  - Boards: {boards_total}")
        print(f"  - Profile Values: {pv_total}")
        print(f"  - Ingredient Slots: {slots_total}")
        
        # For many machines, show a condensed view
        if len(machine_names) > 10:
            print(f"\n{'-'*60}")
            print("MACHINES WITH DIFFERENCES (condensed view):")
            print(f"{'-'*60}")
            
            for machine_name in sorted(machine_diffs.keys()):
                machine_data = machine_diffs[machine_name]
                s_count = len([d for d in machine_data["settings"] if d.result == ComparisonResult.DIFFERENT])
                b_count = len([d for d in machine_data["board"] if d.result == ComparisonResult.DIFFERENT])
                pv_count = len([d for d in machine_data["profile_value"] if d.result == ComparisonResult.DIFFERENT])
                slot_count = len([d for d in machine_data["ingredient_slot"] if d.result == ComparisonResult.DIFFERENT])
                
                total = s_count + b_count + pv_count + slot_count
                if total > 0:
                    parts = []
                    if s_count > 0: parts.append(f"{s_count} settings")
                    if b_count > 0: parts.append(f"{b_count} boards")
                    if pv_count > 0: parts.append(f"{pv_count} profile values")
                    if slot_count > 0: parts.append(f"{slot_count} ingredient slots")
                    print(f"  {machine_name}: {', '.join(parts)}")
            
            return
        
        # For smaller numbers of machines, show detailed view
        for machine_name in machine_names:
            print(f"\nMACHINE: {machine_name}")
            print("-" * 40)
            
            if machine_name not in machine_diffs:
                print("  Settings: No differences")
                print("  Boards: No differences")
                print("  Profile Values: No differences")
                print("  Ingredient Slots: No differences")
                continue
            
            # Settings
            settings_diffs = [d for d in machine_diffs[machine_name]["settings"] if d.result == ComparisonResult.DIFFERENT]
            if not settings_diffs:
                print("  Settings: No differences")
            else:
                print(f"  Settings: {len(settings_diffs)} differences")                
                for diff in settings_diffs:  # Show first 5
                    print(f"    {diff.stage_item['name']}: {', '.join(diff.differences[:2])}{'...' if len(diff.differences) > 2 else ''}")

            # Boards
            boards_diffs = [d for d in machine_diffs[machine_name]["board"] if d.result == ComparisonResult.DIFFERENT]
            if not boards_diffs:
                print("  Boards: No differences")
            else:
                print(f"  Boards: {len(boards_diffs)} differences")
                for diff in boards_diffs:
                    print(f"    Protocol {diff.item_identifier}: {', '.join(diff.differences[:2])}{'...' if len(diff.differences) > 2 else ''}")
            
            # Profile Values
            pv_diffs = [d for d in machine_diffs[machine_name]["profile_value"] if d.result == ComparisonResult.DIFFERENT]
            if not pv_diffs:
                print("  Profile Values: No differences")
            else:
                print(f"  Profile Values: {len(pv_diffs)} differences")
                for diff in pv_diffs[:5]:  # Show first 5
                    print(f"    {diff.item_identifier}: {', '.join(diff.differences[:2])}{'...' if len(diff.differences) > 2 else ''}")
                if len(pv_diffs) > 5:
                    print(f"    ... and {len(pv_diffs) - 5} more")
            
            # Ingredient Slots
            slot_diffs = [d for d in machine_diffs[machine_name]["ingredient_slot"] if d.result == ComparisonResult.DIFFERENT]
            if not slot_diffs:
                print("  Ingredient Slots: No differences")
            else:
                print(f"  Ingredient Slots: {len(slot_diffs)} differences")
                for diff in slot_diffs:
                    print(f"    Slot {diff.item_identifier}: {', '.join(diff.differences[:2])}{'...' if len(diff.differences) > 2 else ''}")
    
    def export_to_json(self, differences: List[ItemDifference], filename: str):
        """
        Export comparison results to JSON file
        """
        # Convert to serializable format
        export_data = []
        for diff in differences:
            export_data.append({
                'machine_name': diff.machine_name,
                'machine_id_stage': diff.machine_id_stage,
                'machine_id_prod': diff.machine_id_prod,
                'item_type': diff.item_type,
                'item_identifier': diff.item_identifier,
                'result': diff.result.value,
                'stage_item': diff.stage_item,
                'prod_item': diff.prod_item,
                'differences': diff.differences
            })
        
        with open(filename, 'w') as f:
            json.dump(export_data, f, indent=2, default=str)
        
        print(f"\nDetailed results exported to {filename}")

def load_config_from_env():
    """Load configuration from environment files or environment variables"""
    config = {}
    
    # Try to load from .env files first
    env_files = ['.env.stage', '.env.prod']
    for env_file in env_files:
        if os.path.exists(env_file):
            print(f"Loading config from {env_file}")
            with open(env_file, 'r') as f:
                for line in f:
                    line = line.strip()
                    if line and not line.startswith('#') and '=' in line:
                        key, value = line.split('=', 1)
                        config[key.strip()] = value.strip()
    
    # Override with environment variables if they exist
    env_vars = ['STAGE_API_KEY', 'PROD_API_KEY', 'STAGE_AUTH_TOKEN', 'PROD_AUTH_TOKEN']
    for var in env_vars:
        if os.getenv(var):
            config[var] = os.getenv(var)
    
    return config

def main():
    """Main function to run the comparison"""
    parser = argparse.ArgumentParser(description='Compare machine settings, boards, and profile values between environments')
    parser.add_argument('machines', nargs='+', help='Machine names or patterns to compare. Use "*" for all machines, or "prefix*" for wildcard matching (e.g., "GC*" matches all machines starting with "GC")')
    parser.add_argument('--stage-key', type=str, help='API key for stage environment')
    parser.add_argument('--prod-key', type=str, help='API key for prod environment')
    parser.add_argument('--stage-env', type=str, default='stage', help='Stage environment name (default: stage)')
    parser.add_argument('--prod-env', type=str, default='prod', help='Prod environment name (default: prod)')
    parser.add_argument('--stage-token', type=str, help='Auth token for stage environment')
    parser.add_argument('--prod-token', type=str, help='Auth token for prod environment')
    
    # Field comparison options
    parser.add_argument('--settings-compare', '-sc', type=str,
                       default='name,description,currentValue,newValue,defaultParam,minParam,maxParam,paramType,isPrivate',
                       help='Comma-separated list of setting fields to compare')
    parser.add_argument('--boards-compare', '-bc', type=str,
                       default='status,forceUpdate,lockVersion,pcbMajor,pcbMinor,pcbPatch,pumpRating,application,type',
                       help='Comma-separated list of board fields to compare')
    parser.add_argument('--profile-values-compare', '-pvc', type=str,
                       default='value,detail.name,detail.description,detail.defaultValue,detail.minValue,detail.maxValue',
                       help='Comma-separated list of profile value fields to compare')
    parser.add_argument('--ingredient-slots-compare', '-isc', type=str,
                       default='ingredient.name,bottleSize,remaining,dockState,cleaningFrequency,rinseWithWater,thickness,ingredientSpeed,dispenseCalibrationFactor,bubbleDetectThreshold',
                       help='Comma-separated list of ingredient slot fields to compare')
    
    args = parser.parse_args()
    
    print("Machine Environment Comparison Tool")
    print("="*50)
    print(f"Machine patterns: {', '.join(args.machines)}")
    
    # Get API credentials and auth tokens
    stage_api_key = args.stage_key
    prod_api_key = args.prod_key
    stage_auth_token = args.stage_token
    prod_auth_token = args.prod_token
    
    if not stage_api_key or not prod_api_key or not stage_auth_token or not prod_auth_token:
        # Load configuration from environment/files
        config = load_config_from_env()
        stage_api_key = stage_api_key or config.get('STAGE_API_KEY')
        prod_api_key = prod_api_key or config.get('PROD_API_KEY')
        stage_auth_token = stage_auth_token or config.get('STAGE_AUTH_TOKEN')
        prod_auth_token = prod_auth_token or config.get('PROD_AUTH_TOKEN')
    
    if not stage_api_key or not prod_api_key:
        print("Error: Missing API keys. Please provide them via:")
        print("  Command line: --stage-key <key> --prod-key <key>")
        print("  Environment variables: STAGE_API_KEY and PROD_API_KEY")
        print("  Environment files: .env.stage and .env.prod")
        sys.exit(1)
    
    if not stage_auth_token or not prod_auth_token:
        print("Error: Missing auth tokens. Please provide them via:")
        print("  Command line: --stage-token <token> --prod-token <token>")
        print("  Environment variables: STAGE_AUTH_TOKEN and PROD_AUTH_TOKEN")
        print("  Environment files: .env.stage and .env.prod")
        sys.exit(1)
    
    # Parse field lists
    settings_fields = [f.strip() for f in args.settings_compare.split(',')]
    boards_fields = [f.strip() for f in args.boards_compare.split(',')]
    profile_values_fields = [f.strip() for f in args.profile_values_compare.split(',')]
    ingredient_slots_fields = [f.strip() for f in args.ingredient_slots_compare.split(',')]
    
    print(f"Comparing settings fields: {settings_fields}")
    print(f"Comparing boards fields: {boards_fields}")
    print(f"Comparing profile values fields: {profile_values_fields}")
    print(f"Comparing ingredient slots fields: {ingredient_slots_fields}")
    
    # Initialize comparer
    comparer = MachineEnvironmentComparer(
        stage_api_key=stage_api_key,
        prod_api_key=prod_api_key,
        stage_env=args.stage_env,
        prod_env=args.prod_env,
        stage_auth_token=stage_auth_token,
        prod_auth_token=prod_auth_token
    )
    
    try:
        # Run comparison
        differences = comparer.compare_machines(
            args.machines, settings_fields, boards_fields, profile_values_fields, ingredient_slots_fields
        )
        
        # Print results
        comparer.print_summary(differences, args.machines)
        
        # Export to JSON
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        json_filename = f"machine_comparison_{timestamp}.json"
        comparer.export_to_json(differences, json_filename)
        
    except Exception as e:
        print(f"Error during comparison: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main() 