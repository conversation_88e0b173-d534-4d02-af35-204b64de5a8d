# Hardware Management System

This document provides instructions for managing hardware, EEPROM settings, board records, and app/profile settings using the Notion database and the hardware import tool.

## 1. Modifying/Adding Hardware

Hardware definitions are managed in the [Hardware Revisions Database](https://www.notion.so/backbar/1ac853ef6fe1808fa8b3cb273d4cef3f?v=1ac853ef6fe180ffb126000c83cca07f&pvs=4).

### Process:

1. Navigate to the Hardware Revisions Database in Notion
2. Add or modify hardware entries as needed
   - Each hardware should have a Name, Type, Rev (a single letter A-Z), and Description
   - The Type must match one of the valid hardware types: "hardware", "subsystem", or "addon"

### Importing to Backend:

To import all hardware from Notion:

```bash
python hardware_import.py hardware import-notion
```

This will create any new hardware entries that don't already exist in the system.

## 2. Modifying/Adding EEPROM Settings

EEPROM settings are managed in dedicated databases linked from the Hardware Revisions Database.

### Process:

1. Navigate to the Hardware Revisions Database
2. Click on the specific hardware system you want to modify
3. Find the linked EEPROM settings database
4. Add or modify settings as needed
   - Each setting should have a Name, Macro, Address, Board Name, Board ID, Param Type, and values for each revision
   - Use "N/A" for any revision where the setting doesn't apply

### Importing to Backend:

To import EEPROM settings for a specific hardware:

```bash
python hardware_import.py settings notion <database_id> --hardware-id <id> --revision "Rev A"
```

Replace:
- `<database_id>` with the Notion database ID for the EEPROM settings
- `<id>` with the hardware ID in the backend system
- `"Rev A"` with the specific revision you're importing for

## 3. Modifying/Adding Board Records

Board records are also managed in dedicated databases linked from the Hardware Revisions Database.

### Process:

1. Navigate to the Hardware Revisions Database
2. Click on the specific hardware system you want to modify
3. Find the linked Board Records database
4. Add or modify board records as needed
   - Each board should have a Board Name, Protocol ID, Type ID, and Pump Rating
   - For each revision, indicate whether the board should be included (True/False)

For new boards, also add them to the [PCBs Database](https://www.notion.so/backbar/1f4853ef6fe180bdabcdf95e2541dd03?v=1f4853ef6fe1809dbe13000ce2844873&pvs=4).

### Importing to Backend:

To import board records for a specific hardware:

```bash
python hardware_import.py boards <database_id> --hardware-id <id> --revision "Rev A"
```

Replace:
- `<database_id>` with the Notion database ID for the board records
- `<id>` with the hardware ID in the backend system
- `"Rev A"` with the specific revision you're importing for

## 4. Modifying/Adding App/Profile Settings

App settings (also called profile values) are managed in dedicated databases linked from the Hardware Revisions Database.

### Process:

1. Navigate to the Hardware Revisions Database
2. Click on the specific hardware system you want to modify
3. Find the linked App Settings database
4. Add or modify app settings as needed
   - Each setting should have a Name, Macro, Param Type, Security Level, Category, Min/Max values, and default values for each revision
   - Use "N/A" for any revision where the setting doesn't apply

### Importing to Backend:

To import app settings for a specific hardware:

```bash
python hardware_import.py app-settings <database_id> --hardware-id <id> --revision "Rev A"
```

Replace:
- `<database_id>` with the Notion database ID for the app settings
- `<id>` with the hardware ID in the backend system
- `"Rev A"` with the specific revision you're importing for

## 5. Webhook Integration for Automatic Updates

You can set up webhooks to automatically update specific databases when changes are made in Notion, without having to update everything at once.

### Setting Up Webhooks:

1. Configure your Notion integration to send webhook events when a specific database is modified
2. In your webhook handler, call the hardware import script with the `import-specific` command

### Using the Specific Database Import:

To update only a specific database for a particular hardware type:

```bash
python hardware_import.py import-specific --hardware "Cooling" --db-type eeprom
```

Replace:
- `"Cooling"` with the name of the hardware system (e.g., "Self Cleaning", "Cooling")
- `eeprom` with the type of database to update (one of: "eeprom", "boards", or "app_settings")

This command will:
1. Find the appropriate Notion database ID from your .env file
2. Locate all revisions of the specified hardware in your system
3. Update only the specified database type for all revisions of that hardware

### Example Webhook Implementation:

```python
from flask import Flask, request
import subprocess
import json

app = Flask(__name__)

@app.route('/notion-webhook', methods=['POST'])
def notion_webhook():
    data = request.json
    
    # Determine which database was updated
    database_id = data.get('database_id')
    
    # Map database IDs to hardware and database types
    db_mapping = {
        'your_cooling_eeprom_db_id': {'hardware': 'Cooling', 'db_type': 'eeprom'},
        'your_cooling_boards_db_id': {'hardware': 'Cooling', 'db_type': 'boards'},
        # Add more mappings as needed
    }
    
    if database_id in db_mapping:
        hardware = db_mapping[database_id]['hardware']
        db_type = db_mapping[database_id]['db_type']
        
        # Run the import command
        subprocess.run([
            'python', 'hardware_import.py', 'import-specific',
            '--hardware', hardware,
            '--db-type', db_type
        ])
        
        return {'status': 'success', 'message': f'Updated {hardware} {db_type}'}
    
    return {'status': 'error', 'message': 'Unknown database ID'}

if __name__ == '__main__':
    app.run(port=5000)
```

This webhook example listens for Notion database changes and triggers the appropriate import based on which database was modified.

## Environment Setup for Webhooks

When setting up webhooks, ensure your environment includes all the necessary variables as described in the Environment Setup section, plus any additional configuration needed for your webhook server.

## Environment Setup

Make sure your `.env` file contains the following variables:

```
API_ENDPOINT=https://api-dev.backbar.com
API_KEY=your_api_key
NOTION_TOKEN=your_notion_token
NOTION_HARDWARE_REVISIONS_ID=your_hardware_db_id

# For each hardware type, add database IDs for its settings
NOTION_COOLING_EEPROM_ID=your_cooling_eeprom_db_id
NOTION_COOLING_BOARDS_ID=your_cooling_boards_db_id
NOTION_COOLING_APP_SETTINGS_ID=your_cooling_app_settings_db_id

# Add similar entries for other hardware types
```

## Troubleshooting

- If you encounter foreign key constraint errors when deleting hardware, make sure to delete all associated boards first.
- Verify that all required fields are filled in Notion before importing.
- Check that revision values match the expected format (single letter A-Z).
- For any import errors, review the console output for specific details about what went wrong.

### Applying Hardware to Machines:

To apply hardware assignments from Notion to machines:

```bash
python hardware_import.py apply-hardware
```

By default, this command will both add hardware to machines and apply the hardware settings. You can control this behavior with the following options:

```bash
# Only add hardware to machines, don't apply settings
python hardware_import.py apply-hardware --add-only

# Only apply hardware settings, don't add hardware to machines
python hardware_import.py apply-hardware --apply-only
```

The process follows these steps:
1. Fetch all machines and their hardware assignments from Notion
2. Look up the corresponding hardware IDs in your system
3. If adding hardware (default or --add-only):
   - Check if each hardware is already added to the machine
   - If a different revision of the same hardware is found, remove it
   - Add the new hardware if it's not already added
4. If applying hardware (default or --apply-only):
   - Apply the hardware settings to the machine

The script uses the last 5 digits of the serial number to identify machines in the system.

Example output:
```
📌 Machine: SN: BB12345, Admin ID: 42
  🗑️ Removing Cooling System Rev A [ID: 123] from machine
  ➕ Adding Cooling System Rev B [ID: 124] to machine
  ✅ Applying Cooling System Rev B [ID: 124]
  📊 Machine Summary: 1 added, 1 removed, 1 applied, 0 skipped, 0 errors
```

### Adding and Applying Hardware to a Specific Machine:

To add and apply specific hardware to a specific machine by serial number:

```bash
python hardware_import.py machine-hardware --serial "BB12345" --hardware "Cooling" --revision "B"
```

This command provides detailed output similar to the import functions and will:
1. Find the machine with the specified serial number
2. Find the hardware with the specified name and revision
3. If a different revision of the same hardware is found on the machine, remove it
4. Add the new hardware if it's not already added
5. Apply the hardware settings to the machine

You can control the behavior with the following options:

```bash
# Only add hardware to machine, don't apply settings
python hardware_import.py machine-hardware --serial "BB12345" --hardware "Cooling" --revision "B" --add-only

# Only apply hardware settings, don't add hardware to machine
python hardware_import.py machine-hardware --serial "BB12345" --hardware "Cooling" --revision "B" --apply-only
```

Example output:
```
================================================================================
========================= PROCESSING MACHINE: BB12345 ==========================
================================================================================

📌 Machine: SN: BB12345, Admin ID: 42

Adding, applying Cooling Rev B to machine...
  🗑️ Removing Cooling Rev A [ID: 123] from machine
  ➕ Adding Cooling Rev B [ID: 124] to machine
  ✅ Applying Cooling Rev B [ID: 124]

--------------------------------------------------------------------------------
----------------------------- OPERATION SUMMARY -------------------------------
--------------------------------------------------------------------------------
  Hardware Added: +1
  Old Revisions Removed: 1
  Hardware Applied: +1
  Errors: 0

================================================================================
============================ OPERATION COMPLETE ===============================
================================================================================
```

### Adding and Applying All Hardware to a Specific Machine:

To add and apply all hardware to a specific machine by serial number:

```bash
python hardware_import.py machine-all-hardware --serial "BB12345"
```

This command will:
1. Find the machine with the specified serial number in both Notion and your system
2. Get all hardware assignments for this machine from Notion
3. For each hardware assignment:
   - Find the hardware with the specified name and revision
   - If a different revision of the same hardware is found on the machine, remove it
   - Add the new hardware if it's not already added
   - Apply the hardware settings to the machine

You can control the behavior with the following options:

```bash
# Only add hardware to machine, don't apply settings
python hardware_import.py machine-all-hardware --serial "BB12345" --add-only

# Only apply hardware settings, don't add hardware to machine
python hardware_import.py machine-all-hardware --serial "BB12345" --apply-only
```

Example output:
```
================================================================================
========================= PROCESSING MACHINE: BB12345 ==========================
================================================================================

📌 Machine: SN: BB12345, Admin ID: 42

Adding, applying all hardware to machine...
  ➕ Adding Cooling Rev B [ID: 124] to machine
  ✅ Applying Cooling Rev B [ID: 124]
  🗑️ Removing Self Cleaning Rev A [ID: 125] from machine
  ➕ Adding Self Cleaning Rev B [ID: 126] to machine
  ✅ Applying Self Cleaning Rev B [ID: 126]
  ✓ Nozzle Rev A [ID: 127] already added to machine
  ✅ Applying Nozzle Rev A [ID: 127]

--------------------------------------------------------------------------------
----------------------------- OPERATION SUMMARY -------------------------------
--------------------------------------------------------------------------------
  Hardware Added: +2
  Old Revisions Removed: 1
  Hardware Applied: +3
  Skipped: 0
  Errors: 0

================================================================================
============================ OPERATION COMPLETE ===============================
================================================================================
```
