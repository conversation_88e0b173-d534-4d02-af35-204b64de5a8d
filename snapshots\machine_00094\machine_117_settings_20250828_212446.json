[{"id": 79830, "name": "Allow Runout A1, A2, B1, B2", "macro": "EEP_ALLOW_RUNOUT", "description": "Allow runout errors for this board's ingredients during drink requests", "address": 277, "boardName": "Pump Board 7", "boardId": 7, "paramType": "BOOLEAN", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 117, "isPrivate": false}, {"id": 79831, "name": "Allow Runout A3, A4, B3, B4", "macro": "EEP_ALLOW_RUNOUT", "description": "Allow runout errors for this board's ingredients during drink requests", "address": 277, "boardName": "Pump Board 8", "boardId": 8, "paramType": "BOOLEAN", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 117, "isPrivate": false}, {"id": 79832, "name": "Allow Runout A5, A6, B5, B6", "macro": "EEP_ALLOW_RUNOUT", "description": "Allow runout errors for this board's ingredients during drink requests", "address": 277, "boardName": "Pump Board 9", "boardId": 9, "paramType": "BOOLEAN", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 117, "isPrivate": false}, {"id": 79833, "name": "Allow Runout C1, D1, E1, F1", "macro": "EEP_ALLOW_RUNOUT", "description": "Allow runout errors for this board's ingredients during drink requests", "address": 277, "boardName": "Pump Board 0", "boardId": 0, "paramType": "BOOLEAN", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 117, "isPrivate": false}, {"id": 79834, "name": "Allow Runout C2, E2, D2, F2", "macro": "EEP_ALLOW_RUNOUT", "description": "Allow runout errors for this board's ingredients during drink requests", "address": 277, "boardName": "Pump Board 1", "boardId": 1, "paramType": "BOOLEAN", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 117, "isPrivate": false}, {"id": 79835, "name": "Allow Runout C3, D3, E3, F3", "macro": "EEP_ALLOW_RUNOUT", "description": "Allow runout errors for this board's ingredients during drink requests", "address": 277, "boardName": "Pump Board 2", "boardId": 2, "paramType": "BOOLEAN", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 117, "isPrivate": false}, {"id": 79836, "name": "Allow Runout C4, D4, E4, F4", "macro": "EEP_ALLOW_RUNOUT", "description": "Allow runout errors for this board's ingredients during drink requests", "address": 277, "boardName": "Pump Board 3", "boardId": 3, "paramType": "BOOLEAN", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 117, "isPrivate": false}, {"id": 79837, "name": "Allow Runout C5, D5, E5, F5", "macro": "EEP_ALLOW_RUNOUT", "description": "Allow runout errors for this board's ingredients during drink requests", "address": 277, "boardName": "Pump Board 4", "boardId": 4, "paramType": "BOOLEAN", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 117, "isPrivate": false}, {"id": 79838, "name": "Allow Runout C6, D6, E6, F6", "macro": "EEP_ALLOW_RUNOUT", "description": "Allow runout errors for this board's ingredients during drink requests", "address": 277, "boardName": "Pump Board 5", "boardId": 5, "paramType": "BOOLEAN", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 117, "isPrivate": false}, {"id": 79839, "name": "Allow Runout T1 - T4", "macro": "EEP_ALLOW_RUNOUT", "description": "Allow runout errors for this board's ingredients during drink requests", "address": 277, "boardName": "Pump Board 13", "boardId": 13, "paramType": "BOOLEAN", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 117, "isPrivate": false}, {"id": 79840, "name": "Allow Runout T5 - T8", "macro": "EEP_ALLOW_RUNOUT", "description": "Allow runout errors for this board's ingredients during drink requests", "address": 277, "boardName": "Pump Board 14", "boardId": 14, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 117, "isPrivate": false}, {"id": 79841, "name": "Bib Cleaning Quantity (every drink)", "macro": "EEP_CLEAN_BIB_QTY", "description": "Amount (in 1/20 fl oz increments) of water to dispense after a drink is dispensed to clean the bib and diffuser. This is also 1/4 of the amount of water used to clean the bib during refresh cycles if ", "address": 336, "boardName": "Master", "boardId": 65535, "paramType": "WORD", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "20", "machineId": 117, "isPrivate": false}, {"id": 79842, "name": "Bubble Detection Threshold - A1", "macro": "EEP_BUBBLE_ROC_THRESHOLD_0", "description": "Rate of change value required to signify a runout. (Default Oat milk = 30, almond/dairy = 50, all other 75+)", "address": 368, "boardName": "Pump Board 7", "boardId": 7, "paramType": "BYTE", "defaultParam": "50", "currentValue": "50", "newValue": null, "minParam": "15", "maxParam": "150", "machineId": 117, "isPrivate": false}, {"id": 79843, "name": "Bubble Detection Threshold - A2", "macro": "EEP_BUBBLE_ROC_THRESHOLD_2", "description": "Rate of change value required to signify a runout. (Default Oat milk = 30, almond/dairy = 50, all other 75+)", "address": 370, "boardName": "Pump Board 7", "boardId": 7, "paramType": "BYTE", "defaultParam": "50", "currentValue": "50", "newValue": "50", "minParam": "15", "maxParam": "150", "machineId": 117, "isPrivate": false}, {"id": 79844, "name": "Bubble Detection Threshold - A3", "macro": "EEP_BUBBLE_ROC_THRESHOLD_0", "description": "Rate of change value required to signify a runout. (Default Oat milk = 30, almond/dairy = 50, all other 75+)", "address": 368, "boardName": "Pump Board 8", "boardId": 8, "paramType": "BYTE", "defaultParam": "50", "currentValue": "50", "newValue": "50", "minParam": "15", "maxParam": "150", "machineId": 117, "isPrivate": false}, {"id": 79845, "name": "Bubble Detection Threshold - A4", "macro": "EEP_BUBBLE_ROC_THRESHOLD_2", "description": "Rate of change value required to signify a runout. (Default Oat milk = 30, almond/dairy = 50, all other 75+)", "address": 370, "boardName": "Pump Board 8", "boardId": 8, "paramType": "BYTE", "defaultParam": "50", "currentValue": "50", "newValue": "50", "minParam": "15", "maxParam": "150", "machineId": 117, "isPrivate": false}, {"id": 79846, "name": "Bubble Detection Threshold - A5", "macro": "EEP_BUBBLE_ROC_THRESHOLD_0", "description": "Rate of change value required to signify a runout. (Default Oat milk = 30, almond/dairy = 50, all other 75+)", "address": 368, "boardName": "Pump Board 9", "boardId": 9, "paramType": "BYTE", "defaultParam": "50", "currentValue": "50", "newValue": "50", "minParam": "15", "maxParam": "150", "machineId": 117, "isPrivate": false}, {"id": 79847, "name": "Bubble Detection Threshold - A6", "macro": "EEP_BUBBLE_ROC_THRESHOLD_2", "description": "Rate of change value required to signify a runout. (Default Oat milk = 30, almond/dairy = 50, all other 75+)", "address": 370, "boardName": "Pump Board 9", "boardId": 9, "paramType": "BYTE", "defaultParam": "50", "currentValue": "50", "newValue": "50", "minParam": "15", "maxParam": "150", "machineId": 117, "isPrivate": false}, {"id": 79848, "name": "Bubble Detection Threshold - B1", "macro": "EEP_BUBBLE_ROC_THRESHOLD_1", "description": "Rate of change value required to signify a runout. (Default Oat milk = 30, almond/dairy = 50, all other 75+)", "address": 369, "boardName": "Pump Board 7", "boardId": 7, "paramType": "BYTE", "defaultParam": "50", "currentValue": "50", "newValue": "50", "minParam": "15", "maxParam": "150", "machineId": 117, "isPrivate": false}, {"id": 79849, "name": "Bubble Detection Threshold - B2", "macro": "EEP_BUBBLE_ROC_THRESHOLD_3", "description": "Rate of change value required to signify a runout. (Default Oat milk = 30, almond/dairy = 50, all other 75+)", "address": 371, "boardName": "Pump Board 7", "boardId": 7, "paramType": "BYTE", "defaultParam": "50", "currentValue": "50", "newValue": "50", "minParam": "15", "maxParam": "150", "machineId": 117, "isPrivate": false}, {"id": 79850, "name": "Bubble Detection Threshold - B3", "macro": "EEP_BUBBLE_ROC_THRESHOLD_1", "description": "Rate of change value required to signify a runout. (Default Oat milk = 30, almond/dairy = 50, all other 75+)", "address": 369, "boardName": "Pump Board 8", "boardId": 8, "paramType": "BYTE", "defaultParam": "50", "currentValue": "50", "newValue": "50", "minParam": "15", "maxParam": "150", "machineId": 117, "isPrivate": false}, {"id": 79851, "name": "Bubble Detection Threshold - B4", "macro": "EEP_BUBBLE_ROC_THRESHOLD_3", "description": "Rate of change value required to signify a runout. (Default Oat milk = 30, almond/dairy = 50, all other 75+)", "address": 371, "boardName": "Pump Board 8", "boardId": 8, "paramType": "BYTE", "defaultParam": "50", "currentValue": "50", "newValue": "50", "minParam": "15", "maxParam": "150", "machineId": 117, "isPrivate": false}, {"id": 79852, "name": "Bubble Detection Threshold - B5", "macro": "EEP_BUBBLE_ROC_THRESHOLD_1", "description": "Rate of change value required to signify a runout. (Default Oat milk = 30, almond/dairy = 50, all other 75+)", "address": 369, "boardName": "Pump Board 9", "boardId": 9, "paramType": "BYTE", "defaultParam": "50", "currentValue": "50", "newValue": null, "minParam": "15", "maxParam": "150", "machineId": 117, "isPrivate": false}, {"id": 79853, "name": "Bubble Detection Threshold - B6", "macro": "EEP_BUBBLE_ROC_THRESHOLD_3", "description": "Rate of change value required to signify a runout. (Default Oat milk = 30, almond/dairy = 50, all other 75+)", "address": 371, "boardName": "Pump Board 9", "boardId": 9, "paramType": "BYTE", "defaultParam": "50", "currentValue": "50", "newValue": null, "minParam": "15", "maxParam": "150", "machineId": 117, "isPrivate": false}, {"id": 79854, "name": "Bubble Detection Threshold - C1", "macro": "EEP_BUBBLE_ROC_THRESHOLD_0", "description": "Rate of change value required to signify a runout. (Default Oat milk = 30, almond/dairy = 50, all other 75+)", "address": 368, "boardName": "Pump Board 0", "boardId": 0, "paramType": "BYTE", "defaultParam": "50", "currentValue": "50", "newValue": null, "minParam": "15", "maxParam": "150", "machineId": 117, "isPrivate": false}, {"id": 79855, "name": "Bubble Detection Threshold - C2", "macro": "EEP_BUBBLE_ROC_THRESHOLD_0", "description": "Rate of change value required to signify a runout. (Default Oat milk = 30, almond/dairy = 50, all other 75+)", "address": 368, "boardName": "Pump Board 1", "boardId": 1, "paramType": "BYTE", "defaultParam": "50", "currentValue": "50", "newValue": "0", "minParam": "15", "maxParam": "150", "machineId": 117, "isPrivate": false}, {"id": 79856, "name": "Bubble Detection Threshold - C3", "macro": "EEP_BUBBLE_ROC_THRESHOLD_0", "description": "Rate of change value required to signify a runout. (Default Oat milk = 30, almond/dairy = 50, all other 75+)", "address": 368, "boardName": "Pump Board 2", "boardId": 2, "paramType": "BYTE", "defaultParam": "50", "currentValue": "50", "newValue": null, "minParam": "15", "maxParam": "150", "machineId": 117, "isPrivate": false}, {"id": 79857, "name": "Bubble Detection Threshold - C4", "macro": "EEP_BUBBLE_ROC_THRESHOLD_0", "description": "Rate of change value required to signify a runout. (Default Oat milk = 30, almond/dairy = 50, all other 75+)", "address": 368, "boardName": "Pump Board 3", "boardId": 3, "paramType": "BYTE", "defaultParam": "50", "currentValue": "50", "newValue": "0", "minParam": "15", "maxParam": "150", "machineId": 117, "isPrivate": false}, {"id": 79858, "name": "Bubble Detection Threshold - C5", "macro": "EEP_BUBBLE_ROC_THRESHOLD_0", "description": "Rate of change value required to signify a runout. (Default Oat milk = 30, almond/dairy = 50, all other 75+)", "address": 368, "boardName": "Pump Board 4", "boardId": 4, "paramType": "BYTE", "defaultParam": "50", "currentValue": "50", "newValue": "0", "minParam": "15", "maxParam": "150", "machineId": 117, "isPrivate": false}, {"id": 79859, "name": "Bubble Detection Threshold - C6", "macro": "EEP_BUBBLE_ROC_THRESHOLD_0", "description": "Rate of change value required to signify a runout. (Default Oat milk = 30, almond/dairy = 50, all other 75+)", "address": 368, "boardName": "Pump Board 5", "boardId": 5, "paramType": "BYTE", "defaultParam": "50", "currentValue": "50", "newValue": "50", "minParam": "15", "maxParam": "150", "machineId": 117, "isPrivate": false}, {"id": 79860, "name": "Bubble Detection Threshold - D1", "macro": "EEP_BUBBLE_ROC_THRESHOLD_1", "description": "Rate of change value required to signify a runout. (Default Oat milk = 30, almond/dairy = 50, all other 75+)", "address": 369, "boardName": "Pump Board 0", "boardId": 0, "paramType": "BYTE", "defaultParam": "50", "currentValue": "50", "newValue": "50", "minParam": "15", "maxParam": "150", "machineId": 117, "isPrivate": false}, {"id": 79861, "name": "Bubble Detection Threshold - D2", "macro": "EEP_BUBBLE_ROC_THRESHOLD_1", "description": "Rate of change value required to signify a runout. (Default Oat milk = 30, almond/dairy = 50, all other 75+)", "address": 369, "boardName": "Pump Board 1", "boardId": 1, "paramType": "BYTE", "defaultParam": "50", "currentValue": "50", "newValue": "50", "minParam": "15", "maxParam": "150", "machineId": 117, "isPrivate": false}, {"id": 79862, "name": "Bubble Detection Threshold - D3", "macro": "EEP_BUBBLE_ROC_THRESHOLD_1", "description": "Rate of change value required to signify a runout. (Default Oat milk = 30, almond/dairy = 50, all other 75+)", "address": 369, "boardName": "Pump Board 2", "boardId": 2, "paramType": "BYTE", "defaultParam": "50", "currentValue": "50", "newValue": "50", "minParam": "15", "maxParam": "150", "machineId": 117, "isPrivate": false}, {"id": 79863, "name": "Bubble Detection Threshold - D4", "macro": "EEP_BUBBLE_ROC_THRESHOLD_1", "description": "Rate of change value required to signify a runout. (Default Oat milk = 30, almond/dairy = 50, all other 75+)", "address": 369, "boardName": "Pump Board 3", "boardId": 3, "paramType": "BYTE", "defaultParam": "50", "currentValue": "50", "newValue": null, "minParam": "15", "maxParam": "150", "machineId": 117, "isPrivate": false}, {"id": 79864, "name": "Bubble Detection Threshold - D5", "macro": "EEP_BUBBLE_ROC_THRESHOLD_1", "description": "Rate of change value required to signify a runout. (Default Oat milk = 30, almond/dairy = 50, all other 75+)", "address": 369, "boardName": "Pump Board 4", "boardId": 4, "paramType": "BYTE", "defaultParam": "50", "currentValue": "50", "newValue": null, "minParam": "15", "maxParam": "150", "machineId": 117, "isPrivate": false}, {"id": 79865, "name": "Bubble Detection Threshold - D6", "macro": "EEP_BUBBLE_ROC_THRESHOLD_1", "description": "Rate of change value required to signify a runout. (Default Oat milk = 30, almond/dairy = 50, all other 75+)", "address": 369, "boardName": "Pump Board 5", "boardId": 5, "paramType": "BYTE", "defaultParam": "50", "currentValue": "50", "newValue": null, "minParam": "15", "maxParam": "150", "machineId": 117, "isPrivate": false}, {"id": 79866, "name": "Bubble Detection Threshold - E1", "macro": "EEP_BUBBLE_ROC_THRESHOLD_2", "description": "Rate of change value required to signify a runout. (Default Oat milk = 30, almond/dairy = 50, all other 75+)", "address": 370, "boardName": "Pump Board 0", "boardId": 0, "paramType": "BYTE", "defaultParam": "50", "currentValue": "50", "newValue": "50", "minParam": "15", "maxParam": "150", "machineId": 117, "isPrivate": false}, {"id": 79867, "name": "Bubble Detection Threshold - E2", "macro": "EEP_BUBBLE_ROC_THRESHOLD_2", "description": "Rate of change value required to signify a runout. (Default Oat milk = 30, almond/dairy = 50, all other 75+)", "address": 370, "boardName": "Pump Board 1", "boardId": 1, "paramType": "BYTE", "defaultParam": "50", "currentValue": "50", "newValue": "0", "minParam": "15", "maxParam": "150", "machineId": 117, "isPrivate": false}, {"id": 79868, "name": "Bubble Detection Threshold - E3", "macro": "EEP_BUBBLE_ROC_THRESHOLD_2", "description": "Rate of change value required to signify a runout. (Default Oat milk = 30, almond/dairy = 50, all other 75+)", "address": 370, "boardName": "Pump Board 2", "boardId": 2, "paramType": "BYTE", "defaultParam": "50", "currentValue": "50", "newValue": "0", "minParam": "15", "maxParam": "150", "machineId": 117, "isPrivate": false}, {"id": 79869, "name": "Bubble Detection Threshold - E4", "macro": "EEP_BUBBLE_ROC_THRESHOLD_2", "description": "Rate of change value required to signify a runout. (Default Oat milk = 30, almond/dairy = 50, all other 75+)", "address": 370, "boardName": "Pump Board 3", "boardId": 3, "paramType": "BYTE", "defaultParam": "50", "currentValue": "50", "newValue": null, "minParam": "15", "maxParam": "150", "machineId": 117, "isPrivate": false}, {"id": 79870, "name": "Bubble Detection Threshold - E5", "macro": "EEP_BUBBLE_ROC_THRESHOLD_2", "description": "Rate of change value required to signify a runout. (Default Oat milk = 30, almond/dairy = 50, all other 75+)", "address": 370, "boardName": "Pump Board 4", "boardId": 4, "paramType": "BYTE", "defaultParam": "50", "currentValue": "50", "newValue": "0", "minParam": "15", "maxParam": "150", "machineId": 117, "isPrivate": false}, {"id": 79871, "name": "Bubble Detection Threshold - E6", "macro": "EEP_BUBBLE_ROC_THRESHOLD_2", "description": "Rate of change value required to signify a runout. (Default Oat milk = 30, almond/dairy = 50, all other 75+)", "address": 370, "boardName": "Pump Board 5", "boardId": 5, "paramType": "BYTE", "defaultParam": "50", "currentValue": "50", "newValue": null, "minParam": "15", "maxParam": "150", "machineId": 117, "isPrivate": false}, {"id": 79872, "name": "Bubble Detection Threshold - F1", "macro": "EEP_BUBBLE_ROC_THRESHOLD_3", "description": "Rate of change value required to signify a runout. (Default Oat milk = 30, almond/dairy = 50, all other 75+)", "address": 371, "boardName": "Pump Board 0", "boardId": 0, "paramType": "BYTE", "defaultParam": "50", "currentValue": "50", "newValue": "50", "minParam": "15", "maxParam": "150", "machineId": 117, "isPrivate": false}, {"id": 79873, "name": "Bubble Detection Threshold - F2", "macro": "EEP_BUBBLE_ROC_THRESHOLD_3", "description": "Rate of change value required to signify a runout. (Default Oat milk = 30, almond/dairy = 50, all other 75+)", "address": 371, "boardName": "Pump Board 1", "boardId": 1, "paramType": "BYTE", "defaultParam": "50", "currentValue": "50", "newValue": null, "minParam": "15", "maxParam": "150", "machineId": 117, "isPrivate": false}, {"id": 79874, "name": "Bubble Detection Threshold - F3", "macro": "EEP_BUBBLE_ROC_THRESHOLD_3", "description": "Rate of change value required to signify a runout. (Default Oat milk = 30, almond/dairy = 50, all other 75+)", "address": 371, "boardName": "Pump Board 2", "boardId": 2, "paramType": "BYTE", "defaultParam": "50", "currentValue": "50", "newValue": null, "minParam": "15", "maxParam": "150", "machineId": 117, "isPrivate": false}, {"id": 79875, "name": "Bubble Detection Threshold - F4", "macro": "EEP_BUBBLE_ROC_THRESHOLD_3", "description": "Rate of change value required to signify a runout. (Default Oat milk = 30, almond/dairy = 50, all other 75+)", "address": 371, "boardName": "Pump Board 3", "boardId": 3, "paramType": "BYTE", "defaultParam": "50", "currentValue": "50", "newValue": "50", "minParam": "15", "maxParam": "150", "machineId": 117, "isPrivate": false}, {"id": 79876, "name": "Bubble Detection Threshold - F5", "macro": "EEP_BUBBLE_ROC_THRESHOLD_3", "description": "Rate of change value required to signify a runout. (Default Oat milk = 30, almond/dairy = 50, all other 75+)", "address": 371, "boardName": "Pump Board 4", "boardId": 4, "paramType": "BYTE", "defaultParam": "50", "currentValue": "50", "newValue": "0", "minParam": "15", "maxParam": "150", "machineId": 117, "isPrivate": false}, {"id": 79877, "name": "Bubble Detection Threshold - F6", "macro": "EEP_BUBBLE_ROC_THRESHOLD_3", "description": "Rate of change value required to signify a runout. (Default Oat milk = 30, almond/dairy = 50, all other 75+)", "address": 371, "boardName": "Pump Board 5", "boardId": 5, "paramType": "BYTE", "defaultParam": "50", "currentValue": "50", "newValue": "0", "minParam": "15", "maxParam": "150", "machineId": 117, "isPrivate": false}, {"id": 79878, "name": "Bubble Detection Threshold - T1", "macro": "EEP_BUBBLE_ROC_THRESHOLD_0", "description": "Rate of change value required to signify a runout. (Default Oat milk = 30, almond/dairy = 50, all other 75+)", "address": 368, "boardName": "Pump Board 13", "boardId": 13, "paramType": "BYTE", "defaultParam": "50", "currentValue": "50", "newValue": null, "minParam": "15", "maxParam": "150", "machineId": 117, "isPrivate": false}, {"id": 79879, "name": "Bubble Detection Threshold - T2", "macro": "EEP_BUBBLE_ROC_THRESHOLD_1", "description": "Rate of change value required to signify a runout. (Default Oat milk = 30, almond/dairy = 50, all other 75+)", "address": 369, "boardName": "Pump Board 13", "boardId": 13, "paramType": "BYTE", "defaultParam": "50", "currentValue": "50", "newValue": null, "minParam": "15", "maxParam": "150", "machineId": 117, "isPrivate": false}, {"id": 79880, "name": "Bubble Detection Threshold - T3", "macro": "EEP_BUBBLE_ROC_THRESHOLD_2", "description": "Rate of change value required to signify a runout. (Default Oat milk = 30, almond/dairy = 50, all other 75+)", "address": 370, "boardName": "Pump Board 13", "boardId": 13, "paramType": "BYTE", "defaultParam": "50", "currentValue": "50", "newValue": null, "minParam": "15", "maxParam": "150", "machineId": 117, "isPrivate": false}, {"id": 79881, "name": "Bubble Detection Threshold - T4", "macro": "EEP_BUBBLE_ROC_THRESHOLD_3", "description": "Rate of change value required to signify a runout. (Default Oat milk = 30, almond/dairy = 50, all other 75+)", "address": 371, "boardName": "Pump Board 13", "boardId": 13, "paramType": "BYTE", "defaultParam": "50", "currentValue": "50", "newValue": null, "minParam": "15", "maxParam": "150", "machineId": 117, "isPrivate": false}, {"id": 79882, "name": "Bubble Detection Threshold - T5", "macro": "EEP_BUBBLE_ROC_THRESHOLD_0", "description": "Rate of change value required to signify a runout. (Default Oat milk = 30, almond/dairy = 50, all other 75+)", "address": 368, "boardName": "Pump Board 14", "boardId": 14, "paramType": "BYTE", "defaultParam": "50", "currentValue": "50", "newValue": null, "minParam": "15", "maxParam": "150", "machineId": 117, "isPrivate": false}, {"id": 79883, "name": "Bubble Detection Threshold - T6", "macro": "EEP_BUBBLE_ROC_THRESHOLD_1", "description": "Rate of change value required to signify a runout. (Default Oat milk = 30, almond/dairy = 50, all other 75+)", "address": 369, "boardName": "Pump Board 14", "boardId": 14, "paramType": "BYTE", "defaultParam": "50", "currentValue": "50", "newValue": null, "minParam": "15", "maxParam": "150", "machineId": 117, "isPrivate": false}, {"id": 79884, "name": "Bubble Detection Threshold - T7", "macro": "EEP_BUBBLE_ROC_THRESHOLD_2", "description": "Rate of change value required to signify a runout. (Default Oat milk = 30, almond/dairy = 50, all other 75+)", "address": 370, "boardName": "Pump Board 14", "boardId": 14, "paramType": "BYTE", "defaultParam": "50", "currentValue": "50", "newValue": null, "minParam": "15", "maxParam": "150", "machineId": 117, "isPrivate": false}, {"id": 79885, "name": "Bubble Detection Threshold - T8", "macro": "EEP_BUBBLE_ROC_THRESHOLD_3", "description": "Rate of change value required to signify a runout. (Default Oat milk = 30, almond/dairy = 50, all other 75+)", "address": 371, "boardName": "Pump Board 14", "boardId": 14, "paramType": "BYTE", "defaultParam": "50", "currentValue": "50", "newValue": null, "minParam": "15", "maxParam": "150", "machineId": 117, "isPrivate": false}, {"id": 79886, "name": "Cleaning Concentrate Ingredient Number", "macro": "EEP_CLEANER_INGR_NUM", "description": "The ingredient number of the cleaner. Currently, this MUST be pump 3 on a pump board. (I.E A6 on pump board board 9, E2 on pump board 2)", "address": 392, "boardName": "Master", "boardId": 65535, "paramType": "BYTE", "defaultParam": "38", "currentValue": "38", "newValue": null, "minParam": "0", "maxParam": "47", "machineId": 117, "isPrivate": false}, {"id": 79887, "name": "Cleaning Concentrate Ratio", "macro": "EEP_CLEANER_RATIO", "description": "The ratio of water to cleaner for internal self cleaning. A value of “0” means that pre-diluted cleaner is being used in place of cleaner concentrate, and there will be no water used for dilution ", "address": 393, "boardName": "Master", "boardId": 65535, "paramType": "BYTE", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "30", "machineId": 117, "isPrivate": false}, {"id": 79888, "name": "Compressor Normal Runtime Max", "macro": "EEP_COMPRESSOR_NORMAL_RT_MAX", "description": "Continuous time that the compressor can be enabled before sending a runtime alert", "address": 208, "boardName": "Cooling", "boardId": 576, "paramType": "WORD", "defaultParam": "43200", "currentValue": "43200", "newValue": null, "minParam": "900", "maxParam": "10800", "machineId": 117, "isPrivate": false}, {"id": 79889, "name": "Compressor Normal Runtime Min", "macro": "EEP_COMPRESSOR_NORMAL_RT_MIN", "description": "Continuous time (in seconds) that the compressor can be disabled before cycling ", "address": 210, "boardName": "Cooling", "boardId": 576, "paramType": "WORD", "defaultParam": "420", "currentValue": "420", "newValue": null, "minParam": "60", "maxParam": "900", "machineId": 117, "isPrivate": false}, {"id": 79890, "name": "Cooling Drawer Switch <PERSON><PERSON>", "macro": "EEP_COOLER_DRAWER_OPEN_ALERT_ENABLE", "description": "Enable alert for cooling drawer switch open", "address": 160, "boardName": "Cooling", "boardId": 576, "paramType": "BOOLEAN", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 117, "isPrivate": false}, {"id": 79891, "name": "Cooling Drawer <PERSON><PERSON> <PERSON><PERSON>", "macro": "EEP_COOLER_DRAWER_OPEN_ALERT_TIME", "description": "Time the cooling drawer switch can remain open until an alert is sent from the cooling board (in seconds)", "address": 176, "boardName": "Cooling", "boardId": 576, "paramType": "WORD", "defaultParam": "600", "currentValue": "600", "newValue": null, "minParam": "300", "maxParam": "1200", "machineId": 117, "isPrivate": false}, {"id": 79892, "name": "Drain Auto Mode Enable", "macro": "EEP_DRAIN_AUTO_ENABLE", "description": "Enable automatic drain pump cycling based on preset ON time and OFF time periods", "address": 112, "boardName": "Cooling", "boardId": 576, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 117, "isPrivate": false}, {"id": 79893, "name": "<PERSON><PERSON>ump OFF Time", "macro": "EEP_DRAIN_OFF_TIME", "description": "Time the drain pump is turned OFF (in seconds)", "address": 96, "boardName": "Cooling", "boardId": 576, "paramType": "WORD", "defaultParam": "540", "currentValue": "540", "newValue": null, "minParam": "10", "maxParam": "10800", "machineId": 117, "isPrivate": false}, {"id": 79894, "name": "<PERSON><PERSON>ump ON Time", "macro": "EEP_DRAIN_ON_TIME", "description": "Time the drain pump is turned ON (in seconds)", "address": 80, "boardName": "Cooling", "boardId": 576, "paramType": "WORD", "defaultParam": "60", "currentValue": "60", "newValue": null, "minParam": "10", "maxParam": "1800", "machineId": 117, "isPrivate": false}, {"id": 79895, "name": "Enable Cooling Board Current Measurements", "macro": "EEP_ENABLE_COOLING_CURRENT_CHECK", "description": "Enable Cooling Board Current Measurements for fans and compressor", "address": 272, "boardName": "Cooling", "boardId": 576, "paramType": "BOOLEAN", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 117, "isPrivate": false}, {"id": 79896, "name": "Enable Water Rinse", "macro": "EEP_ENABLE_WATER_RINSE", "description": "Spray water into the bib during a refresh to clean out the nozzle", "address": 371, "boardName": "Master", "boardId": 65535, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 117, "isPrivate": false}, {"id": 79897, "name": "First Dispense Overpour Quantity - A1, A2, B1, B2", "macro": "EEP_STARTUP_QTY", "description": "Quantity (expressed in 1/20 fl. oz. increments) to overprime this board's ingredients beyond their priming lengths for the first pour of the day", "address": 305, "boardName": "Pump Board 7", "boardId": 7, "paramType": "BYTE", "defaultParam": "10", "currentValue": "10", "newValue": null, "minParam": "0", "maxParam": "100", "machineId": 117, "isPrivate": false}, {"id": 79898, "name": "First Dispense Overpour Quantity - A3, A4, B3, B4", "macro": "EEP_STARTUP_QTY", "description": "Quantity (expressed in 1/20 fl. oz. increments) to overprime this board's ingredients beyond their priming lengths for the first pour of the day", "address": 305, "boardName": "Pump Board 8", "boardId": 8, "paramType": "BYTE", "defaultParam": "10", "currentValue": "10", "newValue": null, "minParam": "0", "maxParam": "100", "machineId": 117, "isPrivate": false}, {"id": 79899, "name": "First Dispense Overpour Quantity - A5, A6, B5, B6", "macro": "EEP_STARTUP_QTY", "description": "Quantity (expressed in 1/20 fl. oz. increments) to overprime this board's ingredients beyond their priming lengths for the first pour of the day", "address": 305, "boardName": "Pump Board 9", "boardId": 9, "paramType": "BYTE", "defaultParam": "10", "currentValue": "10", "newValue": null, "minParam": "0", "maxParam": "100", "machineId": 117, "isPrivate": false}, {"id": 79900, "name": "First Dispense Overpour Quantity - C1, D1, E1, F1", "macro": "EEP_STARTUP_QTY", "description": "Quantity (expressed in 1/20 fl. oz. increments) to overprime this board's ingredients beyond their priming lengths for the first pour of the day", "address": 305, "boardName": "Pump Board 0", "boardId": 0, "paramType": "BYTE", "defaultParam": "10", "currentValue": "10", "newValue": null, "minParam": "0", "maxParam": "100", "machineId": 117, "isPrivate": false}, {"id": 79901, "name": "First Dispense Overpour Quantity - C2, E2, D2, F2", "macro": "EEP_STARTUP_QTY", "description": "Quantity (expressed in 1/20 fl. oz. increments) to overprime this board's ingredients beyond their priming lengths for the first pour of the day", "address": 305, "boardName": "Pump Board 1", "boardId": 1, "paramType": "BYTE", "defaultParam": "10", "currentValue": "10", "newValue": null, "minParam": "0", "maxParam": "100", "machineId": 117, "isPrivate": false}, {"id": 79902, "name": "First Dispense Overpour Quantity - C3, D3, E3, F3", "macro": "EEP_STARTUP_QTY", "description": "Quantity (expressed in 1/20 fl. oz. increments) to overprime this board's ingredients beyond their priming lengths for the first pour of the day", "address": 305, "boardName": "Pump Board 2", "boardId": 2, "paramType": "BYTE", "defaultParam": "10", "currentValue": "10", "newValue": null, "minParam": "0", "maxParam": "100", "machineId": 117, "isPrivate": false}, {"id": 79903, "name": "First Dispense Overpour Quantity - C4, D4, E4, F4", "macro": "EEP_STARTUP_QTY", "description": "Quantity (expressed in 1/20 fl. oz. increments) to overprime this board's ingredients beyond their priming lengths for the first pour of the day", "address": 305, "boardName": "Pump Board 3", "boardId": 3, "paramType": "BYTE", "defaultParam": "10", "currentValue": "10", "newValue": null, "minParam": "0", "maxParam": "100", "machineId": 117, "isPrivate": false}, {"id": 79904, "name": "First Dispense Overpour Quantity - C5, D5, E5, F5", "macro": "EEP_STARTUP_QTY", "description": "Quantity (expressed in 1/20 fl. oz. increments) to overprime this board's ingredients beyond their priming lengths for the first pour of the day", "address": 305, "boardName": "Pump Board 4", "boardId": 4, "paramType": "BYTE", "defaultParam": "10", "currentValue": "10", "newValue": null, "minParam": "0", "maxParam": "100", "machineId": 117, "isPrivate": false}, {"id": 79905, "name": "First Dispense Overpour Quantity - C6, D6, E6, F6", "macro": "EEP_STARTUP_QTY", "description": "Quantity (expressed in 1/20 fl. oz. increments) to overprime this board's ingredients beyond their priming lengths for the first pour of the day", "address": 305, "boardName": "Pump Board 5", "boardId": 5, "paramType": "BYTE", "defaultParam": "10", "currentValue": "10", "newValue": null, "minParam": "0", "maxParam": "100", "machineId": 117, "isPrivate": false}, {"id": 79906, "name": "First Dispense Overpour Quantity - T1 - T4", "macro": "EEP_STARTUP_QTY", "description": "Quantity (expressed in 1/20 fl. oz. increments) to overprime this board's ingredients beyond their priming lengths for the first pour of the day", "address": 305, "boardName": "Pump Board 13", "boardId": 13, "paramType": "BYTE", "defaultParam": "10", "currentValue": "10", "newValue": null, "minParam": "0", "maxParam": "100", "machineId": 117, "isPrivate": false}, {"id": 79907, "name": "First Dispense Overpour Quantity - T5 - T8", "macro": "EEP_STARTUP_QTY", "description": "Quantity (expressed in 1/20 fl. oz. increments) to overprime this board's ingredients beyond their priming lengths for the first pour of the day", "address": 305, "boardName": "Pump Board 14", "boardId": 14, "paramType": "BYTE", "defaultParam": "10", "currentValue": "10", "newValue": null, "minParam": "0", "maxParam": "100", "machineId": 117, "isPrivate": false}, {"id": 79908, "name": "<PERSON><PERSON> Defrost Timeout", "macro": "EEP_DEFROST_TIMEOUT", "description": "Time that the compressor is forced to stay off during a defrost cycle (in seconds)", "address": 240, "boardName": "Cooling", "boardId": 576, "paramType": "WORD", "defaultParam": "3600", "currentValue": "3600", "newValue": null, "minParam": "1800", "maxParam": "43200", "machineId": 117, "isPrivate": false}, {"id": 79909, "name": "Global Cleaner Rinse Speed", "macro": "EEP_GLOBAL_CLEANER_RINSE_SPEED", "description": "The cleaning rate (in oz/min) that cleaner is rinsed during a self clean", "address": 388, "boardName": "Master", "boardId": 65535, "paramType": "SINGLE", "defaultParam": "15.0", "currentValue": "15.0", "newValue": null, "minParam": "5", "maxParam": "37.5", "machineId": 117, "isPrivate": false}, {"id": 79910, "name": "Global Ingredient Sensor Active Threshold", "macro": "EEP_LEVEL_THRESHOLD_GLOABL", "description": "Ingredient level (expressed in 1/20 fl. oz. increments) below which runout detection becomes active for ALL ingredients", "address": 338, "boardName": "Master", "boardId": 65535, "paramType": "WORD", "defaultParam": "100", "currentValue": "100", "newValue": null, "minParam": "10", "maxParam": "3500", "machineId": 117, "isPrivate": false}, {"id": 79911, "name": "Global Water Rinse Speed", "macro": "EEP_GLOBAL_WATER_RINSE_SPEED", "description": "The cleaning rate (in oz/min) that water is rinsed during a self clean", "address": 384, "boardName": "Master", "boardId": 65535, "paramType": "SINGLE", "defaultParam": "35.0", "currentValue": "35.0", "newValue": null, "minParam": "5", "maxParam": "37.5", "machineId": 117, "isPrivate": false}, {"id": 79912, "name": "Ingredient Line Status Enable (A1, B1, A2, B2)", "macro": "EEP_INGR_LINE_STATUS_EN", "description": "Enable ingredient prime status of pumped ingredients", "address": 436, "boardName": "Pump Board 7", "boardId": 7, "paramType": "BOOLEAN", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 117, "isPrivate": false}, {"id": 79913, "name": "Ingredient Line Status Enable (A3, B3, A4, B4)", "macro": "EEP_INGR_LINE_STATUS_EN", "description": "Enable ingredient prime status of pumped ingredients", "address": 436, "boardName": "Pump Board 8", "boardId": 8, "paramType": "BOOLEAN", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 117, "isPrivate": false}, {"id": 79914, "name": "Ingredient Line Status Enable (A5, B5, A6, B6)", "macro": "EEP_INGR_LINE_STATUS_EN", "description": "Enable ingredient prime status of pumped ingredients", "address": 436, "boardName": "Pump Board 9", "boardId": 9, "paramType": "BOOLEAN", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 117, "isPrivate": false}, {"id": 79915, "name": "Ingredient Line Status Enable (C1, D1, E1, F1)", "macro": "EEP_INGR_LINE_STATUS_EN", "description": "Enable ingredient prime status of pumped ingredients", "address": 436, "boardName": "Pump Board 0", "boardId": 0, "paramType": "BOOLEAN", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 117, "isPrivate": false}, {"id": 79916, "name": "Ingredient Line Status Enable (C2, D2, E2, F2)", "macro": "EEP_INGR_LINE_STATUS_EN", "description": "Enable ingredient prime status of pumped ingredients", "address": 436, "boardName": "Pump Board 1", "boardId": 1, "paramType": "BOOLEAN", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 117, "isPrivate": false}, {"id": 79917, "name": "Ingredient Line Status Enable (C3, D3, E3, F3)", "macro": "EEP_INGR_LINE_STATUS_EN", "description": "Enable ingredient prime status of pumped ingredients", "address": 436, "boardName": "Pump Board 2", "boardId": 2, "paramType": "BOOLEAN", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 117, "isPrivate": false}, {"id": 79918, "name": "Ingredient Line Status Enable (C4, D4, E4, F4)", "macro": "EEP_INGR_LINE_STATUS_EN", "description": "Enable ingredient prime status of pumped ingredients", "address": 436, "boardName": "Pump Board 3", "boardId": 3, "paramType": "BOOLEAN", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 117, "isPrivate": false}, {"id": 79919, "name": "Ingredient Line Status Enable (C5, D5, E5, F5)", "macro": "EEP_INGR_LINE_STATUS_EN", "description": "Enable ingredient prime status of pumped ingredients", "address": 436, "boardName": "Pump Board 4", "boardId": 4, "paramType": "BOOLEAN", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 117, "isPrivate": false}, {"id": 79920, "name": "Ingredient Line Status Enable (C6, D6, E6, F6)", "macro": "EEP_INGR_LINE_STATUS_EN", "description": "Enable ingredient prime status of pumped ingredients", "address": 436, "boardName": "Pump Board 5", "boardId": 5, "paramType": "BOOLEAN", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 117, "isPrivate": false}, {"id": 79921, "name": "Ingredient Line Status Enable (T1 - T4)", "macro": "EEP_INGR_LINE_STATUS_EN", "description": "Enable ingredient prime status of pumped ingredients", "address": 436, "boardName": "Pump Board 13", "boardId": 13, "paramType": "BOOLEAN", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 117, "isPrivate": false}, {"id": 79922, "name": "Ingredient Line Status Enable (T5 - T8)", "macro": "EEP_INGR_LINE_STATUS_EN", "description": "Enable ingredient prime status of pumped ingredients", "address": 436, "boardName": "Pump Board 14", "boardId": 14, "paramType": "BOOLEAN", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 117, "isPrivate": false}, {"id": 79923, "name": "Ingredient Mixing Enable - A1", "macro": "EEP_MIXING_ENABLE_28", "description": "Enable ingredient mixing for A1", "address": 124, "boardName": "Master", "boardId": 65535, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 117, "isPrivate": false}, {"id": 79924, "name": "Ingredient Mixing Enable - A2", "macro": "EEP_MIXING_ENABLE_30", "description": "Enable ingredient mixing for A2", "address": 126, "boardName": "Master", "boardId": 65535, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 117, "isPrivate": false}, {"id": 79925, "name": "Ingredient Mixing Enable - A3", "macro": "EEP_MIXING_ENABLE_32", "description": "Enable ingredient mixing for A3", "address": 128, "boardName": "Master", "boardId": 65535, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 117, "isPrivate": false}, {"id": 79926, "name": "Ingredient Mixing Enable - A4", "macro": "EEP_MIXING_ENABLE_34", "description": "Enable ingredient mixing for A4", "address": 130, "boardName": "Master", "boardId": 65535, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 117, "isPrivate": false}, {"id": 79927, "name": "Ingredient Mixing Enable - A5", "macro": "EEP_MIXING_ENABLE_36", "description": "Enable ingredient mixing for A5", "address": 132, "boardName": "Master", "boardId": 65535, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 117, "isPrivate": false}, {"id": 79928, "name": "Ingredient Mixing Enable - A6", "macro": "EEP_MIXING_ENABLE_38", "description": "Enable ingredient mixing for A6", "address": 134, "boardName": "Master", "boardId": 65535, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 117, "isPrivate": false}, {"id": 79929, "name": "Ingredient Mixing Enable - B1", "macro": "EEP_MIXING_ENABLE_29", "description": "Enable ingredient mixing for B1", "address": 125, "boardName": "Master", "boardId": 65535, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 117, "isPrivate": false}, {"id": 79930, "name": "Ingredient Mixing Enable - B2", "macro": "EEP_MIXING_ENABLE_31", "description": "Enable ingredient mixing for B2", "address": 127, "boardName": "Master", "boardId": 65535, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 117, "isPrivate": false}, {"id": 79931, "name": "Ingredient Mixing Enable - B3", "macro": "EEP_MIXING_ENABLE_33", "description": "Enable ingredient mixing for B3", "address": 129, "boardName": "Master", "boardId": 65535, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 117, "isPrivate": false}, {"id": 79932, "name": "Ingredient Mixing Enable - B4", "macro": "EEP_MIXING_ENABLE_35", "description": "Enable ingredient mixing for B4", "address": 131, "boardName": "Master", "boardId": 65535, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 117, "isPrivate": false}, {"id": 79933, "name": "Ingredient Mixing Enable - B5", "macro": "EEP_MIXING_ENABLE_37", "description": "Enable ingredient mixing for B5", "address": 133, "boardName": "Master", "boardId": 65535, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 117, "isPrivate": false}, {"id": 79934, "name": "Ingredient Mixing Enable - B6", "macro": "EEP_MIXING_ENABLE_39", "description": "Enable ingredient mixing for B6", "address": 135, "boardName": "Master", "boardId": 65535, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 117, "isPrivate": false}, {"id": 79935, "name": "Ingredient Mixing Period", "macro": "EEP_MIXING_PERIOD", "description": "Time (in seconds) between house mix mixing/agitation operations", "address": 32, "boardName": "Master", "boardId": 65535, "paramType": "WORD", "defaultParam": "600", "currentValue": "600", "newValue": null, "minParam": "60", "maxParam": "3600", "machineId": 117, "isPrivate": false}, {"id": 79936, "name": "Ingredient Sensor Enable - A1, A2, B1, B2", "macro": "EEP_BUBBLE_DETECT_ENABLE", "description": "Enable the use of ingredient detection sensors for a group of 4 house mix ingredients", "address": 160, "boardName": "Pump Board 7", "boardId": 7, "paramType": "BOOLEAN", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 117, "isPrivate": false}, {"id": 79937, "name": "Ingredient Sensor Enable - A3, A4, B3, B4", "macro": "EEP_BUBBLE_DETECT_ENABLE", "description": "Enable the use of ingredient detection sensors for a group of 4 house mix ingredients", "address": 160, "boardName": "Pump Board 8", "boardId": 8, "paramType": "BOOLEAN", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 117, "isPrivate": false}, {"id": 79938, "name": "Ingredient Sensor Enable - A5, A6, B5, B6", "macro": "EEP_BUBBLE_DETECT_ENABLE", "description": "Enable the use of ingredient detection sensors for a group of 4 house mix ingredients", "address": 160, "boardName": "Pump Board 9", "boardId": 9, "paramType": "BOOLEAN", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 117, "isPrivate": false}, {"id": 79939, "name": "Ingredient Sensor Enable - C1,D1,E1,F1", "macro": "EEP_BUBBLE_DETECT_ENABLE", "description": "Enable the use of ingredient detection sensors for an entire column of ingredients", "address": 160, "boardName": "Pump Board 0", "boardId": 0, "paramType": "BOOLEAN", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 117, "isPrivate": false}, {"id": 79940, "name": "Ingredient Sensor Enable - C2, E2, D2, F2", "macro": "EEP_BUBBLE_DETECT_ENABLE", "description": "Enable the use of ingredient detection sensors for an entire column of ingredients", "address": 160, "boardName": "Pump Board 1", "boardId": 1, "paramType": "BOOLEAN", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 117, "isPrivate": false}, {"id": 79941, "name": "Ingredient Sensor Enable - C3, D3, E3, F3", "macro": "EEP_BUBBLE_DETECT_ENABLE", "description": "Enable the use of ingredient detection sensors for an entire column of ingredients", "address": 160, "boardName": "Pump Board 2", "boardId": 2, "paramType": "BOOLEAN", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 117, "isPrivate": false}, {"id": 79942, "name": "Ingredient Sensor Enable - C4, D4, E4, F4", "macro": "EEP_BUBBLE_DETECT_ENABLE", "description": "Enable the use of ingredient detection sensors for an entire column of ingredients", "address": 160, "boardName": "Pump Board 3", "boardId": 3, "paramType": "BOOLEAN", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 117, "isPrivate": false}, {"id": 79943, "name": "Ingredient Sensor Enable - C5, D5, E5, F5", "macro": "EEP_BUBBLE_DETECT_ENABLE", "description": "Enable the use of ingredient detection sensors for an entire column of ingredients", "address": 160, "boardName": "Pump Board 4", "boardId": 4, "paramType": "BOOLEAN", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 117, "isPrivate": false}, {"id": 79944, "name": "Ingredient Sensor Enable - C6, D6, E6, F6", "macro": "EEP_BUBBLE_DETECT_ENABLE", "description": "Enable the use of ingredient detection sensors for an entire column of ingredients", "address": 160, "boardName": "Pump Board 5", "boardId": 5, "paramType": "BOOLEAN", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 117, "isPrivate": false}, {"id": 79945, "name": "Ingredient Sensor Enable - T1 - T4", "macro": "EEP_BUBBLE_DETECT_ENABLE", "description": "Enable the use of ingredient detection sensors for a group of 4 house mix ingredients", "address": 160, "boardName": "Pump Board 13", "boardId": 13, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 117, "isPrivate": false}, {"id": 79946, "name": "Ingredient Sensor Enable - T5 - T8", "macro": "EEP_BUBBLE_DETECT_ENABLE", "description": "Enable the use of ingredient detection sensors for a group of 4 house mix ingredients", "address": 160, "boardName": "Pump Board 14", "boardId": 14, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 117, "isPrivate": false}, {"id": 79947, "name": "Leave ALL Ingredients Unprimed at Startup", "macro": "EEP_ENABLE_UNPRIMED_STARTUP", "description": "At startup, leave ALL lines that were cleaned unprimed in order to save as much ingredient as possible throughout the day. This causes first dispenses of the day to be SLOW! Use with Caution.", "address": 370, "boardName": "Master", "boardId": 65535, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 117, "isPrivate": false}, {"id": 79948, "name": "Lower Temperature Threshold (Bottom Temp Sensor)", "macro": "EEP_LOWER_TEMP_THRESHOLD_BOT", "description": "Temperature of the bottom fridge sensor (in degrees Fahrenheit) below which the compressor is turned OFF", "address": 228, "boardName": "Cooling", "boardId": 576, "paramType": "SINGLE", "defaultParam": "36", "currentValue": "36", "newValue": null, "minParam": "32", "maxParam": "46", "machineId": 117, "isPrivate": false}, {"id": 79949, "name": "Lower Temperature Threshold (Top Temp Sensor)", "macro": "EEP_LOWER_TEMP_THRESHOLD", "description": "Temperature of the top fridge sensor (in degrees Fahrenheit) below which the compressor is turned OFF", "address": 32, "boardName": "Cooling", "boardId": 576, "paramType": "SINGLE", "defaultParam": "36", "currentValue": "36", "newValue": null, "minParam": "32", "maxParam": "46", "machineId": 117, "isPrivate": false}, {"id": 79950, "name": "<PERSON>/<PERSON> Ingredient Speed - A1", "macro": "EEP_OZMIN_INGR_SPEED_0", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 400, "boardName": "Pump Board 7", "boardId": 7, "paramType": "SINGLE", "defaultParam": "20", "currentValue": "20", "newValue": null, "minParam": "7.5", "maxParam": "37.5", "machineId": 117, "isPrivate": false}, {"id": 79951, "name": "<PERSON>/Min Ingredient Speed - A2", "macro": "EEP_OZMIN_INGR_SPEED_2", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 408, "boardName": "Pump Board 7", "boardId": 7, "paramType": "SINGLE", "defaultParam": "20", "currentValue": "20", "newValue": "15", "minParam": "7.5", "maxParam": "37.5", "machineId": 117, "isPrivate": false}, {"id": 79952, "name": "Oz/Min Ingredient Speed - A3", "macro": "EEP_OZMIN_INGR_SPEED_0", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 400, "boardName": "Pump Board 8", "boardId": 8, "paramType": "SINGLE", "defaultParam": "20", "currentValue": "20", "newValue": "15", "minParam": "7.5", "maxParam": "37.5", "machineId": 117, "isPrivate": false}, {"id": 79953, "name": "Oz/Min Ingredient Speed - A4", "macro": "EEP_OZMIN_INGR_SPEED_2", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 408, "boardName": "Pump Board 8", "boardId": 8, "paramType": "SINGLE", "defaultParam": "20", "currentValue": "20", "newValue": "15", "minParam": "7.5", "maxParam": "37.5", "machineId": 117, "isPrivate": false}, {"id": 79954, "name": "Oz/Min Ingredient Speed - A5", "macro": "EEP_OZMIN_INGR_SPEED_0", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 400, "boardName": "Pump Board 9", "boardId": 9, "paramType": "SINGLE", "defaultParam": "20", "currentValue": "20", "newValue": "15", "minParam": "7.5", "maxParam": "37.5", "machineId": 117, "isPrivate": false}, {"id": 79955, "name": "Oz/Min Ingredient Speed - A6", "macro": "EEP_OZMIN_INGR_SPEED_2", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 408, "boardName": "Pump Board 9", "boardId": 9, "paramType": "SINGLE", "defaultParam": "20", "currentValue": "20", "newValue": "15", "minParam": "7.5", "maxParam": "37.5", "machineId": 117, "isPrivate": false}, {"id": 79956, "name": "Oz/Min Ingredient Speed - B1", "macro": "EEP_OZMIN_INGR_SPEED_1", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 404, "boardName": "Pump Board 7", "boardId": 7, "paramType": "SINGLE", "defaultParam": "20", "currentValue": "20", "newValue": "15", "minParam": "7.5", "maxParam": "37.5", "machineId": 117, "isPrivate": false}, {"id": 79957, "name": "Oz/Min Ingredient Speed - B2", "macro": "EEP_OZMIN_INGR_SPEED_3", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 412, "boardName": "Pump Board 7", "boardId": 7, "paramType": "SINGLE", "defaultParam": "20", "currentValue": "20", "newValue": "15", "minParam": "7.5", "maxParam": "37.5", "machineId": 117, "isPrivate": false}, {"id": 79958, "name": "Oz/Min Ingredient Speed - B3", "macro": "EEP_OZMIN_INGR_SPEED_1", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 404, "boardName": "Pump Board 8", "boardId": 8, "paramType": "SINGLE", "defaultParam": "20", "currentValue": "20", "newValue": "15", "minParam": "7.5", "maxParam": "37.5", "machineId": 117, "isPrivate": false}, {"id": 79959, "name": "Oz/Min Ingredient Speed - B4", "macro": "EEP_OZMIN_INGR_SPEED_3", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 412, "boardName": "Pump Board 8", "boardId": 8, "paramType": "SINGLE", "defaultParam": "20", "currentValue": "20", "newValue": "15", "minParam": "7.5", "maxParam": "37.5", "machineId": 117, "isPrivate": false}, {"id": 79960, "name": "Oz/Min Ingredient Speed - B5", "macro": "EEP_OZMIN_INGR_SPEED_1", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 404, "boardName": "Pump Board 9", "boardId": 9, "paramType": "SINGLE", "defaultParam": "20", "currentValue": "20", "newValue": null, "minParam": "7.5", "maxParam": "37.5", "machineId": 117, "isPrivate": false}, {"id": 79961, "name": "Oz/Min Ingredient Speed - B6", "macro": "EEP_OZMIN_INGR_SPEED_3", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 412, "boardName": "Pump Board 9", "boardId": 9, "paramType": "SINGLE", "defaultParam": "20", "currentValue": "20", "newValue": null, "minParam": "7.5", "maxParam": "37.5", "machineId": 117, "isPrivate": false}, {"id": 79962, "name": "Oz/Min Ingredient Speed - C1", "macro": "EEP_OZMIN_INGR_SPEED_0", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 400, "boardName": "Pump Board 0", "boardId": 0, "paramType": "SINGLE", "defaultParam": "7.5", "currentValue": "7.5", "newValue": null, "minParam": "7.5", "maxParam": "37.5", "machineId": 117, "isPrivate": false}, {"id": 79963, "name": "Oz/Min Ingredient Speed - C2", "macro": "EEP_OZMIN_INGR_SPEED_0", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 400, "boardName": "Pump Board 1", "boardId": 1, "paramType": "SINGLE", "defaultParam": "20", "currentValue": "20", "newValue": "0", "minParam": "7.5", "maxParam": "37.5", "machineId": 117, "isPrivate": false}, {"id": 79964, "name": "Oz/Min Ingredient Speed - C3", "macro": "EEP_OZMIN_INGR_SPEED_0", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 400, "boardName": "Pump Board 2", "boardId": 2, "paramType": "SINGLE", "defaultParam": "20", "currentValue": "20", "newValue": null, "minParam": "7.5", "maxParam": "37.5", "machineId": 117, "isPrivate": false}, {"id": 79965, "name": "Oz/Min Ingredient Speed - C4", "macro": "EEP_OZMIN_INGR_SPEED_0", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 400, "boardName": "Pump Board 3", "boardId": 3, "paramType": "SINGLE", "defaultParam": "20", "currentValue": "20", "newValue": "0", "minParam": "7.5", "maxParam": "37.5", "machineId": 117, "isPrivate": false}, {"id": 79966, "name": "Oz/Min Ingredient Speed - C5", "macro": "EEP_OZMIN_INGR_SPEED_0", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 400, "boardName": "Pump Board 4", "boardId": 4, "paramType": "SINGLE", "defaultParam": "20", "currentValue": "20", "newValue": "0", "minParam": "7.5", "maxParam": "37.5", "machineId": 117, "isPrivate": false}, {"id": 79967, "name": "Oz/Min Ingredient Speed - C6", "macro": "EEP_OZMIN_INGR_SPEED_0", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 400, "boardName": "Pump Board 5", "boardId": 5, "paramType": "SINGLE", "defaultParam": "20", "currentValue": "20", "newValue": "15", "minParam": "7.5", "maxParam": "37.5", "machineId": 117, "isPrivate": false}, {"id": 79968, "name": "Oz/Min Ingredient Speed - D1", "macro": "EEP_OZMIN_INGR_SPEED_1", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 404, "boardName": "Pump Board 0", "boardId": 0, "paramType": "SINGLE", "defaultParam": "7.5", "currentValue": "7.5", "newValue": "15", "minParam": "7.5", "maxParam": "37.5", "machineId": 117, "isPrivate": false}, {"id": 79969, "name": "Oz/Min Ingredient Speed - D2", "macro": "EEP_OZMIN_INGR_SPEED_1", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 404, "boardName": "Pump Board 1", "boardId": 1, "paramType": "SINGLE", "defaultParam": "20", "currentValue": "20", "newValue": "15", "minParam": "7.5", "maxParam": "37.5", "machineId": 117, "isPrivate": false}, {"id": 79970, "name": "Oz/Min Ingredient Speed - D3", "macro": "EEP_OZMIN_INGR_SPEED_1", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 404, "boardName": "Pump Board 2", "boardId": 2, "paramType": "SINGLE", "defaultParam": "20", "currentValue": "20", "newValue": "15", "minParam": "7.5", "maxParam": "37.5", "machineId": 117, "isPrivate": false}, {"id": 79971, "name": "Oz/Min Ingredient Speed - D4", "macro": "EEP_OZMIN_INGR_SPEED_1", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 404, "boardName": "Pump Board 3", "boardId": 3, "paramType": "SINGLE", "defaultParam": "20", "currentValue": "20", "newValue": null, "minParam": "7.5", "maxParam": "37.5", "machineId": 117, "isPrivate": false}, {"id": 79972, "name": "Oz/Min Ingredient Speed - D5", "macro": "EEP_OZMIN_INGR_SPEED_1", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 404, "boardName": "Pump Board 4", "boardId": 4, "paramType": "SINGLE", "defaultParam": "20", "currentValue": "20", "newValue": null, "minParam": "7.5", "maxParam": "37.5", "machineId": 117, "isPrivate": false}, {"id": 79973, "name": "Oz/Min Ingredient Speed - D6", "macro": "EEP_OZMIN_INGR_SPEED_1", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 404, "boardName": "Pump Board 5", "boardId": 5, "paramType": "SINGLE", "defaultParam": "20", "currentValue": "20", "newValue": null, "minParam": "7.5", "maxParam": "37.5", "machineId": 117, "isPrivate": false}, {"id": 79974, "name": "Oz/Min Ingredient Speed - E1", "macro": "EEP_OZMIN_INGR_SPEED_2", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 408, "boardName": "Pump Board 0", "boardId": 0, "paramType": "SINGLE", "defaultParam": "7.5", "currentValue": "7.5", "newValue": "15", "minParam": "7.5", "maxParam": "37.5", "machineId": 117, "isPrivate": false}, {"id": 79975, "name": "Oz/Min Ingredient Speed - E2", "macro": "EEP_OZMIN_INGR_SPEED_2", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 408, "boardName": "Pump Board 1", "boardId": 1, "paramType": "SINGLE", "defaultParam": "20", "currentValue": "20", "newValue": "0", "minParam": "7.5", "maxParam": "37.5", "machineId": 117, "isPrivate": false}, {"id": 79976, "name": "Oz/Min Ingredient Speed - E3", "macro": "EEP_OZMIN_INGR_SPEED_2", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 408, "boardName": "Pump Board 2", "boardId": 2, "paramType": "SINGLE", "defaultParam": "20", "currentValue": "20", "newValue": "0", "minParam": "7.5", "maxParam": "37.5", "machineId": 117, "isPrivate": false}, {"id": 79977, "name": "Oz/Min Ingredient Speed - E4", "macro": "EEP_OZMIN_INGR_SPEED_2", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 408, "boardName": "Pump Board 3", "boardId": 3, "paramType": "SINGLE", "defaultParam": "20", "currentValue": "20", "newValue": null, "minParam": "7.5", "maxParam": "37.5", "machineId": 117, "isPrivate": false}, {"id": 79978, "name": "Oz/Min Ingredient Speed - E5", "macro": "EEP_OZMIN_INGR_SPEED_2", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 408, "boardName": "Pump Board 4", "boardId": 4, "paramType": "SINGLE", "defaultParam": "20", "currentValue": "20", "newValue": "0", "minParam": "7.5", "maxParam": "37.5", "machineId": 117, "isPrivate": false}, {"id": 79979, "name": "Oz/Min Ingredient Speed - E6", "macro": "EEP_OZMIN_INGR_SPEED_2", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 408, "boardName": "Pump Board 5", "boardId": 5, "paramType": "SINGLE", "defaultParam": "20", "currentValue": "20", "newValue": null, "minParam": "7.5", "maxParam": "37.5", "machineId": 117, "isPrivate": false}, {"id": 79980, "name": "<PERSON>/<PERSON> Ingredient Speed - F1", "macro": "EEP_OZMIN_INGR_SPEED_3", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 412, "boardName": "Pump Board 0", "boardId": 0, "paramType": "SINGLE", "defaultParam": "7.5", "currentValue": "7.5", "newValue": "15", "minParam": "7.5", "maxParam": "37.5", "machineId": 117, "isPrivate": false}, {"id": 79981, "name": "Oz/Min Ingredient Speed - F2", "macro": "EEP_OZMIN_INGR_SPEED_3", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 412, "boardName": "Pump Board 1", "boardId": 1, "paramType": "SINGLE", "defaultParam": "20", "currentValue": "20", "newValue": null, "minParam": "7.5", "maxParam": "37.5", "machineId": 117, "isPrivate": false}, {"id": 79982, "name": "Oz/Min Ingredient Speed - F3", "macro": "EEP_OZMIN_INGR_SPEED_3", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 412, "boardName": "Pump Board 2", "boardId": 2, "paramType": "SINGLE", "defaultParam": "20", "currentValue": "20", "newValue": null, "minParam": "7.5", "maxParam": "37.5", "machineId": 117, "isPrivate": false}, {"id": 79983, "name": "Oz/Min Ingredient Speed - F4", "macro": "EEP_OZMIN_INGR_SPEED_3", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 412, "boardName": "Pump Board 3", "boardId": 3, "paramType": "SINGLE", "defaultParam": "20", "currentValue": "20", "newValue": "15", "minParam": "7.5", "maxParam": "37.5", "machineId": 117, "isPrivate": false}, {"id": 79984, "name": "Oz/Min Ingredient Speed - F5", "macro": "EEP_OZMIN_INGR_SPEED_3", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 412, "boardName": "Pump Board 4", "boardId": 4, "paramType": "SINGLE", "defaultParam": "20", "currentValue": "20", "newValue": "0", "minParam": "7.5", "maxParam": "37.5", "machineId": 117, "isPrivate": false}, {"id": 79985, "name": "Oz/Min Ingredient Speed - F6", "macro": "EEP_OZMIN_INGR_SPEED_3", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 412, "boardName": "Pump Board 5", "boardId": 5, "paramType": "SINGLE", "defaultParam": "20", "currentValue": "20", "newValue": "0", "minParam": "7.5", "maxParam": "37.5", "machineId": 117, "isPrivate": false}, {"id": 79986, "name": "Oz/Min Ingredient Speed - T1", "macro": "EEP_OZMIN_INGR_SPEED_0", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 400, "boardName": "Pump Board 13", "boardId": 13, "paramType": "SINGLE", "defaultParam": "8.0", "currentValue": "8.0", "newValue": null, "minParam": "7.5", "maxParam": "37.5", "machineId": 117, "isPrivate": false}, {"id": 79987, "name": "Oz/Min Ingredient Speed - T2", "macro": "EEP_OZMIN_INGR_SPEED_1", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 404, "boardName": "Pump Board 13", "boardId": 13, "paramType": "SINGLE", "defaultParam": "8.0", "currentValue": "8.0", "newValue": null, "minParam": "7.5", "maxParam": "37.5", "machineId": 117, "isPrivate": false}, {"id": 79988, "name": "Oz/Min Ingredient Speed - T3", "macro": "EEP_OZMIN_INGR_SPEED_2", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 408, "boardName": "Pump Board 13", "boardId": 13, "paramType": "SINGLE", "defaultParam": "8.0", "currentValue": "8.0", "newValue": null, "minParam": "7.5", "maxParam": "37.5", "machineId": 117, "isPrivate": false}, {"id": 79989, "name": "Oz/Min Ingredient Speed - T4", "macro": "EEP_OZMIN_INGR_SPEED_3", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 412, "boardName": "Pump Board 13", "boardId": 13, "paramType": "SINGLE", "defaultParam": "8.0", "currentValue": "8.0", "newValue": null, "minParam": "7.5", "maxParam": "37.5", "machineId": 117, "isPrivate": false}, {"id": 79990, "name": "Oz/Min Ingredient Speed - T5", "macro": "EEP_OZMIN_INGR_SPEED_0", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 400, "boardName": "Pump Board 14", "boardId": 14, "paramType": "SINGLE", "defaultParam": "8", "currentValue": "8", "newValue": null, "minParam": "7.5", "maxParam": "37.5", "machineId": 117, "isPrivate": false}, {"id": 79991, "name": "Oz/Min Ingredient Speed - T6", "macro": "EEP_OZMIN_INGR_SPEED_1", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 404, "boardName": "Pump Board 14", "boardId": 14, "paramType": "SINGLE", "defaultParam": "8.0", "currentValue": "8.0", "newValue": null, "minParam": "7.5", "maxParam": "37.5", "machineId": 117, "isPrivate": false}, {"id": 79992, "name": "Oz/Min Ingredient Speed - T7", "macro": "EEP_OZMIN_INGR_SPEED_2", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 408, "boardName": "Pump Board 14", "boardId": 14, "paramType": "SINGLE", "defaultParam": "22.5", "currentValue": "8", "newValue": null, "minParam": "7.5", "maxParam": "37.5", "machineId": 117, "isPrivate": false}, {"id": 79993, "name": "Oz/Min Ingredient Speed - T8", "macro": "EEP_OZMIN_INGR_SPEED_3", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 412, "boardName": "Pump Board 14", "boardId": 14, "paramType": "SINGLE", "defaultParam": "22.5", "currentValue": "8", "newValue": null, "minParam": "7.5", "maxParam": "37.5", "machineId": 117, "isPrivate": false}, {"id": 79994, "name": "Refresh Batching Enable", "macro": "EEP_ENABLE_REFRESH_BATCHING ", "description": "Refreshes happen max once/hr", "address": 368, "boardName": "Master", "boardId": 65535, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 117, "isPrivate": false}, {"id": 79995, "name": "Refresh Lines Check Period", "macro": "EEP_REFRESH_CHECK_PERIOD", "description": "Time (in seconds) between refresh line operations, if there are any to perform", "address": 320, "boardName": "Master", "boardId": 65535, "paramType": "WORD", "defaultParam": "3600", "currentValue": "3600", "newValue": null, "minParam": "300", "maxParam": "5400", "machineId": 117, "isPrivate": false}, {"id": 79996, "name": "Refresh Sync Enable", "macro": "EEP_ENABLE_REFRESH_SYNC", "description": "Attempt to set refresh schedule to the top of the hour every hour. If the top of the next hour is unknown, continue to refresh every hour", "address": 369, "boardName": "Master", "boardId": 65535, "paramType": "BOOLEAN", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 117, "isPrivate": false}, {"id": 79997, "name": "Refrigeration Enable", "macro": "EEP_ENABLE_REFRIGERATION", "description": "Enable the compressor for cooling the fridge. See ", "address": 50, "boardName": "Cooling", "boardId": 576, "paramType": "BOOLEAN", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 117, "isPrivate": false}, {"id": 79998, "name": "Refrigeration Scheduled OFF Time", "macro": "EEP_COMPRESSOR_STATIC_OFF", "description": "Time the compressor is turned OFF for static, scheduled compressor operation (in seconds)", "address": 144, "boardName": "Cooling", "boardId": 576, "paramType": "WORD", "defaultParam": "1800", "currentValue": "1800", "newValue": null, "minParam": "300", "maxParam": "7200", "machineId": 117, "isPrivate": false}, {"id": 79999, "name": "Refrigeration Scheduled ON Time", "macro": "EEP_COMPRESSOR_STATIC_ON", "description": "Time the compressor is turned ON for static, scheduled compressor operation (in seconds)", "address": 128, "boardName": "Cooling", "boardId": 576, "paramType": "WORD", "defaultParam": "1800", "currentValue": "1800", "newValue": null, "minParam": "300", "maxParam": "7200", "machineId": 117, "isPrivate": false}, {"id": 80000, "name": "Static Cycle Enable", "macro": "EEP_ENABLE_STATIC_COMPRESSOR", "description": "Enable static cycling of the compressor regardless of the state of temperature sensors. (Note that if ", "address": 49, "boardName": "Cooling", "boardId": 576, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 117, "isPrivate": false}, {"id": 80001, "name": "Testing Mode Enable", "macro": "EEP_SILENT_MODE_ENABLE", "description": "Testing and production ONLY. Stop all asynchronous machine actions, CAN frames, and serial messages for testing purposes", "address": 80, "boardName": "Master", "boardId": 65535, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 117, "isPrivate": false}, {"id": 80002, "name": "Upper Temperature Threshold (Bottom Temp Sensor)", "macro": "EEP_UPPER_TEMP_THRESHOLD_BOT", "description": "Temperature of the bottom fridge sensor (in degrees Fahrenheit) above which the compressor is turned ON", "address": 224, "boardName": "Cooling", "boardId": 576, "paramType": "SINGLE", "defaultParam": "44", "currentValue": "42", "newValue": null, "minParam": "32", "maxParam": "46", "machineId": 117, "isPrivate": false}, {"id": 80003, "name": "Upper Temperature Threshold (Top Temp Sensor)", "macro": "EEP_UPPER_TEMP_THRESHOLD", "description": "Temperature of the top fridge sensor (in degrees Fahrenheit) above which the compressor is turned ON", "address": 16, "boardName": "Cooling", "boardId": 576, "paramType": "SINGLE", "defaultParam": "44", "currentValue": "44", "newValue": null, "minParam": "32", "maxParam": "46", "machineId": 117, "isPrivate": false}, {"id": 80004, "name": "Allow Retraction A1, A2, B1, B2", "macro": "EEP_ALLOW_RETRACTION", "description": "Allow user-configurable retraction for this board's ingredients", "address": 276, "boardName": "Pump Board 7", "boardId": 7, "paramType": "BOOLEAN", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 117, "isPrivate": false}, {"id": 80005, "name": "Allow Retraction A3, A4, B3, B4", "macro": "EEP_ALLOW_RETRACTION", "description": "Allow user-configurable retraction for this board's ingredients", "address": 276, "boardName": "Pump Board 8", "boardId": 8, "paramType": "BOOLEAN", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 117, "isPrivate": false}, {"id": 80006, "name": "Allow Retraction A5, A6, B5, B6", "macro": "EEP_ALLOW_RETRACTION", "description": "Allow user-configurable retraction for this board's ingredients", "address": 276, "boardName": "Pump Board 9", "boardId": 9, "paramType": "BOOLEAN", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 117, "isPrivate": false}, {"id": 80007, "name": "Allow Retraction C1, D1, E1, F1", "macro": "EEP_ALLOW_RETRACTION", "description": "Allow user-configurable retraction for this board's ingredients", "address": 276, "boardName": "Pump Board 0", "boardId": 0, "paramType": "BOOLEAN", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 117, "isPrivate": false}, {"id": 80008, "name": "Allow Retraction C2, E2, D2, F2", "macro": "EEP_ALLOW_RETRACTION", "description": "Allow user-configurable retraction for this board's ingredients", "address": 276, "boardName": "Pump Board 1", "boardId": 1, "paramType": "BOOLEAN", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 117, "isPrivate": false}, {"id": 80009, "name": "Allow Retraction C3, D3, E3, F3", "macro": "EEP_ALLOW_RETRACTION", "description": "Allow user-configurable retraction for this board's ingredients", "address": 276, "boardName": "Pump Board 2", "boardId": 2, "paramType": "BOOLEAN", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 117, "isPrivate": false}, {"id": 80010, "name": "Allow Retraction C4, D4, E4, F4", "macro": "EEP_ALLOW_RETRACTION", "description": "Allow user-configurable retraction for this board's ingredients", "address": 276, "boardName": "Pump Board 3", "boardId": 3, "paramType": "BOOLEAN", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 117, "isPrivate": false}, {"id": 80011, "name": "Allow Retraction C5, D5, E5, F5", "macro": "EEP_ALLOW_RETRACTION", "description": "Allow user-configurable retraction for this board's ingredients", "address": 276, "boardName": "Pump Board 4", "boardId": 4, "paramType": "BOOLEAN", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 117, "isPrivate": false}, {"id": 80012, "name": "Allow Retraction C6, D6, E6, F6", "macro": "EEP_ALLOW_RETRACTION", "description": "Allow user-configurable retraction for this board's ingredients", "address": 276, "boardName": "Pump Board 5", "boardId": 5, "paramType": "BOOLEAN", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 117, "isPrivate": false}, {"id": 80013, "name": "Allow Retraction T1 - T4", "macro": "EEP_ALLOW_RETRACTION", "description": "Allow user-configurable retraction for this board's ingredients", "address": 276, "boardName": "Pump Board 13", "boardId": 13, "paramType": "BOOLEAN", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 117, "isPrivate": false}, {"id": 80014, "name": "Allow Retraction T5 - T8", "macro": "EEP_ALLOW_RETRACTION", "description": "Allow user-configurable retraction for this board's ingredients", "address": 276, "boardName": "Pump Board 14", "boardId": 14, "paramType": "BOOLEAN", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 117, "isPrivate": false}, {"id": 80015, "name": "Bubble Detection - A1", "macro": "EEP_BUBBLE_DETECTION_0", "description": "Flag indicating whether a bubble/air is detected in A1's dispensing line", "address": 16, "boardName": "Pump Board 7", "boardId": 7, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 117, "isPrivate": true}, {"id": 80016, "name": "Bubble Detection - A2", "macro": "EEP_BUBBLE_DETECTION_2", "description": "Flag indicating whether a bubble/air is detected in A2's dispensing line", "address": 48, "boardName": "Pump Board 7", "boardId": 7, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 117, "isPrivate": true}, {"id": 80017, "name": "Bubble Detection - A3", "macro": "EEP_BUBBLE_DETECTION_0", "description": "Flag indicating whether a bubble/air is detected in A3's dispensing line", "address": 16, "boardName": "Pump Board 8", "boardId": 8, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 117, "isPrivate": true}, {"id": 80018, "name": "Bubble Detection - A4", "macro": "EEP_BUBBLE_DETECTION_2", "description": "Flag indicating whether a bubble/air is detected in A4's dispensing line", "address": 48, "boardName": "Pump Board 8", "boardId": 8, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 117, "isPrivate": true}, {"id": 80019, "name": "Bubble Detection - A5", "macro": "EEP_BUBBLE_DETECTION_0", "description": "Flag indicating whether a bubble/air is detected in A5's dispensing line", "address": 16, "boardName": "Pump Board 9", "boardId": 9, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 117, "isPrivate": true}, {"id": 80020, "name": "Bubble Detection - A6", "macro": "EEP_BUBBLE_DETECTION_2", "description": "Flag indicating whether a bubble/air is detected in A6's dispensing line", "address": 48, "boardName": "Pump Board 9", "boardId": 9, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 117, "isPrivate": true}, {"id": 80021, "name": "Bubble Detection - B1", "macro": "EEP_BUBBLE_DETECTION_1", "description": "Flag indicating whether a bubble/air is detected in B1's dispensing line", "address": 32, "boardName": "Pump Board 7", "boardId": 7, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 117, "isPrivate": true}, {"id": 80022, "name": "Bubble Detection - B2", "macro": "EEP_BUBBLE_DETECTION_3", "description": "Flag indicating whether a bubble/air is detected in B2's dispensing line", "address": 64, "boardName": "Pump Board 7", "boardId": 7, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 117, "isPrivate": true}, {"id": 80023, "name": "Bubble Detection - B3", "macro": "EEP_BUBBLE_DETECTION_1", "description": "Flag indicating whether a bubble/air is detected in B3's dispensing line", "address": 32, "boardName": "Pump Board 8", "boardId": 8, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 117, "isPrivate": true}, {"id": 80024, "name": "Bubble Detection - B4", "macro": "EEP_BUBBLE_DETECTION_3", "description": "Flag indicating whether a bubble/air is detected in B4's dispensing line", "address": 64, "boardName": "Pump Board 8", "boardId": 8, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 117, "isPrivate": true}, {"id": 80025, "name": "Bubble Detection - B5", "macro": "EEP_BUBBLE_DETECTION_1", "description": "Flag indicating whether a bubble/air is detected in B5's dispensing line", "address": 32, "boardName": "Pump Board 9", "boardId": 9, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 117, "isPrivate": true}, {"id": 80026, "name": "Bubble Detection - B6", "macro": "EEP_BUBBLE_DETECTION_3", "description": "Flag indicating whether a bubble/air is detected in B6's dispensing line", "address": 64, "boardName": "Pump Board 9", "boardId": 9, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 117, "isPrivate": true}, {"id": 80027, "name": "Bubble Detection - C1", "macro": "EEP_BUBBLE_DETECTION_0", "description": "Flag indicating whether a bubble/air is detected in C1's dispensing line", "address": 16, "boardName": "Pump Board 0", "boardId": 0, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 117, "isPrivate": true}, {"id": 80028, "name": "Bubble Detection - C2", "macro": "EEP_BUBBLE_DETECTION_0", "description": "Flag indicating whether a bubble/air is detected in C2's dispensing line", "address": 16, "boardName": "Pump Board 1", "boardId": 1, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 117, "isPrivate": true}, {"id": 80029, "name": "Bubble Detection - C3", "macro": "EEP_BUBBLE_DETECTION_0", "description": "Flag indicating whether a bubble/air is detected in C3's dispensing line", "address": 16, "boardName": "Pump Board 2", "boardId": 2, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 117, "isPrivate": true}, {"id": 80030, "name": "Bubble Detection - C4", "macro": "EEP_BUBBLE_DETECTION_0", "description": "Flag indicating whether a bubble/air is detected in C4's dispensing line", "address": 16, "boardName": "Pump Board 3", "boardId": 3, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 117, "isPrivate": true}, {"id": 80031, "name": "Bubble Detection - C5", "macro": "EEP_BUBBLE_DETECTION_0", "description": "Flag indicating whether a bubble/air is detected in C5's dispensing line", "address": 16, "boardName": "Pump Board 4", "boardId": 4, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 117, "isPrivate": true}, {"id": 80032, "name": "Bubble Detection - C6", "macro": "EEP_BUBBLE_DETECTION_0", "description": "Flag indicating whether a bubble/air is detected in C6's dispensing line", "address": 16, "boardName": "Pump Board 5", "boardId": 5, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 117, "isPrivate": true}, {"id": 80033, "name": "Bubble Detection - D1", "macro": "EEP_BUBBLE_DETECTION_1", "description": "Flag indicating whether a bubble/air is detected in D1's dispensing line", "address": 32, "boardName": "Pump Board 0", "boardId": 0, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 117, "isPrivate": true}, {"id": 80034, "name": "Bubble Detection - D2", "macro": "EEP_BUBBLE_DETECTION_1", "description": "Flag indicating whether a bubble/air is detected in D2's dispensing line", "address": 32, "boardName": "Pump Board 1", "boardId": 1, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 117, "isPrivate": true}, {"id": 80035, "name": "Bubble Detection - D3", "macro": "EEP_BUBBLE_DETECTION_1", "description": "Flag indicating whether a bubble/air is detected in D3's dispensing line", "address": 32, "boardName": "Pump Board 2", "boardId": 2, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 117, "isPrivate": true}, {"id": 80036, "name": "Bubble Detection - D4", "macro": "EEP_BUBBLE_DETECTION_1", "description": "Flag indicating whether a bubble/air is detected in D4's dispensing line", "address": 32, "boardName": "Pump Board 3", "boardId": 3, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 117, "isPrivate": true}, {"id": 80037, "name": "Bubble Detection - D5", "macro": "EEP_BUBBLE_DETECTION_1", "description": "Flag indicating whether a bubble/air is detected in D5's dispensing line", "address": 32, "boardName": "Pump Board 4", "boardId": 4, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 117, "isPrivate": true}, {"id": 80038, "name": "Bubble Detection - D6", "macro": "EEP_BUBBLE_DETECTION_1", "description": "Flag indicating whether a bubble/air is detected in D6's dispensing line", "address": 32, "boardName": "Pump Board 5", "boardId": 5, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 117, "isPrivate": true}, {"id": 80039, "name": "Bubble Detection - E1", "macro": "EEP_BUBBLE_DETECTION_2", "description": "Flag indicating whether a bubble/air is detected in E1's dispensing line", "address": 48, "boardName": "Pump Board 0", "boardId": 0, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 117, "isPrivate": true}, {"id": 80040, "name": "Bubble Detection - E2", "macro": "EEP_BUBBLE_DETECTION_2", "description": "Flag indicating whether a bubble/air is detected in E2's dispensing line", "address": 48, "boardName": "Pump Board 1", "boardId": 1, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 117, "isPrivate": true}, {"id": 80041, "name": "Bubble Detection - E3", "macro": "EEP_BUBBLE_DETECTION_2", "description": "Flag indicating whether a bubble/air is detected in E3's dispensing line", "address": 48, "boardName": "Pump Board 2", "boardId": 2, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 117, "isPrivate": true}, {"id": 80042, "name": "Bubble Detection - E4", "macro": "EEP_BUBBLE_DETECTION_2", "description": "Flag indicating whether a bubble/air is detected in E4's dispensing line", "address": 48, "boardName": "Pump Board 3", "boardId": 3, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 117, "isPrivate": true}, {"id": 80043, "name": "Bubble Detection - E5", "macro": "EEP_BUBBLE_DETECTION_2", "description": "Flag indicating whether a bubble/air is detected in E5's dispensing line", "address": 48, "boardName": "Pump Board 4", "boardId": 4, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 117, "isPrivate": true}, {"id": 80044, "name": "Bubble Detection - E6", "macro": "EEP_BUBBLE_DETECTION_2", "description": "Flag indicating whether a bubble/air is detected in E6's dispensing line", "address": 48, "boardName": "Pump Board 5", "boardId": 5, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 117, "isPrivate": true}, {"id": 80045, "name": "Bubble Detection - F1", "macro": "EEP_BUBBLE_DETECTION_3", "description": "Flag indicating whether a bubble/air is detected in F1's dispensing line", "address": 64, "boardName": "Pump Board 0", "boardId": 0, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 117, "isPrivate": true}, {"id": 80046, "name": "Bubble Detection - F2", "macro": "EEP_BUBBLE_DETECTION_3", "description": "Flag indicating whether a bubble/air is detected in F2's dispensing line", "address": 64, "boardName": "Pump Board 1", "boardId": 1, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 117, "isPrivate": true}, {"id": 80047, "name": "Bubble Detection - F3", "macro": "EEP_BUBBLE_DETECTION_3", "description": "Flag indicating whether a bubble/air is detected in F3's dispensing line", "address": 64, "boardName": "Pump Board 2", "boardId": 2, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 117, "isPrivate": true}, {"id": 80048, "name": "Bubble Detection - F4", "macro": "EEP_BUBBLE_DETECTION_3", "description": "Flag indicating whether a bubble/air is detected in F4's dispensing line", "address": 64, "boardName": "Pump Board 3", "boardId": 3, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 117, "isPrivate": true}, {"id": 80049, "name": "Bubble Detection - F5", "macro": "EEP_BUBBLE_DETECTION_3", "description": "Flag indicating whether a bubble/air is detected in F5's dispensing line", "address": 64, "boardName": "Pump Board 4", "boardId": 4, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 117, "isPrivate": true}, {"id": 80050, "name": "Bubble Detection - F6", "macro": "EEP_BUBBLE_DETECTION_3", "description": "Flag indicating whether a bubble/air is detected in F6's dispensing line", "address": 64, "boardName": "Pump Board 5", "boardId": 5, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 117, "isPrivate": true}, {"id": 80051, "name": "Bubble Detection - T1", "macro": "EEP_BUBBLE_DETECTION_0", "description": "Flag indicating whether a bubble/air is detected in T1's dispensing line", "address": 16, "boardName": "Pump Board 13", "boardId": 13, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 117, "isPrivate": true}, {"id": 80052, "name": "Bubble Detection - T2", "macro": "EEP_BUBBLE_DETECTION_1", "description": "Flag indicating whether a bubble/air is detected in T2's dispensing line", "address": 32, "boardName": "Pump Board 13", "boardId": 13, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 117, "isPrivate": true}, {"id": 80053, "name": "Bubble Detection - T3", "macro": "EEP_BUBBLE_DETECTION_2", "description": "Flag indicating whether a bubble/air is detected in T3's dispensing line", "address": 48, "boardName": "Pump Board 13", "boardId": 13, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 117, "isPrivate": true}, {"id": 80054, "name": "Bubble Detection - T4", "macro": "EEP_BUBBLE_DETECTION_3", "description": "Flag indicating whether a bubble/air is detected in T4's dispensing line", "address": 64, "boardName": "Pump Board 13", "boardId": 13, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 117, "isPrivate": true}, {"id": 80055, "name": "Bubble Detection - T5", "macro": "EEP_BUBBLE_DETECTION_0", "description": "Flag indicating whether a bubble/air is detected in A5's dispensing line", "address": 16, "boardName": "Pump Board 14", "boardId": 14, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 117, "isPrivate": true}, {"id": 80056, "name": "Bubble Detection - T6", "macro": "EEP_BUBBLE_DETECTION_1", "description": "Flag indicating whether a bubble/air is detected in T6's dispensing line", "address": 32, "boardName": "Pump Board 14", "boardId": 14, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 117, "isPrivate": true}, {"id": 80057, "name": "Bubble Detection - T7", "macro": "EEP_BUBBLE_DETECTION_2", "description": "Flag indicating whether a bubble/air is detected in T7's dispensing line", "address": 48, "boardName": "Pump Board 14", "boardId": 14, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 117, "isPrivate": true}, {"id": 80058, "name": "Bubble Detection - T8", "macro": "EEP_BUBBLE_DETECTION_3", "description": "Flag indicating whether a bubble/air is detected in T8's dispensing line", "address": 64, "boardName": "Pump Board 14", "boardId": 14, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 117, "isPrivate": true}, {"id": 80059, "name": "Calibration Factor - Bib 1", "macro": "EEP_BIB_0_CAL_VAL", "description": "Ingredient calibration factor (Number of ms per fluid oz)", "address": 16, "boardName": "Nozzle", "boardId": 560, "paramType": "WORD", "defaultParam": "2000", "currentValue": "2000", "newValue": null, "minParam": "100", "maxParam": "10000", "machineId": 117, "isPrivate": true}, {"id": 80060, "name": "Calibration Factor - Bib 2", "macro": "EEP_BIB_1_CAL_VAL", "description": "Ingredient calibration factor (Number of ms per fluid oz)", "address": 32, "boardName": "Nozzle", "boardId": 560, "paramType": "WORD", "defaultParam": "2000", "currentValue": "2000", "newValue": null, "minParam": "100", "maxParam": "10000", "machineId": 117, "isPrivate": true}, {"id": 80061, "name": "Calibration Factor - Bib 3", "macro": "EEP_BIB_2_CAL_VAL", "description": "Ingredient calibration factor (Number of ms per fluid oz)", "address": 48, "boardName": "Nozzle", "boardId": 560, "paramType": "WORD", "defaultParam": "2000", "currentValue": "2000", "newValue": null, "minParam": "100", "maxParam": "10000", "machineId": 117, "isPrivate": true}, {"id": 80062, "name": "Calibration Factor - Bib 4", "macro": "EEP_BIB_3_CAL_VAL", "description": "Ingredient calibration factor (Number of ms per fluid oz)", "address": 64, "boardName": "Nozzle", "boardId": 560, "paramType": "WORD", "defaultParam": "2000", "currentValue": "2000", "newValue": null, "minParam": "100", "maxParam": "10000", "machineId": 117, "isPrivate": true}, {"id": 80063, "name": "Calibration Factor - Soda Bib", "macro": "EEP_SODA_CAL_VAL", "description": "Ingredient calibration factor (Number of ms per fluid oz)", "address": 80, "boardName": "Nozzle", "boardId": 560, "paramType": "WORD", "defaultParam": "2000", "currentValue": "591", "newValue": null, "minParam": "100", "maxParam": "10000", "machineId": 117, "isPrivate": true}, {"id": 80064, "name": "Calibration Factor - Water Bib", "macro": "EEP_WATER_CAL_VAL", "description": "Ingredient calibration factor (Number of ms per fluid oz)", "address": 96, "boardName": "Nozzle", "boardId": 560, "paramType": "WORD", "defaultParam": "2000", "currentValue": "1136", "newValue": null, "minParam": "100", "maxParam": "10000", "machineId": 117, "isPrivate": true}, {"id": 80065, "name": "Dispensing Calibration Factor - A1", "macro": "EEP_INGR_CALIBRATION_FACTOR_0", "description": "Ingredient calibration factor (Number of pulses per fluid oz)", "address": 384, "boardName": "Pump Board 7", "boardId": 7, "paramType": "DWORD", "defaultParam": "48600", "currentValue": "48600", "newValue": null, "minParam": "9720", "maxParam": "194400", "machineId": 117, "isPrivate": true}, {"id": 80066, "name": "Dispensing Calibration Factor - A2", "macro": "EEP_INGR_CALIBRATION_FACTOR_2", "description": "Ingredient calibration factor (Number of pulses per fluid oz)", "address": 392, "boardName": "Pump Board 7", "boardId": 7, "paramType": "DWORD", "defaultParam": "48600", "currentValue": "48600", "newValue": "48600", "minParam": "9720", "maxParam": "194400", "machineId": 117, "isPrivate": true}, {"id": 80067, "name": "Dispensing Calibration Factor - A3", "macro": "EEP_INGR_CALIBRATION_FACTOR_0", "description": "Ingredient calibration factor (Number of pulses per fluid oz)", "address": 384, "boardName": "Pump Board 8", "boardId": 8, "paramType": "DWORD", "defaultParam": "48600", "currentValue": "48600", "newValue": "48600", "minParam": "9720", "maxParam": "194400", "machineId": 117, "isPrivate": true}, {"id": 80068, "name": "Dispensing Calibration Factor - A4", "macro": "EEP_INGR_CALIBRATION_FACTOR_2", "description": "Ingredient calibration factor (Number of pulses per fluid oz)", "address": 392, "boardName": "Pump Board 8", "boardId": 8, "paramType": "DWORD", "defaultParam": "48600", "currentValue": "48600", "newValue": "48600", "minParam": "9720", "maxParam": "194400", "machineId": 117, "isPrivate": true}, {"id": 80069, "name": "Dispensing Calibration Factor - A5", "macro": "EEP_INGR_CALIBRATION_FACTOR_0", "description": "Ingredient calibration factor (Number of pulses per fluid oz)", "address": 384, "boardName": "Pump Board 9", "boardId": 9, "paramType": "DWORD", "defaultParam": "48600", "currentValue": "48600", "newValue": "48600", "minParam": "9720", "maxParam": "194400", "machineId": 117, "isPrivate": true}, {"id": 80070, "name": "Dispensing Calibration Factor - A6", "macro": "EEP_INGR_CALIBRATION_FACTOR_2", "description": "Ingredient calibration factor (Number of pulses per fluid oz)", "address": 392, "boardName": "Pump Board 9", "boardId": 9, "paramType": "DWORD", "defaultParam": "48600", "currentValue": "48600", "newValue": "48600", "minParam": "9720", "maxParam": "194400", "machineId": 117, "isPrivate": true}, {"id": 80071, "name": "Dispensing Calibration Factor - B1", "macro": "EEP_INGR_CALIBRATION_FACTOR_1", "description": "Ingredient calibration factor (Number of pulses per fluid oz)", "address": 388, "boardName": "Pump Board 7", "boardId": 7, "paramType": "DWORD", "defaultParam": "48600", "currentValue": "48600", "newValue": "48600", "minParam": "9720", "maxParam": "194400", "machineId": 117, "isPrivate": true}, {"id": 80072, "name": "Dispensing Calibration Factor - B2", "macro": "EEP_INGR_CALIBRATION_FACTOR_3", "description": "Ingredient calibration factor (Number of pulses per fluid oz)", "address": 396, "boardName": "Pump Board 7", "boardId": 7, "paramType": "DWORD", "defaultParam": "48600", "currentValue": "48600", "newValue": "48600", "minParam": "9720", "maxParam": "194400", "machineId": 117, "isPrivate": true}, {"id": 80073, "name": "Dispensing Calibration Factor - B3", "macro": "EEP_INGR_CALIBRATION_FACTOR_1", "description": "Ingredient calibration factor (Number of pulses per fluid oz)", "address": 388, "boardName": "Pump Board 8", "boardId": 8, "paramType": "DWORD", "defaultParam": "48600", "currentValue": "48600", "newValue": "48600", "minParam": "9720", "maxParam": "194400", "machineId": 117, "isPrivate": true}, {"id": 80074, "name": "Dispensing Calibration Factor - B4", "macro": "EEP_INGR_CALIBRATION_FACTOR_3", "description": "Ingredient calibration factor (Number of pulses per fluid oz)", "address": 396, "boardName": "Pump Board 8", "boardId": 8, "paramType": "DWORD", "defaultParam": "48600", "currentValue": "48600", "newValue": "48600", "minParam": "9720", "maxParam": "194400", "machineId": 117, "isPrivate": true}, {"id": 80075, "name": "Dispensing Calibration Factor - B5", "macro": "EEP_INGR_CALIBRATION_FACTOR_1", "description": "Ingredient calibration factor (Number of pulses per fluid oz)", "address": 388, "boardName": "Pump Board 9", "boardId": 9, "paramType": "DWORD", "defaultParam": "48600", "currentValue": "48600", "newValue": null, "minParam": "9720", "maxParam": "194400", "machineId": 117, "isPrivate": true}, {"id": 80076, "name": "Dispensing Calibration Factor - B6", "macro": "EEP_INGR_CALIBRATION_FACTOR_3", "description": "Ingredient calibration factor (Number of pulses per fluid oz)", "address": 396, "boardName": "Pump Board 9", "boardId": 9, "paramType": "DWORD", "defaultParam": "48600", "currentValue": "48600", "newValue": null, "minParam": "9720", "maxParam": "194400", "machineId": 117, "isPrivate": true}, {"id": 80077, "name": "Dispensing Calibration Factor - C1", "macro": "EEP_INGR_CALIBRATION_FACTOR_0", "description": "Ingredient calibration factor (Number of pulses per fluid oz)", "address": 384, "boardName": "Pump Board 0", "boardId": 0, "paramType": "DWORD", "defaultParam": "24300", "currentValue": "24300", "newValue": null, "minParam": "9720", "maxParam": "194400", "machineId": 117, "isPrivate": true}, {"id": 80078, "name": "Dispensing Calibration Factor - C2", "macro": "EEP_INGR_CALIBRATION_FACTOR_0", "description": "Ingredient calibration factor (Number of pulses per fluid oz)", "address": 384, "boardName": "Pump Board 1", "boardId": 1, "paramType": "DWORD", "defaultParam": "48600", "currentValue": "48600", "newValue": "0", "minParam": "9720", "maxParam": "194400", "machineId": 117, "isPrivate": true}, {"id": 80079, "name": "Dispensing Calibration Factor - C3", "macro": "EEP_INGR_CALIBRATION_FACTOR_0", "description": "Ingredient calibration factor (Number of pulses per fluid oz)", "address": 384, "boardName": "Pump Board 2", "boardId": 2, "paramType": "DWORD", "defaultParam": "48600", "currentValue": "48600", "newValue": null, "minParam": "9720", "maxParam": "194400", "machineId": 117, "isPrivate": true}, {"id": 80080, "name": "Dispensing Calibration Factor - C4", "macro": "EEP_INGR_CALIBRATION_FACTOR_0", "description": "Ingredient calibration factor (Number of pulses per fluid oz)", "address": 384, "boardName": "Pump Board 3", "boardId": 3, "paramType": "DWORD", "defaultParam": "48600", "currentValue": "48600", "newValue": "0", "minParam": "9720", "maxParam": "194400", "machineId": 117, "isPrivate": true}, {"id": 80081, "name": "Dispensing Calibration Factor - C5", "macro": "EEP_INGR_CALIBRATION_FACTOR_0", "description": "Ingredient calibration factor (Number of pulses per fluid oz)", "address": 384, "boardName": "Pump Board 4", "boardId": 4, "paramType": "DWORD", "defaultParam": "48600", "currentValue": "48600", "newValue": "0", "minParam": "9720", "maxParam": "194400", "machineId": 117, "isPrivate": true}, {"id": 80082, "name": "Dispensing Calibration Factor - C6", "macro": "EEP_INGR_CALIBRATION_FACTOR_0", "description": "Ingredient calibration factor (Number of pulses per fluid oz)", "address": 384, "boardName": "Pump Board 5", "boardId": 5, "paramType": "DWORD", "defaultParam": "48600", "currentValue": "48600", "newValue": "48600", "minParam": "9720", "maxParam": "194400", "machineId": 117, "isPrivate": true}, {"id": 80083, "name": "Dispensing Calibration Factor - D1", "macro": "EEP_INGR_CALIBRATION_FACTOR_1", "description": "Ingredient calibration factor (Number of pulses per fluid oz)", "address": 388, "boardName": "Pump Board 0", "boardId": 0, "paramType": "DWORD", "defaultParam": "24300", "currentValue": "24300", "newValue": "48600", "minParam": "9720", "maxParam": "194400", "machineId": 117, "isPrivate": true}, {"id": 80084, "name": "Dispensing Calibration Factor - D2", "macro": "EEP_INGR_CALIBRATION_FACTOR_1", "description": "Ingredient calibration factor (Number of pulses per fluid oz)", "address": 388, "boardName": "Pump Board 1", "boardId": 1, "paramType": "DWORD", "defaultParam": "48600", "currentValue": "48600", "newValue": "48600", "minParam": "9720", "maxParam": "194400", "machineId": 117, "isPrivate": true}, {"id": 80085, "name": "Dispensing Calibration Factor - D3", "macro": "EEP_INGR_CALIBRATION_FACTOR_1", "description": "Ingredient calibration factor (Number of pulses per fluid oz)", "address": 388, "boardName": "Pump Board 2", "boardId": 2, "paramType": "DWORD", "defaultParam": "48600", "currentValue": "48600", "newValue": "48600", "minParam": "9720", "maxParam": "194400", "machineId": 117, "isPrivate": true}, {"id": 80086, "name": "Dispensing Calibration Factor - D4", "macro": "EEP_INGR_CALIBRATION_FACTOR_1", "description": "Ingredient calibration factor (Number of pulses per fluid oz)", "address": 388, "boardName": "Pump Board 3", "boardId": 3, "paramType": "DWORD", "defaultParam": "48600", "currentValue": "48600", "newValue": null, "minParam": "9720", "maxParam": "194400", "machineId": 117, "isPrivate": true}, {"id": 80087, "name": "Dispensing Calibration Factor - D5", "macro": "EEP_INGR_CALIBRATION_FACTOR_1", "description": "Ingredient calibration factor (Number of pulses per fluid oz)", "address": 388, "boardName": "Pump Board 4", "boardId": 4, "paramType": "DWORD", "defaultParam": "48600", "currentValue": "48600", "newValue": null, "minParam": "9720", "maxParam": "194400", "machineId": 117, "isPrivate": true}, {"id": 80088, "name": "Dispensing Calibration Factor - D6", "macro": "EEP_INGR_CALIBRATION_FACTOR_1", "description": "Ingredient calibration factor (Number of pulses per fluid oz)", "address": 388, "boardName": "Pump Board 5", "boardId": 5, "paramType": "DWORD", "defaultParam": "48600", "currentValue": "48600", "newValue": null, "minParam": "9720", "maxParam": "194400", "machineId": 117, "isPrivate": true}, {"id": 80089, "name": "Dispensing Calibration Factor - E1", "macro": "EEP_INGR_CALIBRATION_FACTOR_2", "description": "Ingredient calibration factor (Number of pulses per fluid oz)", "address": 392, "boardName": "Pump Board 0", "boardId": 0, "paramType": "DWORD", "defaultParam": "24300", "currentValue": "24300", "newValue": "48600", "minParam": "9720", "maxParam": "194400", "machineId": 117, "isPrivate": true}, {"id": 80090, "name": "Dispensing Calibration Factor - E2", "macro": "EEP_INGR_CALIBRATION_FACTOR_2", "description": "Ingredient calibration factor (Number of pulses per fluid oz)", "address": 392, "boardName": "Pump Board 1", "boardId": 1, "paramType": "DWORD", "defaultParam": "48600", "currentValue": "48600", "newValue": "0", "minParam": "9720", "maxParam": "194400", "machineId": 117, "isPrivate": true}, {"id": 80091, "name": "Dispensing Calibration Factor - E3", "macro": "EEP_INGR_CALIBRATION_FACTOR_2", "description": "Ingredient calibration factor (Number of pulses per fluid oz)", "address": 392, "boardName": "Pump Board 2", "boardId": 2, "paramType": "DWORD", "defaultParam": "48600", "currentValue": "48600", "newValue": "0", "minParam": "9720", "maxParam": "194400", "machineId": 117, "isPrivate": true}, {"id": 80092, "name": "Dispensing Calibration Factor - E4", "macro": "EEP_INGR_CALIBRATION_FACTOR_2", "description": "Ingredient calibration factor (Number of pulses per fluid oz)", "address": 392, "boardName": "Pump Board 3", "boardId": 3, "paramType": "DWORD", "defaultParam": "48600", "currentValue": "48600", "newValue": null, "minParam": "9720", "maxParam": "194400", "machineId": 117, "isPrivate": true}, {"id": 80093, "name": "Dispensing Calibration Factor - E5", "macro": "EEP_INGR_CALIBRATION_FACTOR_2", "description": "Ingredient calibration factor (Number of pulses per fluid oz)", "address": 392, "boardName": "Pump Board 4", "boardId": 4, "paramType": "DWORD", "defaultParam": "48600", "currentValue": "48600", "newValue": "0", "minParam": "9720", "maxParam": "194400", "machineId": 117, "isPrivate": true}, {"id": 80094, "name": "Dispensing Calibration Factor - E6", "macro": "EEP_INGR_CALIBRATION_FACTOR_2", "description": "Ingredient calibration factor (Number of pulses per fluid oz)", "address": 392, "boardName": "Pump Board 5", "boardId": 5, "paramType": "DWORD", "defaultParam": "48600", "currentValue": "48600", "newValue": null, "minParam": "9720", "maxParam": "194400", "machineId": 117, "isPrivate": true}, {"id": 80095, "name": "Dispensing Calibration Factor - F1", "macro": "EEP_INGR_CALIBRATION_FACTOR_3", "description": "Ingredient calibration factor (Number of pulses per fluid oz)", "address": 396, "boardName": "Pump Board 0", "boardId": 0, "paramType": "DWORD", "defaultParam": "24300", "currentValue": "24300", "newValue": "48600", "minParam": "9720", "maxParam": "194400", "machineId": 117, "isPrivate": true}, {"id": 80096, "name": "Dispensing Calibration Factor - F2", "macro": "EEP_INGR_CALIBRATION_FACTOR_3", "description": "Ingredient calibration factor (Number of pulses per fluid oz)", "address": 396, "boardName": "Pump Board 1", "boardId": 1, "paramType": "DWORD", "defaultParam": "48600", "currentValue": "48600", "newValue": null, "minParam": "9720", "maxParam": "194400", "machineId": 117, "isPrivate": true}, {"id": 80097, "name": "Dispensing Calibration Factor - F3", "macro": "EEP_INGR_CALIBRATION_FACTOR_3", "description": "Ingredient calibration factor (Number of pulses per fluid oz)", "address": 396, "boardName": "Pump Board 2", "boardId": 2, "paramType": "DWORD", "defaultParam": "48600", "currentValue": "48600", "newValue": null, "minParam": "9720", "maxParam": "194400", "machineId": 117, "isPrivate": true}, {"id": 80098, "name": "Dispensing Calibration Factor - F4", "macro": "EEP_INGR_CALIBRATION_FACTOR_3", "description": "Ingredient calibration factor (Number of pulses per fluid oz)", "address": 396, "boardName": "Pump Board 3", "boardId": 3, "paramType": "DWORD", "defaultParam": "48600", "currentValue": "48600", "newValue": "48600", "minParam": "9720", "maxParam": "194400", "machineId": 117, "isPrivate": true}, {"id": 80099, "name": "Dispensing Calibration Factor - F5", "macro": "EEP_INGR_CALIBRATION_FACTOR_3", "description": "Ingredient calibration factor (Number of pulses per fluid oz)", "address": 396, "boardName": "Pump Board 4", "boardId": 4, "paramType": "DWORD", "defaultParam": "48600", "currentValue": "48600", "newValue": "0", "minParam": "9720", "maxParam": "194400", "machineId": 117, "isPrivate": true}, {"id": 80100, "name": "Dispensing Calibration Factor - F6", "macro": "EEP_INGR_CALIBRATION_FACTOR_3", "description": "Ingredient calibration factor (Number of pulses per fluid oz)", "address": 396, "boardName": "Pump Board 5", "boardId": 5, "paramType": "DWORD", "defaultParam": "48600", "currentValue": "48600", "newValue": "0", "minParam": "9720", "maxParam": "194400", "machineId": 117, "isPrivate": true}, {"id": 80101, "name": "Dispensing Calibration Factor - T1", "macro": "EEP_INGR_CALIBRATION_FACTOR_0", "description": "Ingredient calibration factor (Number of pulses per fluid oz)", "address": 384, "boardName": "Pump Board 13", "boardId": 13, "paramType": "DWORD", "defaultParam": "48600", "currentValue": "48600", "newValue": null, "minParam": "9720", "maxParam": "194400", "machineId": 117, "isPrivate": true}, {"id": 80102, "name": "Dispensing Calibration Factor - T2", "macro": "EEP_INGR_CALIBRATION_FACTOR_1", "description": "Ingredient calibration factor (Number of pulses per fluid oz)", "address": 388, "boardName": "Pump Board 13", "boardId": 13, "paramType": "DWORD", "defaultParam": "48600", "currentValue": "48600", "newValue": null, "minParam": "9720", "maxParam": "194400", "machineId": 117, "isPrivate": true}, {"id": 80103, "name": "Dispensing Calibration Factor - T3", "macro": "EEP_INGR_CALIBRATION_FACTOR_2", "description": "Ingredient calibration factor (Number of pulses per fluid oz)", "address": 392, "boardName": "Pump Board 13", "boardId": 13, "paramType": "DWORD", "defaultParam": "48600", "currentValue": "48600", "newValue": null, "minParam": "9720", "maxParam": "194400", "machineId": 117, "isPrivate": true}, {"id": 80104, "name": "Dispensing Calibration Factor - T4", "macro": "EEP_INGR_CALIBRATION_FACTOR_3", "description": "Ingredient calibration factor (Number of pulses per fluid oz)", "address": 396, "boardName": "Pump Board 13", "boardId": 13, "paramType": "DWORD", "defaultParam": "48600", "currentValue": "48600", "newValue": null, "minParam": "9720", "maxParam": "194400", "machineId": 117, "isPrivate": true}, {"id": 80105, "name": "Dispensing Calibration Factor - T5", "macro": "EEP_INGR_CALIBRATION_FACTOR_0", "description": "Ingredient calibration factor (Number of pulses per fluid oz)", "address": 384, "boardName": "Pump Board 14", "boardId": 14, "paramType": "DWORD", "defaultParam": "48600", "currentValue": "48600", "newValue": null, "minParam": "9720", "maxParam": "194400", "machineId": 117, "isPrivate": true}, {"id": 80106, "name": "Dispensing Calibration Factor - T6", "macro": "EEP_INGR_CALIBRATION_FACTOR_1", "description": "Ingredient calibration factor (Number of pulses per fluid oz)", "address": 388, "boardName": "Pump Board 14", "boardId": 14, "paramType": "DWORD", "defaultParam": "48600", "currentValue": "48600", "newValue": null, "minParam": "9720", "maxParam": "194400", "machineId": 117, "isPrivate": true}, {"id": 80107, "name": "Dispensing Calibration Factor - T7", "macro": "EEP_INGR_CALIBRATION_FACTOR_2", "description": "Ingredient calibration factor (Number of pulses per fluid oz)", "address": 392, "boardName": "Pump Board 14", "boardId": 14, "paramType": "DWORD", "defaultParam": "48600", "currentValue": "48600", "newValue": null, "minParam": "9720", "maxParam": "194400", "machineId": 117, "isPrivate": true}, {"id": 80108, "name": "Dispensing Calibration Factor - T8", "macro": "EEP_INGR_CALIBRATION_FACTOR_3", "description": "Ingredient calibration factor (Number of pulses per fluid oz)", "address": 396, "boardName": "Pump Board 14", "boardId": 14, "paramType": "DWORD", "defaultParam": "48600", "currentValue": "48600", "newValue": null, "minParam": "9720", "maxParam": "194400", "machineId": 117, "isPrivate": true}, {"id": 80109, "name": "Ingredient Line Status - A1", "macro": "EEP_INGR_LINE_STATUS_0", "description": "Bitfield for current ingredient prime status of pumped ingredients", "address": 432, "boardName": "Pump Board 7", "boardId": 7, "paramType": "BYTE", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 117, "isPrivate": true}, {"id": 80110, "name": "Ingredient Line Status - A2", "macro": "EEP_INGR_LINE_STATUS_2", "description": "Bitfield for current ingredient prime status of pumped ingredients", "address": 434, "boardName": "Pump Board 7", "boardId": 7, "paramType": "BYTE", "defaultParam": "0", "currentValue": "8", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 117, "isPrivate": true}, {"id": 80111, "name": "Ingredient Line Status - A3", "macro": "EEP_INGR_LINE_STATUS_0", "description": "Bitfield for current ingredient prime status of pumped ingredients", "address": 432, "boardName": "Pump Board 8", "boardId": 8, "paramType": "BYTE", "defaultParam": "0", "currentValue": "8", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 117, "isPrivate": true}, {"id": 80112, "name": "Ingredient Line Status - A4", "macro": "EEP_INGR_LINE_STATUS_2", "description": "Bitfield for current ingredient prime status of pumped ingredients", "address": 434, "boardName": "Pump Board 8", "boardId": 8, "paramType": "BYTE", "defaultParam": "0", "currentValue": "8", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 117, "isPrivate": true}, {"id": 80113, "name": "Ingredient Line Status - A5", "macro": "EEP_INGR_LINE_STATUS_0", "description": "Bitfield for current ingredient prime status of pumped ingredients", "address": 432, "boardName": "Pump Board 9", "boardId": 9, "paramType": "BYTE", "defaultParam": "0", "currentValue": "9", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 117, "isPrivate": true}, {"id": 80114, "name": "Ingredient Line Status - B2", "macro": "EEP_INGR_LINE_STATUS_3", "description": "Bitfield for current ingredient prime status of pumped ingredients", "address": 435, "boardName": "Pump Board 7", "boardId": 7, "paramType": "BYTE", "defaultParam": "0", "currentValue": "8", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 117, "isPrivate": true}, {"id": 80115, "name": "Ingredient Line Status - B3", "macro": "EEP_INGR_LINE_STATUS_1", "description": "Bitfield for current ingredient prime status of pumped ingredients", "address": 433, "boardName": "Pump Board 8", "boardId": 8, "paramType": "BYTE", "defaultParam": "0", "currentValue": "8", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 117, "isPrivate": true}, {"id": 80116, "name": "Ingredient Line Status - B4", "macro": "EEP_INGR_LINE_STATUS_3", "description": "Bitfield for current ingredient prime status of pumped ingredients", "address": 435, "boardName": "Pump Board 8", "boardId": 8, "paramType": "BYTE", "defaultParam": "0", "currentValue": "8", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 117, "isPrivate": true}, {"id": 80117, "name": "Ingredient Line Status - B5", "macro": "EEP_INGR_LINE_STATUS_1", "description": "Bitfield for current ingredient prime status of pumped ingredients", "address": 433, "boardName": "Pump Board 9", "boardId": 9, "paramType": "BYTE", "defaultParam": "0", "currentValue": "8", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 117, "isPrivate": true}, {"id": 80118, "name": "Ingredient Line Status - B6", "macro": "EEP_INGR_LINE_STATUS_3", "description": "Bitfield for current ingredient prime status of pumped ingredients", "address": 435, "boardName": "Pump Board 9", "boardId": 9, "paramType": "BYTE", "defaultParam": "0", "currentValue": "8", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 117, "isPrivate": true}, {"id": 80119, "name": "Ingredient Line Status - C1", "macro": "EEP_INGR_LINE_STATUS_0", "description": "Bitfield for current ingredient prime status of pumped ingredients", "address": 432, "boardName": "Pump Board 0", "boardId": 0, "paramType": "BYTE", "defaultParam": "0", "currentValue": "4", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 117, "isPrivate": true}, {"id": 80120, "name": "Ingredient Line Status - C2", "macro": "EEP_INGR_LINE_STATUS_0", "description": "Bitfield for current ingredient prime status of pumped ingredients", "address": 432, "boardName": "Pump Board 1", "boardId": 1, "paramType": "BYTE", "defaultParam": "0", "currentValue": "4", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 117, "isPrivate": true}, {"id": 80121, "name": "Ingredient Line Status - C3", "macro": "EEP_INGR_LINE_STATUS_0", "description": "Bitfield for current ingredient prime status of pumped ingredients", "address": 432, "boardName": "Pump Board 2", "boardId": 2, "paramType": "BYTE", "defaultParam": "0", "currentValue": "9", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 117, "isPrivate": true}, {"id": 80122, "name": "Ingredient Line Status - C4", "macro": "EEP_INGR_LINE_STATUS_0", "description": "Bitfield for current ingredient prime status of pumped ingredients", "address": 432, "boardName": "Pump Board 3", "boardId": 3, "paramType": "BYTE", "defaultParam": "0", "currentValue": "4", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 117, "isPrivate": true}, {"id": 80123, "name": "Ingredient Line Status - C5", "macro": "EEP_INGR_LINE_STATUS_0", "description": "Bitfield for current ingredient prime status of pumped ingredients", "address": 432, "boardName": "Pump Board 4", "boardId": 4, "paramType": "BYTE", "defaultParam": "0", "currentValue": "9", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 117, "isPrivate": true}, {"id": 80124, "name": "Ingredient Line Status - C6", "macro": "EEP_INGR_LINE_STATUS_0", "description": "Bitfield for current ingredient prime status of pumped ingredients", "address": 432, "boardName": "Pump Board 5", "boardId": 5, "paramType": "BYTE", "defaultParam": "0", "currentValue": "8", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 117, "isPrivate": true}, {"id": 80125, "name": "Ingredient Line Status - D1", "macro": "EEP_INGR_LINE_STATUS_1", "description": "Bitfield for current ingredient prime status of pumped ingredients", "address": 433, "boardName": "Pump Board 0", "boardId": 0, "paramType": "BYTE", "defaultParam": "0", "currentValue": "4", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 117, "isPrivate": true}, {"id": 80126, "name": "Ingredient Line Status - D2", "macro": "EEP_INGR_LINE_STATUS_1", "description": "Bitfield for current ingredient prime status of pumped ingredients", "address": 433, "boardName": "Pump Board 1", "boardId": 1, "paramType": "BYTE", "defaultParam": "0", "currentValue": "9", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 117, "isPrivate": true}, {"id": 80127, "name": "Ingredient Line Status - D3", "macro": "EEP_INGR_LINE_STATUS_1", "description": "Bitfield for current ingredient prime status of pumped ingredients", "address": 433, "boardName": "Pump Board 2", "boardId": 2, "paramType": "BYTE", "defaultParam": "0", "currentValue": "4", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 117, "isPrivate": true}, {"id": 80128, "name": "Ingredient Line Status - D4", "macro": "EEP_INGR_LINE_STATUS_1", "description": "Bitfield for current ingredient prime status of pumped ingredients", "address": 433, "boardName": "Pump Board 3", "boardId": 3, "paramType": "BYTE", "defaultParam": "0", "currentValue": "9", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 117, "isPrivate": true}, {"id": 80129, "name": "Ingredient Line Status - D5", "macro": "EEP_INGR_LINE_STATUS_1", "description": "Bitfield for current ingredient prime status of pumped ingredients", "address": 433, "boardName": "Pump Board 4", "boardId": 4, "paramType": "BYTE", "defaultParam": "0", "currentValue": "8", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 117, "isPrivate": true}, {"id": 80130, "name": "Ingredient Line Status - D6", "macro": "EEP_INGR_LINE_STATUS_1", "description": "Bitfield for current ingredient prime status of pumped ingredients", "address": 433, "boardName": "Pump Board 5", "boardId": 5, "paramType": "BYTE", "defaultParam": "0", "currentValue": "8", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 117, "isPrivate": true}, {"id": 80131, "name": "Ingredient Line Status - E1", "macro": "EEP_INGR_LINE_STATUS_2", "description": "Bitfield for current ingredient prime status of pumped ingredients", "address": 434, "boardName": "Pump Board 0", "boardId": 0, "paramType": "BYTE", "defaultParam": "0", "currentValue": "4", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 117, "isPrivate": true}, {"id": 80132, "name": "Ingredient Line Status - E2", "macro": "EEP_INGR_LINE_STATUS_2", "description": "Bitfield for current ingredient prime status of pumped ingredients", "address": 434, "boardName": "Pump Board 1", "boardId": 1, "paramType": "BYTE", "defaultParam": "0", "currentValue": "4", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 117, "isPrivate": true}, {"id": 80133, "name": "Ingredient Line Status - E3", "macro": "EEP_INGR_LINE_STATUS_2", "description": "Bitfield for current ingredient prime status of pumped ingredients", "address": 434, "boardName": "Pump Board 2", "boardId": 2, "paramType": "BYTE", "defaultParam": "0", "currentValue": "4", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 117, "isPrivate": true}, {"id": 80134, "name": "Ingredient Line Status - E4", "macro": "EEP_INGR_LINE_STATUS_2", "description": "Bitfield for current ingredient prime status of pumped ingredients", "address": 434, "boardName": "Pump Board 3", "boardId": 3, "paramType": "BYTE", "defaultParam": "0", "currentValue": "8", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 117, "isPrivate": true}, {"id": 80135, "name": "Ingredient Line Status - E5", "macro": "EEP_INGR_LINE_STATUS_2", "description": "Bitfield for current ingredient prime status of pumped ingredients", "address": 434, "boardName": "Pump Board 4", "boardId": 4, "paramType": "BYTE", "defaultParam": "0", "currentValue": "8", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 117, "isPrivate": true}, {"id": 80136, "name": "Ingredient Line Status - E6", "macro": "EEP_INGR_LINE_STATUS_2", "description": "Bitfield for current ingredient prime status of pumped ingredients", "address": 434, "boardName": "Pump Board 5", "boardId": 5, "paramType": "BYTE", "defaultParam": "0", "currentValue": "9", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 117, "isPrivate": true}, {"id": 80137, "name": "Ingredient Line Status - F1", "macro": "EEP_INGR_LINE_STATUS_3", "description": "Bitfield for current ingredient prime status of pumped ingredients", "address": 435, "boardName": "Pump Board 0", "boardId": 0, "paramType": "BYTE", "defaultParam": "0", "currentValue": "4", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 117, "isPrivate": true}, {"id": 80138, "name": "Ingredient Line Status - F2", "macro": "EEP_INGR_LINE_STATUS_3", "description": "Bitfield for current ingredient prime status of pumped ingredients", "address": 435, "boardName": "Pump Board 1", "boardId": 1, "paramType": "BYTE", "defaultParam": "0", "currentValue": "4", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 117, "isPrivate": true}, {"id": 80139, "name": "Ingredient Line Status - F3", "macro": "EEP_INGR_LINE_STATUS_3", "description": "Bitfield for current ingredient prime status of pumped ingredients", "address": 435, "boardName": "Pump Board 2", "boardId": 2, "paramType": "BYTE", "defaultParam": "0", "currentValue": "4", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 117, "isPrivate": true}, {"id": 80140, "name": "Ingredient Line Status - F4", "macro": "EEP_INGR_LINE_STATUS_3", "description": "Bitfield for current ingredient prime status of pumped ingredients", "address": 435, "boardName": "Pump Board 3", "boardId": 3, "paramType": "BYTE", "defaultParam": "0", "currentValue": "8", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 117, "isPrivate": true}, {"id": 80141, "name": "Ingredient Line Status - F5", "macro": "EEP_INGR_LINE_STATUS_3", "description": "Bitfield for current ingredient prime status of pumped ingredients", "address": 435, "boardName": "Pump Board 4", "boardId": 4, "paramType": "BYTE", "defaultParam": "0", "currentValue": "8", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 117, "isPrivate": true}, {"id": 80142, "name": "Ingredient Line Status - F6", "macro": "EEP_INGR_LINE_STATUS_3", "description": "Bitfield for current ingredient prime status of pumped ingredients", "address": 435, "boardName": "Pump Board 5", "boardId": 5, "paramType": "BYTE", "defaultParam": "0", "currentValue": "8", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 117, "isPrivate": true}, {"id": 80143, "name": "Ingredient Line Status - T1", "macro": "EEP_INGR_LINE_STATUS_0", "description": "Bitfield for current ingredient prime status of pumped ingredients", "address": 432, "boardName": "Pump Board 13", "boardId": 13, "paramType": "BYTE", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 117, "isPrivate": true}, {"id": 80144, "name": "Ingredient Line Status - T2", "macro": "EEP_INGR_LINE_STATUS_1", "description": "Bitfield for current ingredient prime status of pumped ingredients", "address": 433, "boardName": "Pump Board 13", "boardId": 13, "paramType": "BYTE", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 117, "isPrivate": true}, {"id": 80145, "name": "Ingredient Line Status - T3", "macro": "EEP_INGR_LINE_STATUS_2", "description": "Bitfield for current ingredient prime status of pumped ingredients", "address": 434, "boardName": "Pump Board 13", "boardId": 13, "paramType": "BYTE", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 117, "isPrivate": true}, {"id": 80146, "name": "Ingredient Line Status - T4", "macro": "EEP_INGR_LINE_STATUS_3", "description": "Bitfield for current ingredient prime status of pumped ingredients", "address": 435, "boardName": "Pump Board 13", "boardId": 13, "paramType": "BYTE", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 117, "isPrivate": true}, {"id": 80147, "name": "Ingredient Line Status - T5", "macro": "EEP_INGR_LINE_STATUS_0", "description": "Bitfield for current ingredient prime status of pumped ingredients", "address": 432, "boardName": "Pump Board 14", "boardId": 14, "paramType": "BYTE", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 117, "isPrivate": true}, {"id": 80148, "name": "Ingredient Line Status - T6", "macro": "EEP_INGR_LINE_STATUS_1", "description": "Bitfield for current ingredient prime status of pumped ingredients", "address": 433, "boardName": "Pump Board 14", "boardId": 14, "paramType": "BYTE", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 117, "isPrivate": true}, {"id": 80149, "name": "Ingredient Line Status - T7", "macro": "EEP_INGR_LINE_STATUS_2", "description": "Bitfield for current ingredient prime status of pumped ingredients", "address": 434, "boardName": "Pump Board 14", "boardId": 14, "paramType": "BYTE", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 117, "isPrivate": true}, {"id": 80150, "name": "Ingredient Line Status - T8", "macro": "EEP_INGR_LINE_STATUS_3", "description": "Bitfield for current ingredient prime status of pumped ingredients", "address": 435, "boardName": "Pump Board 14", "boardId": 14, "paramType": "BYTE", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 117, "isPrivate": true}, {"id": 80151, "name": "Ingredient Line Status -A6", "macro": "EEP_INGR_LINE_STATUS_2", "description": "Bitfield for current ingredient prime status of pumped ingredients", "address": 434, "boardName": "Pump Board 9", "boardId": 9, "paramType": "BYTE", "defaultParam": "0", "currentValue": "9", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 117, "isPrivate": true}, {"id": 80152, "name": "Ingredient Line Status -B1", "macro": "EEP_INGR_LINE_STATUS_1", "description": "Bitfield for current ingredient prime status of pumped ingredients", "address": 433, "boardName": "Pump Board 7", "boardId": 7, "paramType": "BYTE", "defaultParam": "0", "currentValue": "9", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 117, "isPrivate": true}, {"id": 80153, "name": "Ingredient Mixing Enable - C1", "macro": "EEP_MIXING_ENABLE_0", "description": "Enable ingredient mixing for A1", "address": 96, "boardName": "Master", "boardId": 65535, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 117, "isPrivate": true}, {"id": 80154, "name": "Ingredient Mixing Enable - C2", "macro": "EEP_MIXING_ENABLE_4", "description": "Enable ingredient mixing for B1", "address": 100, "boardName": "Master", "boardId": 65535, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 117, "isPrivate": true}, {"id": 80155, "name": "Ingredient Mixing Enable - C3", "macro": "EEP_MIXING_ENABLE_7", "description": "Enable ingredient mixing for C1", "address": 104, "boardName": "Master", "boardId": 65535, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 117, "isPrivate": true}, {"id": 80156, "name": "Ingredient Mixing Enable - C4", "macro": "EEP_MIXING_ENABLE_12", "description": "Enable ingredient mixing for D1", "address": 108, "boardName": "Master", "boardId": 65535, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 117, "isPrivate": true}, {"id": 80157, "name": "Ingredient Mixing Enable - C5", "macro": "EEP_MIXING_ENABLE_16", "description": "Enable ingredient mixing for E1", "address": 112, "boardName": "Master", "boardId": 65535, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 117, "isPrivate": true}, {"id": 80158, "name": "Ingredient Mixing Enable - C6", "macro": "EEP_MIXING_ENABLE_20", "description": "Enable ingredient mixing for F1", "address": 116, "boardName": "Master", "boardId": 65535, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 117, "isPrivate": true}, {"id": 80159, "name": "Ingredient Mixing Enable - D1", "macro": "EEP_MIXING_ENABLE_1", "description": "Enable ingredient mixing for A2", "address": 97, "boardName": "Master", "boardId": 65535, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 117, "isPrivate": true}, {"id": 80160, "name": "Ingredient Mixing Enable - D2", "macro": "EEP_MIXING_ENABLE_5", "description": "Enable ingredient mixing for B2", "address": 101, "boardName": "Master", "boardId": 65535, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 117, "isPrivate": true}, {"id": 80161, "name": "Ingredient Mixing Enable - D3", "macro": "EEP_MIXING_ENABLE_9", "description": "Enable ingredient mixing for C2", "address": 105, "boardName": "Master", "boardId": 65535, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 117, "isPrivate": true}, {"id": 80162, "name": "Ingredient Mixing Enable - D4", "macro": "EEP_MIXING_ENABLE_13", "description": "Enable ingredient mixing for D2", "address": 109, "boardName": "Master", "boardId": 65535, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 117, "isPrivate": true}, {"id": 80163, "name": "Ingredient Mixing Enable - D5", "macro": "EEP_MIXING_ENABLE_17", "description": "Enable ingredient mixing for E2", "address": 113, "boardName": "Master", "boardId": 65535, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 117, "isPrivate": true}, {"id": 80164, "name": "Ingredient Mixing Enable - D6", "macro": "EEP_MIXING_ENABLE_21", "description": "Enable ingredient mixing for F2", "address": 117, "boardName": "Master", "boardId": 65535, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 117, "isPrivate": true}, {"id": 80165, "name": "Ingredient Mixing Enable - E1", "macro": "EEP_MIXING_ENABLE_2", "description": "Enable ingredient mixing for A3", "address": 98, "boardName": "Master", "boardId": 65535, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 117, "isPrivate": true}, {"id": 80166, "name": "Ingredient Mixing Enable - E2", "macro": "EEP_MIXING_ENABLE_6", "description": "Enable ingredient mixing for B3", "address": 102, "boardName": "Master", "boardId": 65535, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 117, "isPrivate": true}, {"id": 80167, "name": "Ingredient Mixing Enable - E3", "macro": "EEP_MIXING_ENABLE_10", "description": "Enable ingredient mixing for C3", "address": 106, "boardName": "Master", "boardId": 65535, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 117, "isPrivate": true}, {"id": 80168, "name": "Ingredient Mixing Enable - E4", "macro": "EEP_MIXING_ENABLE_14", "description": "Enable ingredient mixing for D3", "address": 110, "boardName": "Master", "boardId": 65535, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 117, "isPrivate": true}, {"id": 80169, "name": "Ingredient Mixing Enable - E5", "macro": "EEP_MIXING_ENABLE_18", "description": "Enable ingredient mixing for E3", "address": 114, "boardName": "Master", "boardId": 65535, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 117, "isPrivate": true}, {"id": 80170, "name": "Ingredient Mixing Enable - E6", "macro": "EEP_MIXING_ENABLE_22", "description": "Enable ingredient mixing for F3", "address": 118, "boardName": "Master", "boardId": 65535, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 117, "isPrivate": true}, {"id": 80171, "name": "Ingredient Mixing Enable - F1", "macro": "EEP_MIXING_ENABLE_3", "description": "Enable ingredient mixing for A4", "address": 99, "boardName": "Master", "boardId": 65535, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 117, "isPrivate": true}, {"id": 80172, "name": "Ingredient Mixing Enable - F2", "macro": "EEP_MIXING_ENABLE_7", "description": "Enable ingredient mixing for B4", "address": 103, "boardName": "Master", "boardId": 65535, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 117, "isPrivate": true}, {"id": 80173, "name": "Ingredient Mixing Enable - F3", "macro": "EEP_MIXING_ENABLE_11", "description": "Enable ingredient mixing for C4", "address": 107, "boardName": "Master", "boardId": 65535, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 117, "isPrivate": true}, {"id": 80174, "name": "Ingredient Mixing Enable - F4", "macro": "EEP_MIXING_ENABLE_15", "description": "Enable ingredient mixing for D4", "address": 111, "boardName": "Master", "boardId": 65535, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 117, "isPrivate": true}, {"id": 80175, "name": "Ingredient Mixing Enable - F5", "macro": "EEP_MIXING_ENABLE_19", "description": "Enable ingredient mixing for E4", "address": 115, "boardName": "Master", "boardId": 65535, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 117, "isPrivate": true}, {"id": 80176, "name": "Ingredient Mixing Enable - F6", "macro": "EEP_MIXING_ENABLE_23", "description": "Enable ingredient mixing for F4", "address": 119, "boardName": "Master", "boardId": 65535, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 117, "isPrivate": true}, {"id": 80177, "name": "Ingredient Mixing Enable - G1", "macro": "EEP_MIXING_ENABLE_24", "description": "Enable ingredient mixing for G1", "address": 120, "boardName": "Master", "boardId": 65535, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 117, "isPrivate": true}, {"id": 80178, "name": "Ingredient Mixing Enable - G2", "macro": "EEP_MIXING_ENABLE_25", "description": "Enable ingredient mixing for G2", "address": 121, "boardName": "Master", "boardId": 65535, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 117, "isPrivate": true}, {"id": 80179, "name": "Ingredient Mixing Enable - G3", "macro": "EEP_MIXING_ENABLE_26", "description": "Enable ingredient mixing for G3", "address": 122, "boardName": "Master", "boardId": 65535, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 117, "isPrivate": true}, {"id": 80180, "name": "Ingredient Mixing Enable - G4", "macro": "EEP_MIXING_ENABLE_27", "description": "Enable ingredient mixing for G4", "address": 123, "boardName": "Master", "boardId": 65535, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 117, "isPrivate": true}, {"id": 80181, "name": "Ingredient Sensor Active Threshold - A1, A2, B1, B2", "macro": "EEP_LEVEL_THRESHOLD", "description": "Ingredient level (expressed in 1/20 fl. oz. increments) below which runout detection becomes active for ingredients A1, A2, B1, B2", "address": 256, "boardName": "Pump Board 7", "boardId": 7, "paramType": "WORD", "defaultParam": "50", "currentValue": "50", "newValue": null, "minParam": "10", "maxParam": "3500", "machineId": 117, "isPrivate": false}, {"id": 80182, "name": "Ingredient Sensor Active Threshold - A3, A4, B3, B4", "macro": "EEP_LEVEL_THRESHOLD", "description": "Ingredient level (expressed in 1/20 fl. oz. increments) below which runout detection becomes active for ingredients A3, A4, B3, B4", "address": 256, "boardName": "Pump Board 8", "boardId": 8, "paramType": "WORD", "defaultParam": "50", "currentValue": "50", "newValue": null, "minParam": "10", "maxParam": "3500", "machineId": 117, "isPrivate": false}, {"id": 80183, "name": "Ingredient Sensor Active Threshold - A5, A6, B5, B6", "macro": "EEP_LEVEL_THRESHOLD", "description": "Ingredient level (expressed in 1/20 fl. oz. increments) below which runout detection becomes active for ingredients A5, A6, B5, B6", "address": 256, "boardName": "Pump Board 9", "boardId": 9, "paramType": "WORD", "defaultParam": "50", "currentValue": "50", "newValue": null, "minParam": "10", "maxParam": "3500", "machineId": 117, "isPrivate": false}, {"id": 80184, "name": "Ingredient Sensor Active Threshold - C1,D1,E1,F1", "macro": "EEP_LEVEL_THRESHOLD", "description": "Ingredient level (expressed in 1/20 fl. oz. increments) below which runout detection becomes active for ingredients C1,D1,E1,F1", "address": 256, "boardName": "Pump Board 0", "boardId": 0, "paramType": "WORD", "defaultParam": "50", "currentValue": "50", "newValue": null, "minParam": "10", "maxParam": "3500", "machineId": 117, "isPrivate": false}, {"id": 80185, "name": "Ingredient Sensor Active Threshold - C2, E2, D2, F2", "macro": "EEP_LEVEL_THRESHOLD", "description": "Ingredient level (expressed in 1/20 fl. oz. increments) below which runout detection becomes active for ingredients C2, E2, D2, F2", "address": 256, "boardName": "Pump Board 1", "boardId": 1, "paramType": "WORD", "defaultParam": "50", "currentValue": "50", "newValue": null, "minParam": "10", "maxParam": "3500", "machineId": 117, "isPrivate": false}, {"id": 80186, "name": "Ingredient Sensor Active Threshold - C3, D3, E3, F3", "macro": "EEP_LEVEL_THRESHOLD", "description": "Ingredient level (expressed in 1/20 fl. oz. increments) below which runout detection becomes active for ingredients C3, D3, E3, F3", "address": 256, "boardName": "Pump Board 2", "boardId": 2, "paramType": "WORD", "defaultParam": "50", "currentValue": "50", "newValue": null, "minParam": "10", "maxParam": "3500", "machineId": 117, "isPrivate": false}, {"id": 80187, "name": "Ingredient Sensor Active Threshold - C4, D4, E4, F4", "macro": "EEP_LEVEL_THRESHOLD", "description": "Ingredient level (expressed in 1/20 fl. oz. increments) below which runout detection becomes active for ingredients C4, D4, E4, F4", "address": 256, "boardName": "Pump Board 3", "boardId": 3, "paramType": "WORD", "defaultParam": "50", "currentValue": "50", "newValue": null, "minParam": "10", "maxParam": "3500", "machineId": 117, "isPrivate": false}, {"id": 80188, "name": "Ingredient Sensor Active Threshold - C5, D5, E5, F5", "macro": "EEP_LEVEL_THRESHOLD", "description": "Ingredient level (expressed in 1/20 fl. oz. increments) below which runout detection becomes active for ingredients C5, D5, E5, F5", "address": 256, "boardName": "Pump Board 4", "boardId": 4, "paramType": "WORD", "defaultParam": "50", "currentValue": "50", "newValue": null, "minParam": "10", "maxParam": "3500", "machineId": 117, "isPrivate": false}, {"id": 80189, "name": "Ingredient Sensor Active Threshold - C6, D6, E6, F6", "macro": "EEP_LEVEL_THRESHOLD", "description": "Ingredient level (expressed in 1/20 fl. oz. increments) below which runout detection becomes active for ingredients C6, D6, E6, F6", "address": 256, "boardName": "Pump Board 5", "boardId": 5, "paramType": "WORD", "defaultParam": "50", "currentValue": "50", "newValue": null, "minParam": "10", "maxParam": "3500", "machineId": 117, "isPrivate": false}, {"id": 80190, "name": "Ingredient Sensor Active Threshold - T1 - T4", "macro": "EEP_LEVEL_THRESHOLD", "description": "Ingredient level (expressed in 1/20 fl. oz. increments) below which runout detection becomes active for ingredients T1 - T4", "address": 256, "boardName": "Pump Board 13", "boardId": 13, "paramType": "WORD", "defaultParam": "100", "currentValue": "100", "newValue": null, "minParam": "10", "maxParam": "3500", "machineId": 117, "isPrivate": false}, {"id": 80191, "name": "Ingredient Sensor Active Threshold - T5 - T8", "macro": "EEP_LEVEL_THRESHOLD", "description": "Ingredient level (expressed in 1/20 fl. oz. increments) below which runout detection becomes active for ingredients T4 - T8", "address": 256, "boardName": "Pump Board 14", "boardId": 14, "paramType": "WORD", "defaultParam": "100", "currentValue": "100", "newValue": null, "minParam": "10", "maxParam": "3500", "machineId": 117, "isPrivate": false}, {"id": 80192, "name": "<PERSON>/<PERSON> Default Ingredient Speed- A1, B1, A2, B2", "macro": "EEP_OZMIN_INGR_SPEED_DEFAULT", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 416, "boardName": "Pump Board 7", "boardId": 7, "paramType": "SINGLE", "defaultParam": "34.5", "currentValue": "22.5", "newValue": null, "minParam": "7.5", "maxParam": "37.5", "machineId": 117, "isPrivate": true}, {"id": 80193, "name": "<PERSON>/<PERSON> De<PERSON>ult Ingredient Speed- A3, B3, A4, B4", "macro": "EEP_OZMIN_INGR_SPEED_DEFAULT", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 416, "boardName": "Pump Board 8", "boardId": 8, "paramType": "SINGLE", "defaultParam": "34.5", "currentValue": "22.5", "newValue": null, "minParam": "7.5", "maxParam": "37.5", "machineId": 117, "isPrivate": true}, {"id": 80194, "name": "<PERSON>/<PERSON> De<PERSON> Ingredient Speed- A5, B5, A6, B6", "macro": "EEP_OZMIN_INGR_SPEED_DEFAULT", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 416, "boardName": "Pump Board 9", "boardId": 9, "paramType": "SINGLE", "defaultParam": "34.5", "currentValue": "22.5", "newValue": null, "minParam": "7.5", "maxParam": "37.5", "machineId": 117, "isPrivate": true}, {"id": 80195, "name": "<PERSON>/<PERSON> De<PERSON>ult Ingredient Speed- C1, D1, E1, F1", "macro": "EEP_OZMIN_INGR_SPEED_DEFAULT", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 416, "boardName": "Pump Board 0", "boardId": 0, "paramType": "SINGLE", "defaultParam": "34.5", "currentValue": "22.5", "newValue": null, "minParam": "7.5", "maxParam": "37.5", "machineId": 117, "isPrivate": true}, {"id": 80196, "name": "<PERSON>/<PERSON> Default Ingredient Speed- C2, D2, E2, F2", "macro": "EEP_OZMIN_INGR_SPEED_DEFAULT", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 416, "boardName": "Pump Board 1", "boardId": 1, "paramType": "SINGLE", "defaultParam": "34.5", "currentValue": "22.5", "newValue": null, "minParam": "7.5", "maxParam": "37.5", "machineId": 117, "isPrivate": true}, {"id": 80197, "name": "<PERSON>/<PERSON> Default Ingredient Speed- C3, D3, E3, F3", "macro": "EEP_OZMIN_INGR_SPEED_DEFAULT", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 416, "boardName": "Pump Board 2", "boardId": 2, "paramType": "SINGLE", "defaultParam": "34.5", "currentValue": "22.5", "newValue": null, "minParam": "7.5", "maxParam": "37.5", "machineId": 117, "isPrivate": true}, {"id": 80198, "name": "<PERSON>/<PERSON> De<PERSON>ult Ingredient Speed- C4, D4, E4, F4", "macro": "EEP_OZMIN_INGR_SPEED_DEFAULT", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 416, "boardName": "Pump Board 3", "boardId": 3, "paramType": "SINGLE", "defaultParam": "34.5", "currentValue": "22.5", "newValue": null, "minParam": "7.5", "maxParam": "37.5", "machineId": 117, "isPrivate": true}, {"id": 80199, "name": "<PERSON>/<PERSON> De<PERSON>ult Ingredient Speed- C5, D5, E5, F5", "macro": "EEP_OZMIN_INGR_SPEED_DEFAULT", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 416, "boardName": "Pump Board 4", "boardId": 4, "paramType": "SINGLE", "defaultParam": "34.5", "currentValue": "22.5", "newValue": null, "minParam": "7.5", "maxParam": "37.5", "machineId": 117, "isPrivate": true}, {"id": 80200, "name": "<PERSON>/<PERSON> De<PERSON>ult Ingredient Speed- C6, D6, E6, F6", "macro": "EEP_OZMIN_INGR_SPEED_DEFAULT", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 416, "boardName": "Pump Board 5", "boardId": 5, "paramType": "SINGLE", "defaultParam": "34.5", "currentValue": "22.5", "newValue": null, "minParam": "7.5", "maxParam": "37.5", "machineId": 117, "isPrivate": true}, {"id": 80201, "name": "<PERSON>/<PERSON> De<PERSON>ult Ingredient Speed- T1 - T4", "macro": "EEP_OZMIN_INGR_SPEED_DEFAULT", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 416, "boardName": "Pump Board 13", "boardId": 13, "paramType": "SINGLE", "defaultParam": "8.0", "currentValue": "22.5", "newValue": null, "minParam": "7.5", "maxParam": "37.5", "machineId": 117, "isPrivate": true}, {"id": 80202, "name": "<PERSON>/<PERSON> De<PERSON> Ingredient Speed- T5 - T8", "macro": "EEP_OZMIN_INGR_SPEED_DEFAULT", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 416, "boardName": "Pump Board 14", "boardId": 14, "paramType": "SINGLE", "defaultParam": "8.0", "currentValue": "22.5", "newValue": null, "minParam": "7.5", "maxParam": "37.5", "machineId": 117, "isPrivate": true}, {"id": 80203, "name": "Priming Quantity - A1", "macro": "EEP_PRIMING_QTY_0", "description": "Priming quantity (expressed in 1/20 fl. oz. increments) for ingredient A1", "address": 84, "boardName": "Pump Board 7", "boardId": 7, "paramType": "BYTE", "defaultParam": "46", "currentValue": "46", "newValue": null, "minParam": "40", "maxParam": "110", "machineId": 117, "isPrivate": true}, {"id": 80204, "name": "Priming Quantity - A2", "macro": "EEP_PRIMING_QTY_2", "description": "Priming quantity (expressed in 1/20 fl. oz. increments) for ingredient A2", "address": 116, "boardName": "Pump Board 7", "boardId": 7, "paramType": "BYTE", "defaultParam": "56", "currentValue": "56", "newValue": null, "minParam": "40", "maxParam": "110", "machineId": 117, "isPrivate": true}, {"id": 80205, "name": "Priming Quantity - A3", "macro": "EEP_PRIMING_QTY_0", "description": "Priming quantity (expressed in 1/20 fl. oz. increments) for ingredient A3", "address": 84, "boardName": "Pump Board 8", "boardId": 8, "paramType": "BYTE", "defaultParam": "46", "currentValue": "46", "newValue": null, "minParam": "40", "maxParam": "110", "machineId": 117, "isPrivate": true}, {"id": 80206, "name": "Priming Quantity - A4", "macro": "EEP_PRIMING_QTY_2", "description": "Priming quantity (expressed in 1/20 fl. oz. increments) for ingredient A4", "address": 116, "boardName": "Pump Board 8", "boardId": 8, "paramType": "BYTE", "defaultParam": "57", "currentValue": "57", "newValue": null, "minParam": "40", "maxParam": "110", "machineId": 117, "isPrivate": true}, {"id": 80207, "name": "Priming Quantity - A5", "macro": "EEP_PRIMING_QTY_0", "description": "Priming quantity (expressed in 1/20 fl. oz. increments) for ingredient A5", "address": 84, "boardName": "Pump Board 9", "boardId": 9, "paramType": "BYTE", "defaultParam": "50", "currentValue": "50", "newValue": null, "minParam": "40", "maxParam": "110", "machineId": 117, "isPrivate": true}, {"id": 80208, "name": "Priming Quantity - A6", "macro": "EEP_PRIMING_QTY_2", "description": "Priming quantity (expressed in 1/20 fl. oz. increments) for ingredient A6", "address": 116, "boardName": "Pump Board 9", "boardId": 9, "paramType": "BYTE", "defaultParam": "65", "currentValue": "65", "newValue": null, "minParam": "40", "maxParam": "110", "machineId": 117, "isPrivate": true}, {"id": 80209, "name": "Priming Quantity - B1", "macro": "EEP_PRIMING_QTY_1", "description": "Priming quantity (expressed in 1/20 fl. oz. increments) for ingredient B1", "address": 100, "boardName": "Pump Board 7", "boardId": 7, "paramType": "BYTE", "defaultParam": "51", "currentValue": "51", "newValue": null, "minParam": "40", "maxParam": "110", "machineId": 117, "isPrivate": true}, {"id": 80210, "name": "Priming Quantity - B2", "macro": "EEP_PRIMING_QTY_3", "description": "Priming quantity (expressed in 1/20 fl. oz. increments) for ingredient B2", "address": 132, "boardName": "Pump Board 7", "boardId": 7, "paramType": "BYTE", "defaultParam": "55", "currentValue": "55", "newValue": null, "minParam": "40", "maxParam": "110", "machineId": 117, "isPrivate": true}, {"id": 80211, "name": "Priming Quantity - B3", "macro": "EEP_PRIMING_QTY_1", "description": "Priming quantity (expressed in 1/20 fl. oz. increments) for ingredient B3", "address": 100, "boardName": "Pump Board 8", "boardId": 8, "paramType": "BYTE", "defaultParam": "51", "currentValue": "51", "newValue": null, "minParam": "40", "maxParam": "110", "machineId": 117, "isPrivate": true}, {"id": 80212, "name": "Priming Quantity - B4", "macro": "EEP_PRIMING_QTY_3", "description": "Priming quantity (expressed in 1/20 fl. oz. increments) for ingredient B4", "address": 132, "boardName": "Pump Board 8", "boardId": 8, "paramType": "BYTE", "defaultParam": "55", "currentValue": "55", "newValue": null, "minParam": "40", "maxParam": "110", "machineId": 117, "isPrivate": true}, {"id": 80213, "name": "Priming Quantity - B5", "macro": "EEP_PRIMING_QTY_1", "description": "Priming quantity (expressed in 1/20 fl. oz. increments) for ingredient B5", "address": 100, "boardName": "Pump Board 9", "boardId": 9, "paramType": "BYTE", "defaultParam": "51", "currentValue": "56", "newValue": null, "minParam": "40", "maxParam": "110", "machineId": 117, "isPrivate": true}, {"id": 80214, "name": "Priming Quantity - B6", "macro": "EEP_PRIMING_QTY_3", "description": "Priming quantity (expressed in 1/20 fl. oz. increments) for ingredient B6", "address": 132, "boardName": "Pump Board 9", "boardId": 9, "paramType": "BYTE", "defaultParam": "58", "currentValue": "58", "newValue": null, "minParam": "40", "maxParam": "110", "machineId": 117, "isPrivate": true}, {"id": 80215, "name": "Priming Quantity - Bib 1", "macro": "EEP_BIB_PRIME_QTY_0", "description": "Priming quantity (expressed in 1/20th oz increments) for ingredient Bib 1", "address": 112, "boardName": "Nozzle", "boardId": 560, "paramType": "WORD", "defaultParam": "40", "currentValue": "40", "newValue": null, "minParam": "40", "maxParam": "400", "machineId": 117, "isPrivate": true}, {"id": 80216, "name": "Priming Quantity - Bib 2", "macro": "EEP_BIB_PRIME_QTY_1", "description": "Priming quantity (expressed in 1/20th oz increments) for ingredient Bib 2", "address": 114, "boardName": "Nozzle", "boardId": 560, "paramType": "WORD", "defaultParam": "40", "currentValue": "40", "newValue": null, "minParam": "40", "maxParam": "400", "machineId": 117, "isPrivate": true}, {"id": 80217, "name": "Priming Quantity - Bib 3", "macro": "EEP_BIB_PRIME_QTY_2", "description": "Priming quantity (expressed in 1/20th oz increments) for ingredient Bib 3", "address": 116, "boardName": "Nozzle", "boardId": 560, "paramType": "WORD", "defaultParam": "40", "currentValue": "40", "newValue": null, "minParam": "40", "maxParam": "400", "machineId": 117, "isPrivate": true}, {"id": 80218, "name": "Priming Quantity - Bib 4", "macro": "EEP_BIB_PRIME_QTY_3", "description": "Priming quantity (expressed in 1/20th oz increments) for ingredient Bib 4", "address": 118, "boardName": "Nozzle", "boardId": 560, "paramType": "WORD", "defaultParam": "40", "currentValue": "40", "newValue": null, "minParam": "40", "maxParam": "400", "machineId": 117, "isPrivate": true}, {"id": 80219, "name": "Priming Quantity - C1", "macro": "EEP_PRIMING_QTY_0", "description": "Priming quantity (expressed in 1/20 fl. oz. increments) for ingredient C1", "address": 84, "boardName": "Pump Board 0", "boardId": 0, "paramType": "BYTE", "defaultParam": "54", "currentValue": "54", "newValue": null, "minParam": "40", "maxParam": "110", "machineId": 117, "isPrivate": true}, {"id": 80220, "name": "Priming Quantity - C2", "macro": "EEP_PRIMING_QTY_0", "description": "Priming quantity (expressed in 1/20 fl. oz. increments) for ingredient C2", "address": 84, "boardName": "Pump Board 1", "boardId": 1, "paramType": "BYTE", "defaultParam": "50", "currentValue": "50", "newValue": null, "minParam": "40", "maxParam": "110", "machineId": 117, "isPrivate": true}, {"id": 80221, "name": "Priming Quantity - C3", "macro": "EEP_PRIMING_QTY_0", "description": "Priming quantity (expressed in 1/20 fl. oz. increments) for ingredient C3", "address": 84, "boardName": "Pump Board 2", "boardId": 2, "paramType": "BYTE", "defaultParam": "57", "currentValue": "57", "newValue": null, "minParam": "40", "maxParam": "110", "machineId": 117, "isPrivate": true}, {"id": 80222, "name": "Priming Quantity - C4", "macro": "EEP_PRIMING_QTY_0", "description": "Priming quantity (expressed in 1/20 fl. oz. increments) for ingredient C4", "address": 84, "boardName": "Pump Board 3", "boardId": 3, "paramType": "BYTE", "defaultParam": "50", "currentValue": "50", "newValue": null, "minParam": "40", "maxParam": "110", "machineId": 117, "isPrivate": true}, {"id": 80223, "name": "Priming Quantity - C5", "macro": "EEP_PRIMING_QTY_0", "description": "Priming quantity (expressed in 1/20 fl. oz. increments) for ingredient C5", "address": 84, "boardName": "Pump Board 4", "boardId": 4, "paramType": "BYTE", "defaultParam": "55", "currentValue": "55", "newValue": null, "minParam": "40", "maxParam": "110", "machineId": 117, "isPrivate": true}, {"id": 80224, "name": "Priming Quantity - C6", "macro": "EEP_PRIMING_QTY_0", "description": "Priming quantity (expressed in 1/20 fl. oz. increments) for ingredient C6", "address": 84, "boardName": "Pump Board 5", "boardId": 5, "paramType": "BYTE", "defaultParam": "55", "currentValue": "55", "newValue": null, "minParam": "40", "maxParam": "110", "machineId": 117, "isPrivate": true}, {"id": 80225, "name": "Priming Quantity - D1", "macro": "EEP_PRIMING_QTY_1", "description": "Priming quantity (expressed in 1/20 fl. oz. increments) for ingredient D1", "address": 100, "boardName": "Pump Board 0", "boardId": 0, "paramType": "BYTE", "defaultParam": "64", "currentValue": "64", "newValue": null, "minParam": "40", "maxParam": "110", "machineId": 117, "isPrivate": true}, {"id": 80226, "name": "Priming Quantity - D2", "macro": "EEP_PRIMING_QTY_1", "description": "Priming quantity (expressed in 1/20 fl. oz. increments) for ingredient D2", "address": 100, "boardName": "Pump Board 1", "boardId": 1, "paramType": "BYTE", "defaultParam": "48", "currentValue": "48", "newValue": null, "minParam": "40", "maxParam": "110", "machineId": 117, "isPrivate": true}, {"id": 80227, "name": "Priming Quantity - D3", "macro": "EEP_PRIMING_QTY_1", "description": "Priming quantity (expressed in 1/20 fl. oz. increments) for ingredient D3", "address": 100, "boardName": "Pump Board 2", "boardId": 2, "paramType": "BYTE", "defaultParam": "56", "currentValue": "56", "newValue": null, "minParam": "40", "maxParam": "110", "machineId": 117, "isPrivate": true}, {"id": 80228, "name": "Priming Quantity - D4", "macro": "EEP_PRIMING_QTY_1", "description": "Priming quantity (expressed in 1/20 fl. oz. increments) for ingredient D4", "address": 100, "boardName": "Pump Board 3", "boardId": 3, "paramType": "BYTE", "defaultParam": "51", "currentValue": "51", "newValue": null, "minParam": "40", "maxParam": "110", "machineId": 117, "isPrivate": true}, {"id": 80229, "name": "Priming Quantity - D5", "macro": "EEP_PRIMING_QTY_1", "description": "Priming quantity (expressed in 1/20 fl. oz. increments) for ingredient D5", "address": 100, "boardName": "Pump Board 4", "boardId": 4, "paramType": "BYTE", "defaultParam": "55", "currentValue": "55", "newValue": null, "minParam": "40", "maxParam": "110", "machineId": 117, "isPrivate": true}, {"id": 80230, "name": "Priming Quantity - D6", "macro": "EEP_PRIMING_QTY_1", "description": "Priming quantity (expressed in 1/20 fl. oz. increments) for ingredient D6", "address": 100, "boardName": "Pump Board 5", "boardId": 5, "paramType": "BYTE", "defaultParam": "52", "currentValue": "52", "newValue": null, "minParam": "40", "maxParam": "110", "machineId": 117, "isPrivate": true}, {"id": 80231, "name": "Priming Quantity - E1", "macro": "EEP_PRIMING_QTY_2", "description": "Priming quantity (expressed in 1/20 fl. oz. increments) for ingredient E1", "address": 116, "boardName": "Pump Board 0", "boardId": 0, "paramType": "BYTE", "defaultParam": "60", "currentValue": "60", "newValue": null, "minParam": "40", "maxParam": "110", "machineId": 117, "isPrivate": true}, {"id": 80232, "name": "Priming Quantity - E2", "macro": "EEP_PRIMING_QTY_2", "description": "Priming quantity (expressed in 1/20 fl. oz. increments) for ingredient E2", "address": 116, "boardName": "Pump Board 1", "boardId": 1, "paramType": "BYTE", "defaultParam": "46", "currentValue": "46", "newValue": null, "minParam": "40", "maxParam": "110", "machineId": 117, "isPrivate": true}, {"id": 80233, "name": "Priming Quantity - E3", "macro": "EEP_PRIMING_QTY_2", "description": "Priming quantity (expressed in 1/20 fl. oz. increments) for ingredient E3", "address": 116, "boardName": "Pump Board 2", "boardId": 2, "paramType": "BYTE", "defaultParam": "63", "currentValue": "63", "newValue": null, "minParam": "40", "maxParam": "110", "machineId": 117, "isPrivate": true}, {"id": 80234, "name": "Priming Quantity - E4", "macro": "EEP_PRIMING_QTY_2", "description": "Priming quantity (expressed in 1/20 fl. oz. increments) for ingredient E4", "address": 116, "boardName": "Pump Board 3", "boardId": 3, "paramType": "BYTE", "defaultParam": "48", "currentValue": "48", "newValue": null, "minParam": "40", "maxParam": "110", "machineId": 117, "isPrivate": true}, {"id": 80235, "name": "Priming Quantity - E5", "macro": "EEP_PRIMING_QTY_2", "description": "Priming quantity (expressed in 1/20 fl. oz. increments) for ingredient E5", "address": 116, "boardName": "Pump Board 4", "boardId": 4, "paramType": "BYTE", "defaultParam": "64", "currentValue": "64", "newValue": null, "minParam": "40", "maxParam": "110", "machineId": 117, "isPrivate": true}, {"id": 80236, "name": "Priming Quantity - E6", "macro": "EEP_PRIMING_QTY_2", "description": "Priming quantity (expressed in 1/20 fl. oz. increments) for ingredient E6", "address": 116, "boardName": "Pump Board 5", "boardId": 5, "paramType": "BYTE", "defaultParam": "51", "currentValue": "51", "newValue": null, "minParam": "40", "maxParam": "110", "machineId": 117, "isPrivate": true}, {"id": 80237, "name": "Priming Quantity - F1", "macro": "EEP_PRIMING_QTY_3", "description": "Priming quantity (expressed in 1/20 fl. oz. increments) for ingredient F1", "address": 132, "boardName": "Pump Board 0", "boardId": 0, "paramType": "BYTE", "defaultParam": "66", "currentValue": "66", "newValue": null, "minParam": "40", "maxParam": "110", "machineId": 117, "isPrivate": true}, {"id": 80238, "name": "Priming Quantity - F2", "macro": "EEP_PRIMING_QTY_3", "description": "Priming quantity (expressed in 1/20 fl. oz. increments) for ingredient F2", "address": 132, "boardName": "Pump Board 1", "boardId": 1, "paramType": "BYTE", "defaultParam": "46", "currentValue": "46", "newValue": null, "minParam": "40", "maxParam": "110", "machineId": 117, "isPrivate": true}, {"id": 80239, "name": "Priming Quantity - F3", "macro": "EEP_PRIMING_QTY_3", "description": "Priming quantity (expressed in 1/20 fl. oz. increments) for ingredient F3", "address": 132, "boardName": "Pump Board 2", "boardId": 2, "paramType": "BYTE", "defaultParam": "62", "currentValue": "62", "newValue": null, "minParam": "40", "maxParam": "110", "machineId": 117, "isPrivate": true}, {"id": 80240, "name": "Priming Quantity - F4", "macro": "EEP_PRIMING_QTY_3", "description": "Priming quantity (expressed in 1/20 fl. oz. increments) for ingredient F4", "address": 132, "boardName": "Pump Board 3", "boardId": 3, "paramType": "BYTE", "defaultParam": "47", "currentValue": "47", "newValue": null, "minParam": "40", "maxParam": "110", "machineId": 117, "isPrivate": true}, {"id": 80241, "name": "Priming Quantity - F5", "macro": "EEP_PRIMING_QTY_3", "description": "Priming quantity (expressed in 1/20 fl. oz. increments) for ingredient F5", "address": 132, "boardName": "Pump Board 4", "boardId": 4, "paramType": "BYTE", "defaultParam": "63", "currentValue": "63", "newValue": null, "minParam": "40", "maxParam": "110", "machineId": 117, "isPrivate": true}, {"id": 80242, "name": "Priming Quantity - F6", "macro": "EEP_PRIMING_QTY_3", "description": "Priming quantity (expressed in 1/20 fl. oz. increments) for ingredient F6", "address": 132, "boardName": "Pump Board 5", "boardId": 5, "paramType": "BYTE", "defaultParam": "48", "currentValue": "48", "newValue": null, "minParam": "40", "maxParam": "110", "machineId": 117, "isPrivate": true}, {"id": 80243, "name": "Priming Quantity - Soda Bib", "macro": "EEP_BIB_PRIME_QTY_SODA", "description": "Priming quantity (expressed in 1/20th oz increments) for the soda bib", "address": 120, "boardName": "Nozzle", "boardId": 560, "paramType": "WORD", "defaultParam": "40", "currentValue": "40", "newValue": null, "minParam": "40", "maxParam": "400", "machineId": 117, "isPrivate": true}, {"id": 80244, "name": "Priming Quantity - T1", "macro": "EEP_PRIMING_QTY_0", "description": "Priming quantity (expressed in 1/20 fl. oz. increments) for ingredient T1", "address": 84, "boardName": "Pump Board 13", "boardId": 13, "paramType": "BYTE", "defaultParam": "40", "currentValue": "50", "newValue": null, "minParam": "40", "maxParam": "110", "machineId": 117, "isPrivate": true}, {"id": 80245, "name": "Priming Quantity - T3", "macro": "EEP_PRIMING_QTY_2", "description": "Priming quantity (expressed in 1/20 fl. oz. increments) for ingredient T3", "address": 116, "boardName": "Pump Board 13", "boardId": 13, "paramType": "BYTE", "defaultParam": "40", "currentValue": "65", "newValue": null, "minParam": "40", "maxParam": "110", "machineId": 117, "isPrivate": true}, {"id": 80246, "name": "Priming Quantity - T4", "macro": "EEP_PRIMING_QTY_3", "description": "Priming quantity (expressed in 1/20 fl. oz. increments) for ingredient T4", "address": 132, "boardName": "Pump Board 13", "boardId": 13, "paramType": "BYTE", "defaultParam": "40", "currentValue": "58", "newValue": null, "minParam": "40", "maxParam": "110", "machineId": 117, "isPrivate": true}, {"id": 80247, "name": "Priming Quantity - T5", "macro": "EEP_PRIMING_QTY_0", "description": "Priming quantity (expressed in 1/20 fl. oz. increments) for ingredient A5", "address": 84, "boardName": "Pump Board 14", "boardId": 14, "paramType": "BYTE", "defaultParam": "40", "currentValue": "50", "newValue": null, "minParam": "40", "maxParam": "110", "machineId": 117, "isPrivate": true}, {"id": 80248, "name": "Priming Quantity - T6", "macro": "EEP_PRIMING_QTY_1", "description": "Priming quantity (expressed in 1/20 fl. oz. increments) for ingredient T6", "address": 100, "boardName": "Pump Board 14", "boardId": 14, "paramType": "BYTE", "defaultParam": "40", "currentValue": "51", "newValue": null, "minParam": "40", "maxParam": "110", "machineId": 117, "isPrivate": true}, {"id": 80249, "name": "Priming Quantity - T7", "macro": "EEP_PRIMING_QTY_2", "description": "Priming quantity (expressed in 1/20 fl. oz. increments) for ingredient T7", "address": 116, "boardName": "Pump Board 14", "boardId": 14, "paramType": "BYTE", "defaultParam": "65", "currentValue": "65", "newValue": null, "minParam": "40", "maxParam": "110", "machineId": 117, "isPrivate": true}, {"id": 80250, "name": "Priming Quantity - T8", "macro": "EEP_PRIMING_QTY_3", "description": "Priming quantity (expressed in 1/20 fl. oz. increments) for ingredient T8", "address": 132, "boardName": "Pump Board 14", "boardId": 14, "paramType": "BYTE", "defaultParam": "58", "currentValue": "58", "newValue": null, "minParam": "40", "maxParam": "110", "machineId": 117, "isPrivate": true}, {"id": 80251, "name": "Priming Quantity - Water Bib", "macro": "EEP_BIB_PRIME_QTY_WATER", "description": "Priming quantity (expressed in 1/20th oz increments) for the water bib", "address": 122, "boardName": "Nozzle", "boardId": 560, "paramType": "WORD", "defaultParam": "40", "currentValue": "40", "newValue": null, "minParam": "40", "maxParam": "400", "machineId": 117, "isPrivate": true}, {"id": 80252, "name": "Priming Quantity -T2", "macro": "EEP_PRIMING_QTY_1", "description": "Priming quantity (expressed in 1/20 fl. oz. increments) for ingredient T2", "address": 100, "boardName": "Pump Board 13", "boardId": 13, "paramType": "BYTE", "defaultParam": "40", "currentValue": "51", "newValue": null, "minParam": "40", "maxParam": "110", "machineId": 117, "isPrivate": true}, {"id": 80253, "name": "Refresh Line Timeout - A1", "macro": "EEP_REFRESH_TIMEOUT_0", "description": "Time (in seconds) this ingredients can remain in the lines without any dispensing operations. Set to 0 for no timeout.", "address": 336, "boardName": "Pump Board 7", "boardId": 7, "paramType": "WORD", "defaultParam": "0", "currentValue": "3600", "newValue": null, "minParam": "0", "maxParam": "10800", "machineId": 117, "isPrivate": true}, {"id": 80254, "name": "Refresh Line Timeout - A2", "macro": "EEP_REFRESH_TIMEOUT_2", "description": "Time (in seconds) this ingredients can remain in the lines without any dispensing operations. Set to 0 for no timeout.", "address": 340, "boardName": "Pump Board 7", "boardId": 7, "paramType": "WORD", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "10800", "machineId": 117, "isPrivate": true}, {"id": 80255, "name": "Refresh Line Timeout - A3", "macro": "EEP_REFRESH_TIMEOUT_0", "description": "Time (in seconds) this ingredients can remain in the lines without any dispensing operations. Set to 0 for no timeout.", "address": 336, "boardName": "Pump Board 8", "boardId": 8, "paramType": "WORD", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "10800", "machineId": 117, "isPrivate": true}, {"id": 80256, "name": "Refresh Line Timeout - A4", "macro": "EEP_REFRESH_TIMEOUT_2", "description": "Time (in seconds) this ingredients can remain in the lines without any dispensing operations. Set to 0 for no timeout.", "address": 340, "boardName": "Pump Board 8", "boardId": 8, "paramType": "WORD", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "10800", "machineId": 117, "isPrivate": true}, {"id": 80257, "name": "Refresh Line Timeout - A5", "macro": "EEP_REFRESH_TIMEOUT_0", "description": "Time (in seconds) this ingredients can remain in the lines without any dispensing operations. Set to 0 for no timeout.", "address": 336, "boardName": "Pump Board 9", "boardId": 9, "paramType": "WORD", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "10800", "machineId": 117, "isPrivate": true}, {"id": 80258, "name": "Refresh Line Timeout - A6", "macro": "EEP_REFRESH_TIMEOUT_2", "description": "Time (in seconds) this ingredients can remain in the lines without any dispensing operations. Set to 0 for no timeout.", "address": 340, "boardName": "Pump Board 9", "boardId": 9, "paramType": "WORD", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "10800", "machineId": 117, "isPrivate": true}, {"id": 80259, "name": "Refresh Line Timeout - B1", "macro": "EEP_REFRESH_TIMEOUT_1", "description": "Time (in seconds) this ingredients can remain in the lines without any dispensing operations. Set to 0 for no timeout.", "address": 338, "boardName": "Pump Board 7", "boardId": 7, "paramType": "WORD", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "10800", "machineId": 117, "isPrivate": true}, {"id": 80260, "name": "Refresh Line Timeout - B2", "macro": "EEP_REFRESH_TIMEOUT_3", "description": "Time (in seconds) this ingredients can remain in the lines without any dispensing operations. Set to 0 for no timeout.", "address": 342, "boardName": "Pump Board 7", "boardId": 7, "paramType": "WORD", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "10800", "machineId": 117, "isPrivate": true}, {"id": 80261, "name": "Refresh Line Timeout - B3", "macro": "EEP_REFRESH_TIMEOUT_1", "description": "Time (in seconds) this ingredients can remain in the lines without any dispensing operations. Set to 0 for no timeout.", "address": 338, "boardName": "Pump Board 8", "boardId": 8, "paramType": "WORD", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "10800", "machineId": 117, "isPrivate": true}, {"id": 80262, "name": "Refresh Line Timeout - B4", "macro": "EEP_REFRESH_TIMEOUT_3", "description": "Time (in seconds) this ingredients can remain in the lines without any dispensing operations. Set to 0 for no timeout.", "address": 342, "boardName": "Pump Board 8", "boardId": 8, "paramType": "WORD", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "10800", "machineId": 117, "isPrivate": true}, {"id": 80263, "name": "Refresh Line Timeout - B5", "macro": "EEP_REFRESH_TIMEOUT_1", "description": "Time (in seconds) this ingredients can remain in the lines without any dispensing operations. Set to 0 for no timeout.", "address": 338, "boardName": "Pump Board 9", "boardId": 9, "paramType": "WORD", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "10800", "machineId": 117, "isPrivate": true}, {"id": 80264, "name": "Refresh Line Timeout - B6", "macro": "EEP_REFRESH_TIMEOUT_3", "description": "Time (in seconds) this ingredients can remain in the lines without any dispensing operations. Set to 0 for no timeout.", "address": 342, "boardName": "Pump Board 9", "boardId": 9, "paramType": "WORD", "defaultParam": "0", "currentValue": "3600", "newValue": null, "minParam": "0", "maxParam": "10800", "machineId": 117, "isPrivate": true}, {"id": 80265, "name": "Refresh Line Timeout - C1", "macro": "EEP_REFRESH_TIMEOUT_0", "description": "Time (in seconds) this ingredients can remain in the lines without any dispensing operations. Set to 0 for no timeout.", "address": 336, "boardName": "Pump Board 0", "boardId": 0, "paramType": "WORD", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "10800", "machineId": 117, "isPrivate": true}, {"id": 80266, "name": "Refresh Line Timeout - C2", "macro": "EEP_REFRESH_TIMEOUT_0", "description": "Time (in seconds) this ingredients can remain in the lines without any dispensing operations. Set to 0 for no timeout.", "address": 336, "boardName": "Pump Board 1", "boardId": 1, "paramType": "WORD", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "10800", "machineId": 117, "isPrivate": true}, {"id": 80267, "name": "Refresh Line Timeout - C3", "macro": "EEP_REFRESH_TIMEOUT_0", "description": "Time (in seconds) this ingredients can remain in the lines without any dispensing operations. Set to 0 for no timeout.", "address": 336, "boardName": "Pump Board 2", "boardId": 2, "paramType": "WORD", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "10800", "machineId": 117, "isPrivate": true}, {"id": 80268, "name": "Refresh Line Timeout - C4", "macro": "EEP_REFRESH_TIMEOUT_0", "description": "Time (in seconds) this ingredients can remain in the lines without any dispensing operations. Set to 0 for no timeout.", "address": 336, "boardName": "Pump Board 3", "boardId": 3, "paramType": "WORD", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "10800", "machineId": 117, "isPrivate": true}, {"id": 80269, "name": "Refresh Line Timeout - C5", "macro": "EEP_REFRESH_TIMEOUT_0", "description": "Time (in seconds) this ingredients can remain in the lines without any dispensing operations. Set to 0 for no timeout.", "address": 336, "boardName": "Pump Board 4", "boardId": 4, "paramType": "WORD", "defaultParam": "0", "currentValue": "7200", "newValue": null, "minParam": "0", "maxParam": "10800", "machineId": 117, "isPrivate": true}, {"id": 80270, "name": "Refresh Line Timeout - C6", "macro": "EEP_REFRESH_TIMEOUT_0", "description": "Time (in seconds) this ingredients can remain in the lines without any dispensing operations. Set to 0 for no timeout.", "address": 336, "boardName": "Pump Board 5", "boardId": 5, "paramType": "WORD", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "10800", "machineId": 117, "isPrivate": true}, {"id": 80271, "name": "Refresh Line Timeout - D1", "macro": "EEP_REFRESH_TIMEOUT_1", "description": "Time (in seconds) this ingredients can remain in the lines without any dispensing operations. Set to 0 for no timeout.", "address": 338, "boardName": "Pump Board 0", "boardId": 0, "paramType": "WORD", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "10800", "machineId": 117, "isPrivate": true}, {"id": 80272, "name": "Refresh Line Timeout - D2", "macro": "EEP_REFRESH_TIMEOUT_1", "description": "Time (in seconds) this ingredients can remain in the lines without any dispensing operations. Set to 0 for no timeout.", "address": 338, "boardName": "Pump Board 1", "boardId": 1, "paramType": "WORD", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "10800", "machineId": 117, "isPrivate": true}, {"id": 80273, "name": "Refresh Line Timeout - D3", "macro": "EEP_REFRESH_TIMEOUT_1", "description": "Time (in seconds) this ingredients can remain in the lines without any dispensing operations. Set to 0 for no timeout.", "address": 338, "boardName": "Pump Board 2", "boardId": 2, "paramType": "WORD", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "10800", "machineId": 117, "isPrivate": true}, {"id": 80274, "name": "Refresh Line Timeout - D4", "macro": "EEP_REFRESH_TIMEOUT_1", "description": "Time (in seconds) this ingredients can remain in the lines without any dispensing operations. Set to 0 for no timeout.", "address": 338, "boardName": "Pump Board 3", "boardId": 3, "paramType": "WORD", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "10800", "machineId": 117, "isPrivate": true}, {"id": 80275, "name": "Refresh Line Timeout - D5", "macro": "EEP_REFRESH_TIMEOUT_1", "description": "Time (in seconds) this ingredients can remain in the lines without any dispensing operations. Set to 0 for no timeout.", "address": 338, "boardName": "Pump Board 4", "boardId": 4, "paramType": "WORD", "defaultParam": "0", "currentValue": "7200", "newValue": null, "minParam": "0", "maxParam": "10800", "machineId": 117, "isPrivate": true}, {"id": 80276, "name": "Refresh Line Timeout - D6", "macro": "EEP_REFRESH_TIMEOUT_1", "description": "Time (in seconds) this ingredients can remain in the lines without any dispensing operations. Set to 0 for no timeout.", "address": 338, "boardName": "Pump Board 5", "boardId": 5, "paramType": "WORD", "defaultParam": "0", "currentValue": "3600", "newValue": null, "minParam": "0", "maxParam": "10800", "machineId": 117, "isPrivate": true}, {"id": 80277, "name": "Refresh Line Timeout - E1", "macro": "EEP_REFRESH_TIMEOUT_2", "description": "Time (in seconds) this ingredients can remain in the lines without any dispensing operations. Set to 0 for no timeout.", "address": 340, "boardName": "Pump Board 0", "boardId": 0, "paramType": "WORD", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "10800", "machineId": 117, "isPrivate": true}, {"id": 80278, "name": "Refresh Line Timeout - E2", "macro": "EEP_REFRESH_TIMEOUT_2", "description": "Time (in seconds) this ingredients can remain in the lines without any dispensing operations. Set to 0 for no timeout.", "address": 340, "boardName": "Pump Board 1", "boardId": 1, "paramType": "WORD", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "10800", "machineId": 117, "isPrivate": true}, {"id": 80279, "name": "Refresh Line Timeout - E3", "macro": "EEP_REFRESH_TIMEOUT_2", "description": "Time (in seconds) this ingredients can remain in the lines without any dispensing operations. Set to 0 for no timeout.", "address": 340, "boardName": "Pump Board 2", "boardId": 2, "paramType": "WORD", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "10800", "machineId": 117, "isPrivate": true}, {"id": 80280, "name": "Refresh Line Timeout - E4", "macro": "EEP_REFRESH_TIMEOUT_2", "description": "Time (in seconds) this ingredients can remain in the lines without any dispensing operations. Set to 0 for no timeout.", "address": 340, "boardName": "Pump Board 3", "boardId": 3, "paramType": "WORD", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "10800", "machineId": 117, "isPrivate": true}, {"id": 80281, "name": "Refresh Line Timeout - E5", "macro": "EEP_REFRESH_TIMEOUT_2", "description": "Time (in seconds) this ingredients can remain in the lines without any dispensing operations. Set to 0 for no timeout.", "address": 340, "boardName": "Pump Board 4", "boardId": 4, "paramType": "WORD", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "10800", "machineId": 117, "isPrivate": true}, {"id": 80282, "name": "Refresh Line Timeout - E6", "macro": "EEP_REFRESH_TIMEOUT_2", "description": "Time (in seconds) this ingredients can remain in the lines without any dispensing operations. Set to 0 for no timeout.", "address": 340, "boardName": "Pump Board 5", "boardId": 5, "paramType": "WORD", "defaultParam": "0", "currentValue": "3600", "newValue": null, "minParam": "0", "maxParam": "10800", "machineId": 117, "isPrivate": true}, {"id": 80283, "name": "Refresh Line Timeout - F1", "macro": "EEP_REFRESH_TIMEOUT_3", "description": "Time (in seconds) this ingredients can remain in the lines without any dispensing operations. Set to 0 for no timeout.", "address": 342, "boardName": "Pump Board 0", "boardId": 0, "paramType": "WORD", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "10800", "machineId": 117, "isPrivate": true}, {"id": 80284, "name": "Refresh Line Timeout - F2", "macro": "EEP_REFRESH_TIMEOUT_3", "description": "Time (in seconds) this ingredients can remain in the lines without any dispensing operations. Set to 0 for no timeout.", "address": 342, "boardName": "Pump Board 1", "boardId": 1, "paramType": "WORD", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "10800", "machineId": 117, "isPrivate": true}, {"id": 80285, "name": "Refresh Line Timeout - F3", "macro": "EEP_REFRESH_TIMEOUT_3", "description": "Time (in seconds) this ingredients can remain in the lines without any dispensing operations. Set to 0 for no timeout.", "address": 342, "boardName": "Pump Board 2", "boardId": 2, "paramType": "WORD", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "10800", "machineId": 117, "isPrivate": true}, {"id": 80286, "name": "Refresh Line Timeout - F4", "macro": "EEP_REFRESH_TIMEOUT_3", "description": "Time (in seconds) this ingredients can remain in the lines without any dispensing operations. Set to 0 for no timeout.", "address": 342, "boardName": "Pump Board 3", "boardId": 3, "paramType": "WORD", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "10800", "machineId": 117, "isPrivate": true}, {"id": 80287, "name": "Refresh Line Timeout - F5", "macro": "EEP_REFRESH_TIMEOUT_3", "description": "Time (in seconds) this ingredients can remain in the lines without any dispensing operations. Set to 0 for no timeout.", "address": 342, "boardName": "Pump Board 4", "boardId": 4, "paramType": "WORD", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "10800", "machineId": 117, "isPrivate": true}, {"id": 80288, "name": "Refresh Line Timeout - F6", "macro": "EEP_REFRESH_TIMEOUT_3", "description": "Time (in seconds) this ingredients can remain in the lines without any dispensing operations. Set to 0 for no timeout.", "address": 342, "boardName": "Pump Board 5", "boardId": 5, "paramType": "WORD", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "10800", "machineId": 117, "isPrivate": true}, {"id": 80289, "name": "Refresh Line Timeout - T1", "macro": "EEP_REFRESH_TIMEOUT_0", "description": "Time (in seconds) this ingredients can remain in the lines without any dispensing operations. Set to 0 for no timeout.", "address": 336, "boardName": "Pump Board 13", "boardId": 13, "paramType": "WORD", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "10800", "machineId": 117, "isPrivate": true}, {"id": 80290, "name": "Refresh Line Timeout - T2", "macro": "EEP_REFRESH_TIMEOUT_1", "description": "Time (in seconds) this ingredients can remain in the lines without any dispensing operations. Set to 0 for no timeout.", "address": 338, "boardName": "Pump Board 13", "boardId": 13, "paramType": "WORD", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "10800", "machineId": 117, "isPrivate": true}, {"id": 80291, "name": "Refresh Line Timeout - T3", "macro": "EEP_REFRESH_TIMEOUT_2", "description": "Time (in seconds) this ingredients can remain in the lines without any dispensing operations. Set to 0 for no timeout.", "address": 340, "boardName": "Pump Board 13", "boardId": 13, "paramType": "WORD", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "10800", "machineId": 117, "isPrivate": true}, {"id": 80292, "name": "Refresh Line Timeout - T4", "macro": "EEP_REFRESH_TIMEOUT_3", "description": "Time (in seconds) this ingredients can remain in the lines without any dispensing operations. Set to 0 for no timeout.", "address": 342, "boardName": "Pump Board 13", "boardId": 13, "paramType": "WORD", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "10800", "machineId": 117, "isPrivate": true}, {"id": 80293, "name": "Refresh Line Timeout - T5", "macro": "EEP_REFRESH_TIMEOUT_0", "description": "Time (in seconds) this ingredients can remain in the lines without any dispensing operations. Set to 0 for no timeout.", "address": 336, "boardName": "Pump Board 14", "boardId": 14, "paramType": "WORD", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "10800", "machineId": 117, "isPrivate": true}, {"id": 80294, "name": "Refresh Line Timeout - T6", "macro": "EEP_REFRESH_TIMEOUT_1", "description": "Time (in seconds) this ingredients can remain in the lines without any dispensing operations. Set to 0 for no timeout.", "address": 338, "boardName": "Pump Board 14", "boardId": 14, "paramType": "WORD", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "10800", "machineId": 117, "isPrivate": true}, {"id": 80295, "name": "Refresh Line Timeout - T7", "macro": "EEP_REFRESH_TIMEOUT_2", "description": "Time (in seconds) this ingredients can remain in the lines without any dispensing operations. Set to 0 for no timeout.", "address": 340, "boardName": "Pump Board 14", "boardId": 14, "paramType": "WORD", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "10800", "machineId": 117, "isPrivate": true}, {"id": 80296, "name": "Refresh Line Timeout - T8", "macro": "EEP_REFRESH_TIMEOUT_3", "description": "Time (in seconds) this ingredients can remain in the lines without any dispensing operations. Set to 0 for no timeout.", "address": 342, "boardName": "Pump Board 14", "boardId": 14, "paramType": "WORD", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "10800", "machineId": 117, "isPrivate": true}, {"id": 80297, "name": "Enable Top Temperature Sensor", "macro": "EEP_ENABLE_SENSOR_TOP", "description": "Enable the top temperature sensor reading to toggle the compressor ON and OFF. Set to false if the top temperature sensor is not working properly.", "address": 233, "boardName": "Cooling", "boardId": 576, "paramType": "BOOLEAN", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 117, "isPrivate": false}, {"id": 80298, "name": "Enable Bottom Temperature Sensor", "macro": "EEP_ENABLE_SENSOR_BOT", "description": "Enable the bottom temperature sensor reading to toggle the compressor ON and OFF. Set to false if the bottom temperature sensor is not working properly.", "address": 232, "boardName": "Cooling", "boardId": 576, "paramType": "BOOLEAN", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 117, "isPrivate": false}, {"id": 87882, "name": "Calibration Factor - Cleaner", "macro": "EEP_CLEANER_CAL_VAL", "description": "Cleaner calibration factor (Number of pulses per fluid oz)", "address": 128, "boardName": "Nozzle", "boardId": 560, "paramType": "DWORD", "defaultParam": "52720", "currentValue": "52720", "newValue": null, "minParam": "9720", "maxParam": "194400", "machineId": 117, "isPrivate": false}, {"id": 87883, "name": "Current Cleaner Level", "macro": "EEP_CLEANER_LEVEL", "description": "Current volume of cleaner concentrate (expressed in 1/20th oz increments)", "address": 134, "boardName": "Nozzle", "boardId": 560, "paramType": "WORD", "defaultParam": "0", "currentValue": "64469", "newValue": null, "minParam": "0", "maxParam": "65535", "machineId": 117, "isPrivate": true}, {"id": 87884, "name": "Enable Cleaner Tracking", "macro": "EEP_ENABLE_CLEANER_TRACKING", "description": "Enable cleaner tracking using inline paddle sensors", "address": 137, "boardName": "Nozzle", "boardId": 560, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 117, "isPrivate": false}, {"id": 87885, "name": "Ingredient number for Water", "macro": "EEP_WATER_INGR_NUM", "description": "Ingredient number for water. Used for self cleaning procedure", "address": 136, "boardName": "Nozzle", "boardId": 560, "paramType": "BYTE", "defaultParam": "45", "currentValue": "45", "newValue": null, "minParam": "40", "maxParam": "47", "machineId": 117, "isPrivate": false}, {"id": 87886, "name": "Priming Quantity - Cleaner", "macro": "EEP_CLEANER_PRIME_QTY", "description": "Priming quantity (expressed in 1/20th oz increments) for ingredient Cleaner", "address": 132, "boardName": "Nozzle", "boardId": 560, "paramType": "WORD", "defaultParam": "40", "currentValue": "40", "newValue": null, "minParam": "20", "maxParam": "400", "machineId": 117, "isPrivate": false}, {"id": 95477, "name": "Enable Evaporator Plate Temperature Check", "macro": "EEP_ENABLE_EVAP_PLATE_TEMP_CEHCK", "description": "Enable Evaporator Plate temperature measurements to be reported back to the master PCB", "address": 273, "boardName": "Cooling", "boardId": 576, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 117, "isPrivate": false}, {"id": 95478, "name": "Ingredient Line Status - Bib 1", "macro": "EEP_BIB_LINE_STATUS_0", "description": "Bitfield for current ingredient prime status of ingredient", "address": 144, "boardName": "Nozzle", "boardId": 560, "paramType": "BYTE", "defaultParam": "0", "currentValue": "9", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 117, "isPrivate": true}, {"id": 95479, "name": "Ingredient Line Status - Bib 2", "macro": "EEP_BIB_LINE_STATUS_1", "description": "Bitfield for current ingredient prime status of ingredient", "address": 145, "boardName": "Nozzle", "boardId": 560, "paramType": "BYTE", "defaultParam": "0", "currentValue": "9", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 117, "isPrivate": true}, {"id": 95480, "name": "Ingredient Line Status - Bib 3", "macro": "EEP_BIB_LINE_STATUS_2", "description": "Bitfield for current ingredient prime status of ingredient", "address": 146, "boardName": "Nozzle", "boardId": 560, "paramType": "BYTE", "defaultParam": "0", "currentValue": "9", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 117, "isPrivate": true}, {"id": 95481, "name": "Ingredient Line Status - Bib 4", "macro": "EEP_BIB_LINE_STATUS_3", "description": "Bitfield for current ingredient prime status of ingredient", "address": 147, "boardName": "Nozzle", "boardId": 560, "paramType": "BYTE", "defaultParam": "0", "currentValue": "9", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 117, "isPrivate": true}, {"id": 95482, "name": "Ingredient Line Status - Cleaner", "macro": "EEP_BIB_LINE_STATUS_CLEANER", "description": "Bitfield for current ingredient prime status of ingredient", "address": 150, "boardName": "Nozzle", "boardId": 560, "paramType": "BYTE", "defaultParam": "0", "currentValue": "255", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 117, "isPrivate": true}, {"id": 95483, "name": "Ingredient Line Status - Soda Bib", "macro": "EEP_BIB_LINE_STATUS_SODA", "description": "Bitfield for current ingredient prime status of ingredient", "address": 148, "boardName": "Nozzle", "boardId": 560, "paramType": "BYTE", "defaultParam": "0", "currentValue": "9", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 117, "isPrivate": true}, {"id": 95484, "name": "Ingredient Line Status - Unused Bib", "macro": "EEP_BIB_LINE_STATUS_7", "description": "Bitfield for current ingredient prime status of ingredient", "address": 151, "boardName": "Nozzle", "boardId": 560, "paramType": "BYTE", "defaultParam": "0", "currentValue": "9", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 117, "isPrivate": true}, {"id": 95485, "name": "Ingredient Line Status - Water Bib", "macro": "EEP_BIB_LINE_STATUS_WATER", "description": "Bitfield for current ingredient prime status of ingredient", "address": 149, "boardName": "Nozzle", "boardId": 560, "paramType": "BYTE", "defaultParam": "0", "currentValue": "255", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 117, "isPrivate": true}, {"id": 95488, "name": "Soaking Lines", "macro": "EEP_SOAKING_LINES", "description": "Bitfield of which dispensing lines are soaking with water or cleaner", "address": 240, "boardName": "Master", "boardId": 65535, "paramType": "DWORD", "defaultParam": "0", "currentValue": "3502169823", "newValue": null, "minParam": "0", "maxParam": "1048575", "machineId": 117, "isPrivate": true}, {"id": 95528, "name": "Shutdown Prep Quantity", "macro": "EEP_SHUTDOWN_PREP_QTY", "description": "The volume of liquid to backtrack before shutdown to ensure there is no cross contamination between ingredients in the nozzle cap (in 1/20th oz increments)", "address": 394, "boardName": "Master", "boardId": 65535, "paramType": "BYTE", "defaultParam": "2", "currentValue": "2", "newValue": null, "minParam": "0", "maxParam": "60", "machineId": 117, "isPrivate": false}, {"id": 95529, "name": "Startup Prep Quantity", "macro": "EEP_STARTUP_PREP_QTY", "description": "The volume of liquid to dispense before startup to ensure all ingredients are primed and ready to dispense into the first drink (in 1/20th oz increments)", "address": 395, "boardName": "Master", "boardId": 65535, "paramType": "BYTE", "defaultParam": "7", "currentValue": "7", "newValue": null, "minParam": "0", "maxParam": "60", "machineId": 117, "isPrivate": false}, {"id": 96654, "name": "Ingredient Sensor Enable - A5, A6, B5, B6", "macro": "EEP_BUBBLE_DETECT_ENABLE`", "description": "Enable the use of ingredient detection sensors for a group of 4 house mix ingredients", "address": 160, "boardName": "Pump Board 9", "boardId": 9, "paramType": "BOOLEAN", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 117, "isPrivate": false}, {"id": 96656, "name": "Pickup LEDs Always On", "macro": "EEP_OUTPUT_LEDS_ON", "description": "Force the output/pickup LEDs to remain on until a drink/order is picked up", "address": 64, "boardName": "Master", "boardId": 65535, "paramType": "BOOLEAN", "defaultParam": " 0", "currentValue": " 0", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 117, "isPrivate": false}, {"id": 96657, "name": "Priming Quantity - Bib 7", "macro": "EEP_BIB_PRIME_QTY_2", "description": "Priming quantity (expressed in 1/20th oz increments) for ingredient bib 7", "address": 116, "boardName": "Solenoid", "boardId": 12, "paramType": "WORD", "defaultParam": "40", "currentValue": "40", "newValue": null, "minParam": "40", "maxParam": "400", "machineId": 117, "isPrivate": true}, {"id": 96658, "name": "Priming Quantity - Bib 6", "macro": "EEP_BIB_PRIME_QTY_1", "description": "Priming quantity (expressed in 1/20th oz increments) for ingredient bib 6", "address": 114, "boardName": "Solenoid", "boardId": 12, "paramType": "WORD", "defaultParam": "40", "currentValue": "40", "newValue": null, "minParam": "40", "maxParam": "400", "machineId": 117, "isPrivate": true}, {"id": 96659, "name": "Calibration Factor - Bib 6", "macro": "EEP_BIB_1_CAL_VAL", "description": "Ingredient calibration factor (Number of ms per fluid oz)", "address": 32, "boardName": "Solenoid", "boardId": 12, "paramType": "WORD", "defaultParam": "2000", "currentValue": "2000", "newValue": null, "minParam": "100", "maxParam": "10000", "machineId": 117, "isPrivate": true}, {"id": 96660, "name": "Priming Quantity - Bib 5", "macro": "EEP_BIB_PRIME_QTY_0", "description": "Priming quantity (expressed in 1/20th oz increments) for ingredient bib 5", "address": 112, "boardName": "Solenoid", "boardId": 12, "paramType": "WORD", "defaultParam": "40", "currentValue": "40", "newValue": null, "minParam": "40", "maxParam": "400", "machineId": 117, "isPrivate": true}, {"id": 96661, "name": "Calibration Factor - Bib 5", "macro": "EEP_BIB_0_CAL_VAL", "description": "Ingredient calibration factor (Number of ms per fluid oz)", "address": 16, "boardName": "Solenoid", "boardId": 12, "paramType": "WORD", "defaultParam": "2000", "currentValue": "2000", "newValue": null, "minParam": "100", "maxParam": "10000", "machineId": 117, "isPrivate": true}, {"id": 96662, "name": "Ingredient Line Status - Bib 5", "macro": "EEP_BIB_LINE_STATUS_0", "description": "Bitfield for current ingredient prime status of ingredient", "address": 144, "boardName": "Solenoid", "boardId": 12, "paramType": "BYTE", "defaultParam": "0", "currentValue": "9", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 117, "isPrivate": true}, {"id": 96663, "name": "Ingredient Line Status - Bib 6", "macro": "EEP_BIB_LINE_STATUS_1", "description": "Bitfield for current ingredient prime status of ingredient", "address": 145, "boardName": "Solenoid", "boardId": 12, "paramType": "BYTE", "defaultParam": "0", "currentValue": "9", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 117, "isPrivate": true}, {"id": 96664, "name": "Ingredient Line Status - Bib 7", "macro": "EEP_BIB_LINE_STATUS_2", "description": "Bitfield for current ingredient prime status of ingredient", "address": 146, "boardName": "Solenoid", "boardId": 12, "paramType": "BYTE", "defaultParam": "0", "currentValue": "9", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 117, "isPrivate": true}, {"id": 96665, "name": "Ingredient Line Status - Bib 8", "macro": "EEP_BIB_LINE_STATUS_3", "description": "Bitfield for current ingredient prime status of ingredient", "address": 147, "boardName": "Solenoid", "boardId": 12, "paramType": "BYTE", "defaultParam": "0", "currentValue": "9", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 117, "isPrivate": true}, {"id": 96666, "name": "Ingredient Line Status - Unused Bib", "macro": "EEP_BIB_LINE_STATUS_8", "description": "Bitfield for current ingredient prime status of ingredient", "address": 151, "boardName": "Nozzle", "boardId": 560, "paramType": "BYTE", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 117, "isPrivate": true}, {"id": 96667, "name": "Priming Quantity - Bib 8", "macro": "EEP_BIB_PRIME_QTY_3", "description": "Priming quantity (expressed in 1/20th oz increments) for ingredient bib 8", "address": 118, "boardName": "Solenoid", "boardId": 12, "paramType": "WORD", "defaultParam": "40", "currentValue": "40", "newValue": null, "minParam": "40", "maxParam": "400", "machineId": 117, "isPrivate": true}, {"id": 96668, "name": "Calibration Factor - Bib 8", "macro": "EEP_BIB_3_CAL_VAL", "description": "Ingredient calibration factor (Number of ms per fluid oz)", "address": 64, "boardName": "Solenoid", "boardId": 12, "paramType": "WORD", "defaultParam": "2000", "currentValue": "2000", "newValue": null, "minParam": "100", "maxParam": "10000", "machineId": 117, "isPrivate": true}, {"id": 96669, "name": "Calibration Factor - Bib 7", "macro": "EEP_BIB_2_CAL_VAL", "description": "Ingredient calibration factor (Number of ms per fluid oz)", "address": 48, "boardName": "Solenoid", "boardId": 12, "paramType": "WORD", "defaultParam": "2000", "currentValue": "2000", "newValue": null, "minParam": "100", "maxParam": "10000", "machineId": 117, "isPrivate": true}, {"id": 96670, "name": "<PERSON><PERSON><PERSON> (2) Current Maximum", "macro": "EEP_PERIPH_CHAS_FAN2_MAX_CURRENT", "description": "ADC maximum value of normal operation for the external chassis fan (2) (Current = ADC * 5/4096)", "address": 302, "boardName": "Cooling", "boardId": 576, "paramType": "WORD", "defaultParam": "819", "currentValue": "819", "newValue": null, "minParam": "0", "maxParam": "1500", "machineId": 117, "isPrivate": false}, {"id": 96671, "name": "External Fan 4 Current Maximum", "macro": "EEP_PERIPH_EXT_FAN4_MAX_CURRENT", "description": "ADC maximum value of normal operation for the external fan (4) (Current = ADC * 5/4096)", "address": 300, "boardName": "Cooling", "boardId": 576, "paramType": "WORD", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "1500", "machineId": 117, "isPrivate": false}, {"id": 96672, "name": "<PERSON><PERSON><PERSON> (1) Current Maximum", "macro": "EEP_PERIPH_CHAS_FAN1_MAX_CURRENT", "description": "ADC maximum value of normal operation for the external chassis fan (1) (Current = ADC * 5/4096)", "address": 298, "boardName": "Cooling", "boardId": 576, "paramType": "WORD", "defaultParam": "819", "currentValue": "819", "newValue": null, "minParam": "0", "maxParam": "1500", "machineId": 117, "isPrivate": false}, {"id": 96673, "name": "Compressor Fan Current Maximum", "macro": "EEP_PERIPH_COMP_FAN_MAX_CURRENT", "description": "ADC maximum value of normal operation for the fridge’s external compressor fan (Current = ADC * 5/4096)", "address": 296, "boardName": "Cooling", "boardId": 576, "paramType": "WORD", "defaultParam": "819", "currentValue": "819", "newValue": null, "minParam": "0", "maxParam": "1500", "machineId": 117, "isPrivate": false}, {"id": 96674, "name": "Internal Fan (Right) Current Maximum", "macro": "EEP_PERIPH_INT_RGHT_MAX_CURRENT", "description": "ADC maximum value of normal operation for the fridge’s right internal fan (Current = ADC * 5/4096)", "address": 294, "boardName": "Cooling", "boardId": 576, "paramType": "WORD", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "1500", "machineId": 117, "isPrivate": false}, {"id": 96675, "name": "Internal Fan (Left) Current Maximum", "macro": "EEP_PERIPH_INT_LFT_MAX_CURRENT", "description": "ADC maximum value of normal operation for the fridge’s left internal fan (Current = ADC * 5/4096)", "address": 292, "boardName": "Cooling", "boardId": 576, "paramType": "WORD", "defaultParam": "1229", "currentValue": "1229", "newValue": null, "minParam": "0", "maxParam": "1500", "machineId": 117, "isPrivate": false}, {"id": 96676, "name": "Internal Fan (Top) Current Maximum", "macro": "EEP_PERIPH_INT_TOP_MAX_CURRENT", "description": "ADC maximum value of normal operation for the fridge’s top internal fan (Current = ADC * 5/4096)", "address": 290, "boardName": "Cooling", "boardId": 576, "paramType": "WORD", "defaultParam": "1229", "currentValue": "1229", "newValue": null, "minParam": "0", "maxParam": "1500", "machineId": 117, "isPrivate": false}, {"id": 96677, "name": "Compressor Current Maximum", "macro": "EEP_PERIPH_COMPRESSOR_MAX_CURRENT", "description": "ADC maximum value of normal operation for the fridge compressor (Current = ADC * 102.4)", "address": 288, "boardName": "Cooling", "boardId": 576, "paramType": "WORD", "defaultParam": "204", "currentValue": "204", "newValue": null, "minParam": "0", "maxParam": "1500", "machineId": 117, "isPrivate": false}, {"id": 96678, "name": "<PERSON><PERSON><PERSON> (2) Current Minimum", "macro": "EEP_PERIPH_CHAS_FAN2_MIN_CURRENT", "description": "ADC minimum value of normal operation for the external chassis fan (2) (Current = ADC * 5/4096)", "address": 270, "boardName": "Cooling", "boardId": 576, "paramType": "WORD", "defaultParam": "163", "currentValue": "163", "newValue": null, "minParam": "0", "maxParam": "1500", "machineId": 117, "isPrivate": false}, {"id": 96679, "name": "External Fan 4 Current Minimum", "macro": "EEP_PERIPH_EXT_FAN4_MIN_CURRENT", "description": "ADC minimum value of normal operation for the external fan (4) (Current = ADC * 5/4096)", "address": 268, "boardName": "Cooling", "boardId": 576, "paramType": "WORD", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "1500", "machineId": 117, "isPrivate": false}, {"id": 96680, "name": "<PERSON><PERSON><PERSON> (1) Current Minimum", "macro": "EEP_PERIPH_CHAS_FAN1_MIN_CURRENT", "description": "ADC minimum value of normal operation for the external chassis fan (1) (Current = ADC * 5/4096)", "address": 266, "boardName": "Cooling", "boardId": 576, "paramType": "WORD", "defaultParam": "163", "currentValue": "163", "newValue": null, "minParam": "0", "maxParam": "1500", "machineId": 117, "isPrivate": false}, {"id": 96681, "name": "Compressor Fan Current Minimum", "macro": "EEP_PERIPH_COMP_FAN_MIN_CURRENT", "description": "ADC minimum value of normal operation for the fridge’s external compressor fan (Current = ADC * 5/4096)", "address": 264, "boardName": "Cooling", "boardId": 576, "paramType": "WORD", "defaultParam": "327", "currentValue": "327", "newValue": null, "minParam": "0", "maxParam": "1500", "machineId": 117, "isPrivate": false}, {"id": 96682, "name": "Internal Fan (Right) Current Minimum", "macro": "EEP_PERIPH_INT_RGHT_MIN_CURRENT", "description": "ADC minimum value of normal operation for the fridge’s right internal fan (Current = ADC * 5/4096)", "address": 262, "boardName": "Cooling", "boardId": 576, "paramType": "WORD", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "1500", "machineId": 117, "isPrivate": false}, {"id": 96683, "name": "Internal Fan (Left) Current Minimum", "macro": "EEP_PERIPH_INT_LFT_MIN_CURRENT", "description": "ADC minimum value of normal operation for the fridge’s left internal fan (Current = ADC * 5/4096)", "address": 260, "boardName": "Cooling", "boardId": 576, "paramType": "WORD", "defaultParam": "491", "currentValue": "491", "newValue": null, "minParam": "0", "maxParam": "1500", "machineId": 117, "isPrivate": false}, {"id": 96684, "name": "Internal Fan (Top) Current Minimum", "macro": "EEP_PERIPH_INT_TOP_MIN_CURRENT", "description": "ADC minimum value of normal operation for the fridge’s top internal fan (Current = ADC * 5/4096)", "address": 258, "boardName": "Cooling", "boardId": 576, "paramType": "WORD", "defaultParam": "491", "currentValue": "491", "newValue": null, "minParam": "0", "maxParam": "1500", "machineId": 117, "isPrivate": false}, {"id": 96685, "name": "Compressor Current Minimum", "macro": "EEP_PERIPH_COMPRESSOR_MIN_CURRENT", "description": "ADC minimum value of normal operation for the fridge compressor (Current = ADC * 102.4)", "address": 256, "boardName": "Cooling", "boardId": 576, "paramType": "WORD", "defaultParam": "25", "currentValue": "25", "newValue": null, "minParam": "0", "maxParam": "1500", "machineId": 117, "isPrivate": false}, {"id": 96686, "name": "Compressor Minimum Off Time", "macro": "EEP_COMPRESSOR_MIN_OFFTIME", "description": "Timeout used for allowing evaporator plate to thaw before turning the compressor back on. Ignored in machines that have an evaporator plate sensor", "address": 214, "boardName": "Cooling", "boardId": 576, "paramType": "WORD", "defaultParam": "900", "currentValue": "900", "newValue": null, "minParam": "60", "maxParam": "7200", "machineId": 117, "isPrivate": false}, {"id": 96687, "name": "Shutdown Prep Volume", "macro": "EEP_SHUTDOWN_PREP_VOL", "description": "The volume of liquid to backtrack before shutdown to ensure there is no cross contamination between ingredients in the nozzle cap (in 1/20th oz increments)", "address": 394, "boardName": "Master", "boardId": 65535, "paramType": "BYTE", "defaultParam": "2", "currentValue": "2", "newValue": null, "minParam": "0", "maxParam": "60", "machineId": 117, "isPrivate": false}, {"id": 96688, "name": "Startup Prep Volume", "macro": "EEP_STARTUP_PREP_VOL", "description": "The volume of liquid to dispense before startup to ensure all ingredients are primed and ready to dispense into the first drink (in 1/20th oz increments)", "address": 395, "boardName": "Master", "boardId": 65535, "paramType": "BYTE", "defaultParam": "7", "currentValue": "7", "newValue": null, "minParam": "0", "maxParam": "60", "machineId": 117, "isPrivate": false}, {"id": 99015, "name": "Retraction Quantity C1, D1, E1, F1", "macro": "EEP_RETRACTION_QTY", "description": "Amount to retract (expressed in 1/20 fl. oz. increments) after a forward dispense ", "address": 278, "boardName": "Pump Board 0", "boardId": 0, "paramType": "BYTE", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "20", "machineId": 117, "isPrivate": false}, {"id": 99016, "name": "Retraction Quantity C2, E2, D2, F2", "macro": "EEP_RETRACTION_QTY", "description": "Amount to retract (expressed in 1/20 fl. oz. increments) after a forward dispense ", "address": 278, "boardName": "Pump Board 1", "boardId": 1, "paramType": "BYTE", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "20", "machineId": 117, "isPrivate": false}, {"id": 99017, "name": "Retraction Quantity C3, D3, E3, F3", "macro": "EEP_RETRACTION_QTY", "description": "Amount to retract (expressed in 1/20 fl. oz. increments) after a forward dispense ", "address": 278, "boardName": "Pump Board 2", "boardId": 2, "paramType": "BYTE", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "20", "machineId": 117, "isPrivate": false}, {"id": 99018, "name": "Retraction Quantity C4, D4, E4, F4", "macro": "EEP_RETRACTION_QTY", "description": "Amount to retract (expressed in 1/20 fl. oz. increments) after a forward dispense ", "address": 278, "boardName": "Pump Board 3", "boardId": 3, "paramType": "BYTE", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "20", "machineId": 117, "isPrivate": false}, {"id": 99019, "name": "Retraction Quantity C5, D5, E5, F5", "macro": "EEP_RETRACTION_QTY", "description": "Amount to retract (expressed in 1/20 fl. oz. increments) after a forward dispense ", "address": 278, "boardName": "Pump Board 4", "boardId": 4, "paramType": "BYTE", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "20", "machineId": 117, "isPrivate": false}, {"id": 99020, "name": "Retraction Quantity C6, D6, E6, F6", "macro": "EEP_RETRACTION_QTY", "description": "Amount to retract (expressed in 1/20 fl. oz. increments) after a forward dispense ", "address": 278, "boardName": "Pump Board 5", "boardId": 5, "paramType": "BYTE", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "20", "machineId": 117, "isPrivate": false}, {"id": 99021, "name": "Retraction Quantity A1, A2, B1, B2", "macro": "EEP_RETRACTION_QTY", "description": "Amount to retract (expressed in 1/20 fl. oz. increments) after a forward dispense ", "address": 278, "boardName": "Pump Board 7", "boardId": 7, "paramType": "BYTE", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "20", "machineId": 117, "isPrivate": false}, {"id": 99022, "name": "Retraction Quantity A3, A4, B3, B4", "macro": "EEP_RETRACTION_QTY", "description": "Amount to retract (expressed in 1/20 fl. oz. increments) after a forward dispense ", "address": 278, "boardName": "Pump Board 8", "boardId": 8, "paramType": "BYTE", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "20", "machineId": 117, "isPrivate": false}, {"id": 99023, "name": "Retraction Quantity A5, A6, B5, B6", "macro": "EEP_RETRACTION_QTY", "description": "Amount to retract (expressed in 1/20 fl. oz. increments) after a forward dispense ", "address": 278, "boardName": "Pump Board 9", "boardId": 9, "paramType": "BYTE", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "20", "machineId": 117, "isPrivate": false}, {"id": 99024, "name": "Startup Prime Setting - C1, D1, E1, F1", "macro": "EEP_STARTUP_PRIME_MODE", "description": "Setting to indicate what ingredients to prime at startup.\n0: Prime Shelf Stable ingredients ONLY\n1: Prime ALL ingredients regardless of refresh timer\n2: Leave ALL ingredients UNPRIMED", "address": 306, "boardName": "Pump Board 0", "boardId": 0, "paramType": "BYTE", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "2", "machineId": 117, "isPrivate": false}, {"id": 99025, "name": "Startup Prime Setting - C2, E2, D2, F2", "macro": "EEP_STARTUP_PRIME_MODE", "description": "Setting to indicate what ingredients to prime at startup.\n0: Prime Shelf Stable ingredients ONLY\n1: Prime ALL ingredients regardless of refresh timer\n2: Leave ALL ingredients UNPRIMED", "address": 306, "boardName": "Pump Board 1", "boardId": 1, "paramType": "BYTE", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "2", "machineId": 117, "isPrivate": false}, {"id": 99026, "name": "Startup Prime Setting - C3, D3, E3, F3", "macro": "EEP_STARTUP_PRIME_MODE", "description": "Setting to indicate what ingredients to prime at startup.\n0: Prime Shelf Stable ingredients ONLY\n1: Prime ALL ingredients regardless of refresh timer\n2: Leave ALL ingredients UNPRIMED", "address": 306, "boardName": "Pump Board 2", "boardId": 2, "paramType": "BYTE", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "2", "machineId": 117, "isPrivate": false}, {"id": 99027, "name": "Startup Prime Setting - C4, D4, E4, F4", "macro": "EEP_STARTUP_PRIME_MODE", "description": "Setting to indicate what ingredients to prime at startup.\n0: Prime Shelf Stable ingredients ONLY\n1: Prime ALL ingredients regardless of refresh timer\n2: Leave ALL ingredients UNPRIMED", "address": 306, "boardName": "Pump Board 3", "boardId": 3, "paramType": "BYTE", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "2", "machineId": 117, "isPrivate": false}, {"id": 99028, "name": "Startup Prime Setting - C5, D5, E5, F5", "macro": "EEP_STARTUP_PRIME_MODE", "description": "Setting to indicate what ingredients to prime at startup.\n0: Prime Shelf Stable ingredients ONLY\n1: Prime ALL ingredients regardless of refresh timer\n2: Leave ALL ingredients UNPRIMED", "address": 306, "boardName": "Pump Board 4", "boardId": 4, "paramType": "BYTE", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "2", "machineId": 117, "isPrivate": false}, {"id": 99029, "name": "Startup Prime Setting - C6, D6, E6, F6", "macro": "EEP_STARTUP_PRIME_MODE", "description": "Setting to indicate what ingredients to prime at startup.\n0: Prime Shelf Stable ingredients ONLY\n1: Prime ALL ingredients regardless of refresh timer\n2: Leave ALL ingredients UNPRIMED", "address": 306, "boardName": "Pump Board 5", "boardId": 5, "paramType": "BYTE", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "2", "machineId": 117, "isPrivate": false}, {"id": 99030, "name": "Startup Prime Setting - A1, A2, B1, B2", "macro": "EEP_STARTUP_PRIME_MODE", "description": "Setting to indicate what ingredients to prime at startup.\n0: Prime Shelf Stable ingredients ONLY\n1: Prime ALL ingredients regardless of refresh timer\n2: Leave ALL ingredients UNPRIMED", "address": 306, "boardName": "Pump Board 7", "boardId": 7, "paramType": "BYTE", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "2", "machineId": 117, "isPrivate": false}, {"id": 99031, "name": "Startup Prime Setting - A3, A4, B3, B4", "macro": "EEP_STARTUP_PRIME_MODE", "description": "Setting to indicate what ingredients to prime at startup.\n0: Prime Shelf Stable ingredients ONLY\n1: Prime ALL ingredients regardless of refresh timer\n2: Leave ALL ingredients UNPRIMED", "address": 306, "boardName": "Pump Board 8", "boardId": 8, "paramType": "BYTE", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "2", "machineId": 117, "isPrivate": false}, {"id": 99032, "name": "Startup Prime Setting - A5, A6, B5, B6", "macro": "EEP_STARTUP_PRIME_MODE", "description": "Setting to indicate what ingredients to prime at startup.\n0: Prime Shelf Stable ingredients ONLY\n1: Prime ALL ingredients regardless of refresh timer\n2: Leave ALL ingredients UNPRIMED", "address": 306, "boardName": "Pump Board 9", "boardId": 9, "paramType": "BYTE", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "2", "machineId": 117, "isPrivate": false}]