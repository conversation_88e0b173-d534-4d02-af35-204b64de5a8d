[{"id": 83674, "name": "Ingredient Mixing Period", "macro": "EEP_MIXING_PERIOD", "description": "Time (in seconds) between house mix mixing/agitation operations", "address": 32, "boardName": "Master", "boardId": 65535, "paramType": "WORD", "defaultParam": "600", "currentValue": "600", "newValue": null, "minParam": "60", "maxParam": "3600", "machineId": 133, "isPrivate": false}, {"id": 83712, "name": "<PERSON><PERSON>ump ON Time", "macro": "EEP_DRAIN_ON_TIME", "description": "Time the drain pump is turned ON (in seconds)", "address": 80, "boardName": "Cooling", "boardId": 576, "paramType": "WORD", "defaultParam": "60", "currentValue": "60", "newValue": null, "minParam": "10", "maxParam": "1800", "machineId": 133, "isPrivate": false}, {"id": 83713, "name": "<PERSON><PERSON>ump OFF Time", "macro": "EEP_DRAIN_OFF_TIME", "description": "Time the drain pump is turned OFF (in seconds)", "address": 96, "boardName": "Cooling", "boardId": 576, "paramType": "WORD", "defaultParam": "540", "currentValue": "540", "newValue": null, "minParam": "10", "maxParam": "10800", "machineId": 133, "isPrivate": false}, {"id": 83723, "name": "Refrigeration Enable", "macro": "EEP_ENABLE_REFRIGERATION", "description": "Enable the compressor for cooling the fridge. See ", "address": 50, "boardName": "Cooling", "boardId": 576, "paramType": "BOOLEAN", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 133, "isPrivate": false}, {"id": 83724, "name": "Drain Auto Mode Enable", "macro": "EEP_DRAIN_AUTO_ENABLE", "description": "Enable automatic drain pump cycling based on preset ON time and OFF time periods", "address": 112, "boardName": "Cooling", "boardId": 576, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 133, "isPrivate": false}, {"id": 83731, "name": "Testing Mode Enable", "macro": "EEP_SILENT_MODE_ENABLE", "description": "Testing and production ONLY. Stop all asynchronous machine actions, CAN frames, and serial messages for testing purposes", "address": 80, "boardName": "Master", "boardId": 65535, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 133, "isPrivate": false}, {"id": 83795, "name": "Bubble Detection Threshold - B1", "macro": "EEP_BUBBLE_ROC_THRESHOLD_1", "description": "Rate of change value required to signify a runout. (Default Oat milk = 30, almond/dairy = 50, all other 75+)", "address": 369, "boardName": "Pump Board 7", "boardId": 7, "paramType": "BYTE", "defaultParam": "50", "currentValue": "50", "newValue": "50", "minParam": "15", "maxParam": "150", "machineId": 133, "isPrivate": false}, {"id": 83796, "name": "Bubble Detection Threshold - B2", "macro": "EEP_BUBBLE_ROC_THRESHOLD_3", "description": "Rate of change value required to signify a runout. (Default Oat milk = 30, almond/dairy = 50, all other 75+)", "address": 371, "boardName": "Pump Board 7", "boardId": 7, "paramType": "BYTE", "defaultParam": "50", "currentValue": "50", "newValue": "50", "minParam": "15", "maxParam": "150", "machineId": 133, "isPrivate": false}, {"id": 83797, "name": "Bubble Detection Threshold - B3", "macro": "EEP_BUBBLE_ROC_THRESHOLD_1", "description": "Rate of change value required to signify a runout. (Default Oat milk = 30, almond/dairy = 50, all other 75+)", "address": 369, "boardName": "Pump Board 8", "boardId": 8, "paramType": "BYTE", "defaultParam": "50", "currentValue": "50", "newValue": "50", "minParam": "15", "maxParam": "150", "machineId": 133, "isPrivate": false}, {"id": 83798, "name": "Bubble Detection Threshold - B4", "macro": "EEP_BUBBLE_ROC_THRESHOLD_3", "description": "Rate of change value required to signify a runout. (Default Oat milk = 30, almond/dairy = 50, all other 75+)", "address": 371, "boardName": "Pump Board 8", "boardId": 8, "paramType": "BYTE", "defaultParam": "50", "currentValue": "50", "newValue": "50", "minParam": "15", "maxParam": "150", "machineId": 133, "isPrivate": false}, {"id": 83799, "name": "Bubble Detection Threshold - C1", "macro": "EEP_BUBBLE_ROC_THRESHOLD_0", "description": "Rate of change value required to signify a runout. (Default Oat milk = 30, almond/dairy = 50, all other 75+)", "address": 368, "boardName": "Pump Board 0", "boardId": 0, "paramType": "BYTE", "defaultParam": "50", "currentValue": "50", "newValue": "50", "minParam": "15", "maxParam": "150", "machineId": 133, "isPrivate": false}, {"id": 83800, "name": "Bubble Detection Threshold - C2", "macro": "EEP_BUBBLE_ROC_THRESHOLD_0", "description": "Rate of change value required to signify a runout. (Default Oat milk = 30, almond/dairy = 50, all other 75+)", "address": 368, "boardName": "Pump Board 1", "boardId": 1, "paramType": "BYTE", "defaultParam": "50", "currentValue": "50", "newValue": "50", "minParam": "15", "maxParam": "150", "machineId": 133, "isPrivate": false}, {"id": 83801, "name": "Bubble Detection Threshold - C3", "macro": "EEP_BUBBLE_ROC_THRESHOLD_0", "description": "Rate of change value required to signify a runout. (Default Oat milk = 30, almond/dairy = 50, all other 75+)", "address": 368, "boardName": "Pump Board 2", "boardId": 2, "paramType": "BYTE", "defaultParam": "50", "currentValue": "50", "newValue": "50", "minParam": "15", "maxParam": "150", "machineId": 133, "isPrivate": false}, {"id": 83802, "name": "Bubble Detection Threshold - C4", "macro": "EEP_BUBBLE_ROC_THRESHOLD_0", "description": "Rate of change value required to signify a runout. (Default Oat milk = 30, almond/dairy = 50, all other 75+)", "address": 368, "boardName": "Pump Board 3", "boardId": 3, "paramType": "BYTE", "defaultParam": "50", "currentValue": "50", "newValue": "50", "minParam": "15", "maxParam": "150", "machineId": 133, "isPrivate": false}, {"id": 83803, "name": "Bubble Detection Threshold - D1", "macro": "EEP_BUBBLE_ROC_THRESHOLD_1", "description": "Rate of change value required to signify a runout. (Default Oat milk = 30, almond/dairy = 50, all other 75+)", "address": 369, "boardName": "Pump Board 0", "boardId": 0, "paramType": "BYTE", "defaultParam": "50", "currentValue": "50", "newValue": "50", "minParam": "15", "maxParam": "150", "machineId": 133, "isPrivate": false}, {"id": 83804, "name": "Bubble Detection Threshold - D2", "macro": "EEP_BUBBLE_ROC_THRESHOLD_1", "description": "Rate of change value required to signify a runout. (Default Oat milk = 30, almond/dairy = 50, all other 75+)", "address": 369, "boardName": "Pump Board 1", "boardId": 1, "paramType": "BYTE", "defaultParam": "50", "currentValue": "50", "newValue": "50", "minParam": "15", "maxParam": "150", "machineId": 133, "isPrivate": false}, {"id": 83805, "name": "Bubble Detection Threshold - D3", "macro": "EEP_BUBBLE_ROC_THRESHOLD_1", "description": "Rate of change value required to signify a runout. (Default Oat milk = 30, almond/dairy = 50, all other 75+)", "address": 369, "boardName": "Pump Board 2", "boardId": 2, "paramType": "BYTE", "defaultParam": "50", "currentValue": "50", "newValue": "50", "minParam": "15", "maxParam": "150", "machineId": 133, "isPrivate": false}, {"id": 83806, "name": "Bubble Detection Threshold - D4", "macro": "EEP_BUBBLE_ROC_THRESHOLD_1", "description": "Rate of change value required to signify a runout. (Default Oat milk = 30, almond/dairy = 50, all other 75+)", "address": 369, "boardName": "Pump Board 3", "boardId": 3, "paramType": "BYTE", "defaultParam": "50", "currentValue": "50", "newValue": "50", "minParam": "15", "maxParam": "150", "machineId": 133, "isPrivate": false}, {"id": 83807, "name": "Bubble Detection Threshold - E1", "macro": "EEP_BUBBLE_ROC_THRESHOLD_2", "description": "Rate of change value required to signify a runout. (Default Oat milk = 30, almond/dairy = 50, all other 75+)", "address": 370, "boardName": "Pump Board 0", "boardId": 0, "paramType": "BYTE", "defaultParam": "50", "currentValue": "50", "newValue": "0", "minParam": "15", "maxParam": "150", "machineId": 133, "isPrivate": false}, {"id": 83808, "name": "Bubble Detection Threshold - E2", "macro": "EEP_BUBBLE_ROC_THRESHOLD_2", "description": "Rate of change value required to signify a runout. (Default Oat milk = 30, almond/dairy = 50, all other 75+)", "address": 370, "boardName": "Pump Board 1", "boardId": 1, "paramType": "BYTE", "defaultParam": "50", "currentValue": "50", "newValue": "50", "minParam": "15", "maxParam": "150", "machineId": 133, "isPrivate": false}, {"id": 83809, "name": "Bubble Detection Threshold - E3", "macro": "EEP_BUBBLE_ROC_THRESHOLD_2", "description": "Rate of change value required to signify a runout. (Default Oat milk = 30, almond/dairy = 50, all other 75+)", "address": 370, "boardName": "Pump Board 2", "boardId": 2, "paramType": "BYTE", "defaultParam": "50", "currentValue": "50", "newValue": "50", "minParam": "15", "maxParam": "150", "machineId": 133, "isPrivate": false}, {"id": 83810, "name": "Bubble Detection Threshold - E4", "macro": "EEP_BUBBLE_ROC_THRESHOLD_2", "description": "Rate of change value required to signify a runout. (Default Oat milk = 30, almond/dairy = 50, all other 75+)", "address": 370, "boardName": "Pump Board 3", "boardId": 3, "paramType": "BYTE", "defaultParam": "50", "currentValue": "50", "newValue": "50", "minParam": "15", "maxParam": "150", "machineId": 133, "isPrivate": false}, {"id": 83811, "name": "Bubble Detection Threshold - F1", "macro": "EEP_BUBBLE_ROC_THRESHOLD_3", "description": "Rate of change value required to signify a runout. (Default Oat milk = 30, almond/dairy = 50, all other 75+)", "address": 371, "boardName": "Pump Board 0", "boardId": 0, "paramType": "BYTE", "defaultParam": "50", "currentValue": "50", "newValue": "50", "minParam": "15", "maxParam": "150", "machineId": 133, "isPrivate": false}, {"id": 83812, "name": "Bubble Detection Threshold - F2", "macro": "EEP_BUBBLE_ROC_THRESHOLD_3", "description": "Rate of change value required to signify a runout. (Default Oat milk = 30, almond/dairy = 50, all other 75+)", "address": 371, "boardName": "Pump Board 1", "boardId": 1, "paramType": "BYTE", "defaultParam": "50", "currentValue": "50", "newValue": "50", "minParam": "15", "maxParam": "150", "machineId": 133, "isPrivate": false}, {"id": 83813, "name": "Bubble Detection Threshold - F3", "macro": "EEP_BUBBLE_ROC_THRESHOLD_3", "description": "Rate of change value required to signify a runout. (Default Oat milk = 30, almond/dairy = 50, all other 75+)", "address": 371, "boardName": "Pump Board 2", "boardId": 2, "paramType": "BYTE", "defaultParam": "50", "currentValue": "50", "newValue": "50", "minParam": "15", "maxParam": "150", "machineId": 133, "isPrivate": false}, {"id": 83814, "name": "Bubble Detection Threshold - F4", "macro": "EEP_BUBBLE_ROC_THRESHOLD_3", "description": "Rate of change value required to signify a runout. (Default Oat milk = 30, almond/dairy = 50, all other 75+)", "address": 371, "boardName": "Pump Board 3", "boardId": 3, "paramType": "BYTE", "defaultParam": "50", "currentValue": "50", "newValue": "50", "minParam": "15", "maxParam": "150", "machineId": 133, "isPrivate": false}, {"id": 83831, "name": "Refrigeration Scheduled ON Time", "macro": "EEP_COMPRESSOR_STATIC_ON", "description": "Time the compressor is turned ON for static, scheduled compressor operation (in seconds)", "address": 128, "boardName": "Cooling", "boardId": 576, "paramType": "WORD", "defaultParam": "1800", "currentValue": "1800", "newValue": null, "minParam": "300", "maxParam": "7200", "machineId": 133, "isPrivate": false}, {"id": 83832, "name": "Refrigeration Scheduled OFF Time", "macro": "EEP_COMPRESSOR_STATIC_OFF", "description": "Time the compressor is turned OFF for static, scheduled compressor operation (in seconds)", "address": 144, "boardName": "Cooling", "boardId": 576, "paramType": "WORD", "defaultParam": "1800", "currentValue": "2700", "newValue": null, "minParam": "300", "maxParam": "7200", "machineId": 133, "isPrivate": false}, {"id": 83833, "name": "Cooling Drawer Switch <PERSON><PERSON>", "macro": "EEP_COOLER_DRAWER_OPEN_ALERT_ENABLE", "description": "Enable alert for cooling drawer switch open", "address": 160, "boardName": "Cooling", "boardId": 576, "paramType": "BOOLEAN", "defaultParam": "1", "currentValue": "TRUE", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 133, "isPrivate": false}, {"id": 83834, "name": "Cooling Drawer <PERSON><PERSON> <PERSON><PERSON>", "macro": "EEP_COOLER_DRAWER_OPEN_ALERT_TIME", "description": "Time the cooling drawer switch can remain open until an alert is sent from the cooling board (in seconds)", "address": 176, "boardName": "Cooling", "boardId": 576, "paramType": "WORD", "defaultParam": "600", "currentValue": "180", "newValue": null, "minParam": "300", "maxParam": "1200", "machineId": 133, "isPrivate": false}, {"id": 83836, "name": "Compressor Normal Runtime Max", "macro": "EEP_COMPRESSOR_NORMAL_RT_MAX", "description": "Continuous time that the compressor can be enabled before sending a runtime alert", "address": 208, "boardName": "Cooling", "boardId": 576, "paramType": "WORD", "defaultParam": "43200", "currentValue": "3600", "newValue": null, "minParam": "900", "maxParam": "10800", "machineId": 133, "isPrivate": false}, {"id": 83837, "name": "Refresh Lines Check Period", "macro": "EEP_REFRESH_CHECK_PERIOD", "description": "Time (in seconds) between refresh line operations, if there are any to perform", "address": 320, "boardName": "Master", "boardId": 65535, "paramType": "WORD", "defaultParam": "3600", "currentValue": "600", "newValue": null, "minParam": "300", "maxParam": "5400", "machineId": 133, "isPrivate": false}, {"id": 83838, "name": "Cleaning Concentrate Ratio", "macro": "EEP_CLEANER_RATIO", "description": "The ratio of water to cleaner for internal self cleaning. A value of “0” means that pre-diluted cleaner is being used in place of cleaner concentrate, and there will be no water used for dilution ", "address": 393, "boardName": "Master", "boardId": 65535, "paramType": "BYTE", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "30", "machineId": 133, "isPrivate": false}, {"id": 83839, "name": "Cleaning Concentrate Ingredient Number", "macro": "EEP_CLEANER_INGR_NUM", "description": "The ingredient number of the cleaner. Currently, this MUST be pump 3 on a pump board. (I.E A6 on pump board board 9, E2 on pump board 2)", "address": 392, "boardName": "Master", "boardId": 65535, "paramType": "BYTE", "defaultParam": "46", "currentValue": "46", "newValue": null, "minParam": "0", "maxParam": "47", "machineId": 133, "isPrivate": false}, {"id": 83840, "name": "Global Water Rinse Speed", "macro": "EEP_GLOBAL_WATER_RINSE_SPEED", "description": "The cleaning rate (in oz/min) that water is rinsed during a self clean", "address": 384, "boardName": "Master", "boardId": 65535, "paramType": "SINGLE", "defaultParam": "35.0", "currentValue": "15.0", "newValue": null, "minParam": "5", "maxParam": "37.5", "machineId": 133, "isPrivate": false}, {"id": 83841, "name": "Global Cleaner Rinse Speed", "macro": "EEP_GLOBAL_CLEANER_RINSE_SPEED", "description": "The cleaning rate (in oz/min) that cleaner is rinsed during a self clean", "address": 388, "boardName": "Master", "boardId": 65535, "paramType": "SINGLE", "defaultParam": "15.0", "currentValue": "15.0", "newValue": null, "minParam": "5", "maxParam": "37.5", "machineId": 133, "isPrivate": false}, {"id": 83842, "name": "Enable Cooling Board Current Measurements", "macro": "EEP_ENABLE_COOLING_CURRENT_CHECK", "description": "Enable Cooling Board Current Measurements for fans and compressor", "address": 272, "boardName": "Cooling", "boardId": 576, "paramType": "BOOLEAN", "defaultParam": "1", "currentValue": "420", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 133, "isPrivate": false}, {"id": 83843, "name": "<PERSON><PERSON> Defrost Timeout", "macro": "EEP_DEFROST_TIMEOUT", "description": "Time that the compressor is forced to stay off during a defrost cycle (in seconds)", "address": 240, "boardName": "Cooling", "boardId": 576, "paramType": "WORD", "defaultParam": "3600", "currentValue": "1", "newValue": null, "minParam": "1800", "maxParam": "43200", "machineId": 133, "isPrivate": false}, {"id": 83844, "name": "Enable Top Temperature Sensor", "macro": "EEP_ENABLE_SENSOR_TOP", "description": "Enable the top temperature sensor reading to toggle the compressor ON and OFF. Set to false if the top temperature sensor is not working properly.", "address": 233, "boardName": "Cooling", "boardId": 576, "paramType": "BOOLEAN", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 133, "isPrivate": false}, {"id": 83845, "name": "Enable Bottom Temperature Sensor", "macro": "EEP_ENABLE_SENSOR_BOT", "description": "Enable the bottom temperature sensor reading to toggle the compressor ON and OFF. Set to false if the bottom temperature sensor is not working properly.", "address": 232, "boardName": "Cooling", "boardId": 576, "paramType": "BOOLEAN", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 133, "isPrivate": false}, {"id": 83846, "name": "Refrigeration Enable", "macro": "EEP_ENABLE_REFRIGERATION", "description": "Enable the compressor for cooling the fridge. See  Enable Static Compressor  for temperature control operation", "address": 50, "boardName": "Cooling", "boardId": 576, "paramType": "BOOLEAN", "defaultParam": "1", "currentValue": "3600", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 133, "isPrivate": false}, {"id": 83847, "name": "Compressor Normal Runtime Min", "macro": "EEP_COMPRESSOR_NORMAL_RT_MIN", "description": "Continuous time (in seconds) that the compressor can be disabled before cycling ", "address": 210, "boardName": "Cooling", "boardId": 576, "paramType": "WORD", "defaultParam": "420", "currentValue": "420", "newValue": null, "minParam": "60", "maxParam": "900", "machineId": 133, "isPrivate": false}, {"id": 83848, "name": "Static Cycle Enable", "macro": "EEP_ENABLE_STATIC_COMPRESSOR", "description": "Enable static cycling of the compressor regardless of the state of temperature sensors. (Note that if ", "address": 49, "boardName": "Cooling", "boardId": 576, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 133, "isPrivate": false}, {"id": 83849, "name": "Lower Temperature Threshold (Top Temp Sensor)", "macro": "EEP_LOWER_TEMP_THRESHOLD", "description": "Temperature of the top fridge sensor (in degrees Fahrenheit) below which the compressor is turned OFF", "address": 32, "boardName": "Cooling", "boardId": 576, "paramType": "SINGLE", "defaultParam": "36", "currentValue": "36", "newValue": null, "minParam": "32", "maxParam": "46", "machineId": 133, "isPrivate": false}, {"id": 83850, "name": "Lower Temperature Threshold (Bottom Temp Sensor)", "macro": "EEP_LOWER_TEMP_THRESHOLD_BOT", "description": "Temperature of the bottom fridge sensor (in degrees Fahrenheit) below which the compressor is turned OFF", "address": 228, "boardName": "Cooling", "boardId": 576, "paramType": "SINGLE", "defaultParam": "36", "currentValue": "36", "newValue": null, "minParam": "32", "maxParam": "46", "machineId": 133, "isPrivate": false}, {"id": 83851, "name": "Upper Temperature Threshold (Bottom Temp Sensor)", "macro": "EEP_UPPER_TEMP_THRESHOLD_BOT", "description": "Temperature of the bottom fridge sensor (in degrees Fahrenheit) above which the compressor is turned ON", "address": 224, "boardName": "Cooling", "boardId": 576, "paramType": "SINGLE", "defaultParam": "44", "currentValue": "44", "newValue": null, "minParam": "32", "maxParam": "46", "machineId": 133, "isPrivate": false}, {"id": 83852, "name": "Upper Temperature Threshold (Top Temp Sensor)", "macro": "EEP_UPPER_TEMP_THRESHOLD", "description": "Temperature of the top fridge sensor (in degrees Fahrenheit) above which the compressor is turned ON", "address": 16, "boardName": "Cooling", "boardId": 576, "paramType": "SINGLE", "defaultParam": "44", "currentValue": "44", "newValue": null, "minParam": "32", "maxParam": "46", "machineId": 133, "isPrivate": false}, {"id": 83853, "name": "<PERSON><PERSON>ump ON Time", "macro": "EEP_DRAIN_ON_TIME", "description": "Time the drain pump is turned ON (in seconds)", "address": 80, "boardName": "Cooling", "boardId": 576, "paramType": "WORD", "defaultParam": "60", "currentValue": "60", "newValue": null, "minParam": "10", "maxParam": "1800", "machineId": 133, "isPrivate": false}, {"id": 83854, "name": "Drain Auto Mode Enable", "macro": "EEP_DRAIN_AUTO_ENABLE", "description": "Enable automatic drain pump cycling based on preset ON time and OFF time periods", "address": 112, "boardName": "Cooling", "boardId": 576, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 133, "isPrivate": false}, {"id": 83855, "name": "<PERSON><PERSON>ump OFF Time", "macro": "EEP_DRAIN_OFF_TIME", "description": "Time the drain pump is turned OFF (in seconds)", "address": 96, "boardName": "Cooling", "boardId": 576, "paramType": "WORD", "defaultParam": "540", "currentValue": "540", "newValue": null, "minParam": "10", "maxParam": "10800", "machineId": 133, "isPrivate": false}, {"id": 83856, "name": "Ingredient Sensor Enable - A1, A2, B1, B2", "macro": "EEP_BUBBLE_DETECT_ENABLE", "description": "Enable the use of ingredient detection sensors for a group of 4 house mix ingredients", "address": 160, "boardName": "Pump Board 7", "boardId": 7, "paramType": "BOOLEAN", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 133, "isPrivate": false}, {"id": 83857, "name": "Allow Retraction A1, A2, B1, B2", "macro": "EEP_ALLOW_RETRACTION", "description": "Allow user-configurable retraction for this board's ingredients", "address": 276, "boardName": "Pump Board 7", "boardId": 7, "paramType": "BOOLEAN", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 133, "isPrivate": false}, {"id": 83858, "name": "Allow Runout A1, A2, B1, B2", "macro": "EEP_ALLOW_RUNOUT", "description": "Allow runout errors for this board's ingredients during drink requests", "address": 277, "boardName": "Pump Board 7", "boardId": 7, "paramType": "BOOLEAN", "defaultParam": "1", "currentValue": "10", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 133, "isPrivate": false}, {"id": 83859, "name": "First Dispense Overpour Quantity - A1, A2, B1, B2", "macro": "EEP_STARTUP_QTY", "description": "Quantity (expressed in 1/20 fl. oz. increments) to overprime this board's ingredients beyond their priming lengths for the first pour of the day", "address": 305, "boardName": "Pump Board 7", "boardId": 7, "paramType": "BYTE", "defaultParam": "10", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "100", "machineId": 133, "isPrivate": false}, {"id": 83860, "name": "Ingredient Line Status Enable (A1, B1, A2, B2)", "macro": "EEP_INGR_LINE_STATUS_EN", "description": "Enable ingredient prime status of pumped ingredients", "address": 436, "boardName": "Pump Board 7", "boardId": 7, "paramType": "BOOLEAN", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 133, "isPrivate": false}, {"id": 83861, "name": "Ingredient Sensor Enable - A3, A4, B3, B4", "macro": "EEP_BUBBLE_DETECT_ENABLE", "description": "Enable the use of ingredient detection sensors for a group of 4 house mix ingredients", "address": 160, "boardName": "Pump Board 8", "boardId": 8, "paramType": "BOOLEAN", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 133, "isPrivate": false}, {"id": 83862, "name": "Allow Retraction A3, A4, B3, B4", "macro": "EEP_ALLOW_RETRACTION", "description": "Allow user-configurable retraction for this board's ingredients", "address": 276, "boardName": "Pump Board 8", "boardId": 8, "paramType": "BOOLEAN", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 133, "isPrivate": false}, {"id": 83863, "name": "Allow Runout A3, A4, B3, B4", "macro": "EEP_ALLOW_RUNOUT", "description": "Allow runout errors for this board's ingredients during drink requests", "address": 277, "boardName": "Pump Board 8", "boardId": 8, "paramType": "BOOLEAN", "defaultParam": "1", "currentValue": "10", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 133, "isPrivate": false}, {"id": 83864, "name": "Ingredient Sensor Enable - C4, D4, E4, F4", "macro": "EEP_BUBBLE_DETECT_ENABLE", "description": "Enable the use of ingredient detection sensors for an entire column of ingredients", "address": 160, "boardName": "Pump Board 3", "boardId": 3, "paramType": "BOOLEAN", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 133, "isPrivate": false}, {"id": 83865, "name": "Allow Runout C5, D5, E5, F5", "macro": "EEP_ALLOW_RUNOUT", "description": "Allow runout errors for this board's ingredients during drink requests", "address": 277, "boardName": "Pump Board 4", "boardId": 4, "paramType": "BOOLEAN", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 133, "isPrivate": false}, {"id": 83866, "name": "Ingredient Line Status Enable (C4, D4, E4, F4)", "macro": "EEP_INGR_LINE_STATUS_EN", "description": "Enable ingredient prime status of pumped ingredients", "address": 436, "boardName": "Pump Board 3", "boardId": 3, "paramType": "BOOLEAN", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 133, "isPrivate": false}, {"id": 83867, "name": "Ingredient Line Status Enable (A3, B3, A4, B4)", "macro": "EEP_INGR_LINE_STATUS_EN", "description": "Enable ingredient prime status of pumped ingredients", "address": 436, "boardName": "Pump Board 8", "boardId": 8, "paramType": "BOOLEAN", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 133, "isPrivate": false}, {"id": 83868, "name": "Ingredient Line Status Enable (C5, D5, E5, F5)", "macro": "EEP_INGR_LINE_STATUS_EN", "description": "Enable ingredient prime status of pumped ingredients", "address": 436, "boardName": "Pump Board 4", "boardId": 4, "paramType": "BOOLEAN", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 133, "isPrivate": false}, {"id": 83869, "name": "First Dispense Overpour Quantity - C4, D4, E4, F4", "macro": "EEP_STARTUP_QTY", "description": "Quantity (expressed in 1/20 fl. oz. increments) to overprime this board's ingredients beyond their priming lengths for the first pour of the day", "address": 305, "boardName": "Pump Board 3", "boardId": 3, "paramType": "BYTE", "defaultParam": "10", "currentValue": "10", "newValue": null, "minParam": "0", "maxParam": "100", "machineId": 133, "isPrivate": false}, {"id": 83870, "name": "Allow Runout C4, D4, E4, F4", "macro": "EEP_ALLOW_RUNOUT", "description": "Allow runout errors for this board's ingredients during drink requests", "address": 277, "boardName": "Pump Board 3", "boardId": 3, "paramType": "BOOLEAN", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 133, "isPrivate": false}, {"id": 83871, "name": "Ingredient Sensor Enable - C5, D5, E5, F5", "macro": "EEP_BUBBLE_DETECT_ENABLE", "description": "Enable the use of ingredient detection sensors for an entire column of ingredients", "address": 160, "boardName": "Pump Board 4", "boardId": 4, "paramType": "BOOLEAN", "defaultParam": "1", "currentValue": "10", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 133, "isPrivate": false}, {"id": 83872, "name": "Allow Retraction C5, D5, E5, F5", "macro": "EEP_ALLOW_RETRACTION", "description": "Allow user-configurable retraction for this board's ingredients", "address": 276, "boardName": "Pump Board 4", "boardId": 4, "paramType": "BOOLEAN", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 133, "isPrivate": false}, {"id": 83873, "name": "First Dispense Overpour Quantity - C2, E2, D2, F2", "macro": "EEP_STARTUP_QTY", "description": "Quantity (expressed in 1/20 fl. oz. increments) to overprime this board's ingredients beyond their priming lengths for the first pour of the day", "address": 305, "boardName": "Pump Board 1", "boardId": 1, "paramType": "BYTE", "defaultParam": "10", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "100", "machineId": 133, "isPrivate": false}, {"id": 83874, "name": "First Dispense Overpour Quantity - C5, D5, E5, F5", "macro": "EEP_STARTUP_QTY", "description": "Quantity (expressed in 1/20 fl. oz. increments) to overprime this board's ingredients beyond their priming lengths for the first pour of the day", "address": 305, "boardName": "Pump Board 4", "boardId": 4, "paramType": "BYTE", "defaultParam": "10", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "100", "machineId": 133, "isPrivate": false}, {"id": 83875, "name": "Ingredient Line Status Enable (C2, D2, E2, F2)", "macro": "EEP_INGR_LINE_STATUS_EN", "description": "Enable ingredient prime status of pumped ingredients", "address": 436, "boardName": "Pump Board 1", "boardId": 1, "paramType": "BOOLEAN", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 133, "isPrivate": false}, {"id": 83876, "name": "Allow Retraction C4, D4, E4, F4", "macro": "EEP_ALLOW_RETRACTION", "description": "Allow user-configurable retraction for this board's ingredients", "address": 276, "boardName": "Pump Board 3", "boardId": 3, "paramType": "BOOLEAN", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 133, "isPrivate": false}, {"id": 83877, "name": "Allow Retraction C3, D3, E3, F3", "macro": "EEP_ALLOW_RETRACTION", "description": "Allow user-configurable retraction for this board's ingredients", "address": 276, "boardName": "Pump Board 2", "boardId": 2, "paramType": "BOOLEAN", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 133, "isPrivate": false}, {"id": 83878, "name": "Allow Retraction C2, E2, D2, F2", "macro": "EEP_ALLOW_RETRACTION", "description": "Allow user-configurable retraction for this board's ingredients", "address": 276, "boardName": "Pump Board 1", "boardId": 1, "paramType": "BOOLEAN", "defaultParam": "1", "currentValue": "10", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 133, "isPrivate": false}, {"id": 83879, "name": "Ingredient Line Status Enable (C3, D3, E3, F3)", "macro": "EEP_INGR_LINE_STATUS_EN", "description": "Enable ingredient prime status of pumped ingredients", "address": 436, "boardName": "Pump Board 2", "boardId": 2, "paramType": "BOOLEAN", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 133, "isPrivate": false}, {"id": 83880, "name": "Ingredient Sensor Enable - C3, D3, E3, F3", "macro": "EEP_BUBBLE_DETECT_ENABLE", "description": "Enable the use of ingredient detection sensors for an entire column of ingredients", "address": 160, "boardName": "Pump Board 2", "boardId": 2, "paramType": "BOOLEAN", "defaultParam": "1", "currentValue": "10", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 133, "isPrivate": false}, {"id": 83881, "name": "First Dispense Overpour Quantity - C3, D3, E3, F3", "macro": "EEP_STARTUP_QTY", "description": "Quantity (expressed in 1/20 fl. oz. increments) to overprime this board's ingredients beyond their priming lengths for the first pour of the day", "address": 305, "boardName": "Pump Board 2", "boardId": 2, "paramType": "BYTE", "defaultParam": "10", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "100", "machineId": 133, "isPrivate": false}, {"id": 83882, "name": "Allow Runout C3, D3, E3, F3", "macro": "EEP_ALLOW_RUNOUT", "description": "Allow runout errors for this board's ingredients during drink requests", "address": 277, "boardName": "Pump Board 2", "boardId": 2, "paramType": "BOOLEAN", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 133, "isPrivate": false}, {"id": 83883, "name": "Allow Runout C2, E2, D2, F2", "macro": "EEP_ALLOW_RUNOUT", "description": "Allow runout errors for this board's ingredients during drink requests", "address": 277, "boardName": "Pump Board 1", "boardId": 1, "paramType": "BOOLEAN", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 133, "isPrivate": false}, {"id": 83884, "name": "Ingredient Sensor Enable - C2, E2, D2, F2", "macro": "EEP_BUBBLE_DETECT_ENABLE", "description": "Enable the use of ingredient detection sensors for an entire column of ingredients", "address": 160, "boardName": "Pump Board 1", "boardId": 1, "paramType": "BOOLEAN", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 133, "isPrivate": false}, {"id": 83885, "name": "Ingredient Line Status Enable (C6, D6, E6, F6)", "macro": "EEP_INGR_LINE_STATUS_EN", "description": "Enable ingredient prime status of pumped ingredients", "address": 436, "boardName": "Pump Board 5", "boardId": 5, "paramType": "BOOLEAN", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 133, "isPrivate": false}, {"id": 83886, "name": "First Dispense Overpour Quantity - C6, D6, E6, F6", "macro": "EEP_STARTUP_QTY", "description": "Quantity (expressed in 1/20 fl. oz. increments) to overprime this board's ingredients beyond their priming lengths for the first pour of the day", "address": 305, "boardName": "Pump Board 5", "boardId": 5, "paramType": "BYTE", "defaultParam": "10", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "100", "machineId": 133, "isPrivate": false}, {"id": 83887, "name": "Allow Runout C6, D6, E6, F6", "macro": "EEP_ALLOW_RUNOUT", "description": "Allow runout errors for this board's ingredients during drink requests", "address": 277, "boardName": "Pump Board 5", "boardId": 5, "paramType": "BOOLEAN", "defaultParam": "1", "currentValue": "10", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 133, "isPrivate": false}, {"id": 83888, "name": "Allow Retraction C6, D6, E6, F6", "macro": "EEP_ALLOW_RETRACTION", "description": "Allow user-configurable retraction for this board's ingredients", "address": 276, "boardName": "Pump Board 5", "boardId": 5, "paramType": "BOOLEAN", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 133, "isPrivate": false}, {"id": 83889, "name": "Ingredient Sensor Enable - C6, D6, E6, F6", "macro": "EEP_BUBBLE_DETECT_ENABLE", "description": "Enable the use of ingredient detection sensors for an entire column of ingredients", "address": 160, "boardName": "Pump Board 5", "boardId": 5, "paramType": "BOOLEAN", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 133, "isPrivate": false}, {"id": 83890, "name": "Ingredient Line Status Enable (A5, B5, A6, B6)", "macro": "EEP_INGR_LINE_STATUS_EN", "description": "Enable ingredient prime status of pumped ingredients", "address": 436, "boardName": "Pump Board 9", "boardId": 9, "paramType": "BOOLEAN", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 133, "isPrivate": false}, {"id": 83891, "name": "First Dispense Overpour Quantity - A5, A6, B5, B6", "macro": "EEP_STARTUP_QTY", "description": "Quantity (expressed in 1/20 fl. oz. increments) to overprime this board's ingredients beyond their priming lengths for the first pour of the day", "address": 305, "boardName": "Pump Board 9", "boardId": 9, "paramType": "BYTE", "defaultParam": "10", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "100", "machineId": 133, "isPrivate": false}, {"id": 83892, "name": "Allow Runout A5, A6, B5, B6", "macro": "EEP_ALLOW_RUNOUT", "description": "Allow runout errors for this board's ingredients during drink requests", "address": 277, "boardName": "Pump Board 9", "boardId": 9, "paramType": "BOOLEAN", "defaultParam": "1", "currentValue": "10", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 133, "isPrivate": false}, {"id": 83893, "name": "Allow Retraction A5, A6, B5, B6", "macro": "EEP_ALLOW_RETRACTION", "description": "Allow user-configurable retraction for this board's ingredients", "address": 276, "boardName": "Pump Board 9", "boardId": 9, "paramType": "BOOLEAN", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 133, "isPrivate": false}, {"id": 83894, "name": "Ingredient Sensor Enable - A5, A6, B5, B6", "macro": "EEP_BUBBLE_DETECT_ENABLE`", "description": "Enable the use of ingredient detection sensors for a group of 4 house mix ingredients", "address": 160, "boardName": "Pump Board 9", "boardId": 9, "paramType": "BOOLEAN", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 133, "isPrivate": false}, {"id": 83895, "name": "First Dispense Overpour Quantity - A3, A4, B3, B4", "macro": "EEP_STARTUP_QTY", "description": "Quantity (expressed in 1/20 fl. oz. increments) to overprime this board's ingredients beyond their priming lengths for the first pour of the day", "address": 305, "boardName": "Pump Board 8", "boardId": 8, "paramType": "BYTE", "defaultParam": "10", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "100", "machineId": 133, "isPrivate": false}, {"id": 83896, "name": "Bib Cleaning Quantity (every drink)", "macro": "EEP_CLEAN_BIB_QTY", "description": "Amount (in 1/20 fl oz increments) of water to dispense after a drink is dispensed to clean the bib and diffuser. This is also 1/4 of the amount of water used to clean the bib during refresh cycles if ", "address": 336, "boardName": "Master", "boardId": 65535, "paramType": "WORD", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "20", "machineId": 133, "isPrivate": false}, {"id": 83897, "name": "Enable Water Rinse", "macro": "EEP_ENABLE_WATER_RINSE", "description": "Spray water into the bib during a refresh to clean out the nozzle", "address": 371, "boardName": "Master", "boardId": 65535, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 133, "isPrivate": false}, {"id": 83898, "name": "First Dispense Overpour Quantity - T1 - T4", "macro": "EEP_STARTUP_QTY", "description": "Quantity (expressed in 1/20 fl. oz. increments) to overprime this board's ingredients beyond their priming lengths for the first pour of the day", "address": 305, "boardName": "Pump Board 13", "boardId": 13, "paramType": "BYTE", "defaultParam": "10", "currentValue": "10", "newValue": null, "minParam": "0", "maxParam": "100", "machineId": 133, "isPrivate": false}, {"id": 83899, "name": "Allow Retraction T1 - T4", "macro": "EEP_ALLOW_RETRACTION", "description": "Allow user-configurable retraction for this board's ingredients", "address": 276, "boardName": "Pump Board 13", "boardId": 13, "paramType": "BOOLEAN", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 133, "isPrivate": false}, {"id": 83900, "name": "Ingredient Sensor Enable - T1 - T4", "macro": "EEP_BUBBLE_DETECT_ENABLE", "description": "Enable the use of ingredient detection sensors for a group of 4 house mix ingredients", "address": 160, "boardName": "Pump Board 13", "boardId": 13, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 133, "isPrivate": false}, {"id": 83901, "name": "Allow Runout T1 - T4", "macro": "EEP_ALLOW_RUNOUT", "description": "Allow runout errors for this board's ingredients during drink requests", "address": 277, "boardName": "Pump Board 13", "boardId": 13, "paramType": "BOOLEAN", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 133, "isPrivate": false}, {"id": 83902, "name": "Ingredient Line Status Enable (T1 - T4)", "macro": "EEP_INGR_LINE_STATUS_EN", "description": "Enable ingredient prime status of pumped ingredients", "address": 436, "boardName": "Pump Board 13", "boardId": 13, "paramType": "BOOLEAN", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 133, "isPrivate": false}, {"id": 83903, "name": "Ingredient Line Status Enable (T5 - T8)", "macro": "EEP_INGR_LINE_STATUS_EN", "description": "Enable ingredient prime status of pumped ingredients", "address": 436, "boardName": "Pump Board 14", "boardId": 14, "paramType": "BOOLEAN", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 133, "isPrivate": false}, {"id": 83904, "name": "Ingredient Sensor Enable - T5 - T8", "macro": "EEP_BUBBLE_DETECT_ENABLE", "description": "Enable the use of ingredient detection sensors for a group of 4 house mix ingredients", "address": 160, "boardName": "Pump Board 14", "boardId": 14, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 133, "isPrivate": false}, {"id": 83905, "name": "First Dispense Overpour Quantity - T5 - T8", "macro": "EEP_STARTUP_QTY", "description": "Quantity (expressed in 1/20 fl. oz. increments) to overprime this board's ingredients beyond their priming lengths for the first pour of the day", "address": 305, "boardName": "Pump Board 14", "boardId": 14, "paramType": "BYTE", "defaultParam": "10", "currentValue": "10", "newValue": null, "minParam": "0", "maxParam": "100", "machineId": 133, "isPrivate": false}, {"id": 83906, "name": "Allow Retraction T5 - T8", "macro": "EEP_ALLOW_RETRACTION", "description": "Allow user-configurable retraction for this board's ingredients", "address": 276, "boardName": "Pump Board 14", "boardId": 14, "paramType": "BOOLEAN", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 133, "isPrivate": false}, {"id": 83907, "name": "Allow Runout T5 - T8", "macro": "EEP_ALLOW_RUNOUT", "description": "Allow runout errors for this board's ingredients during drink requests", "address": 277, "boardName": "Pump Board 14", "boardId": 14, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 133, "isPrivate": false}, {"id": 83914, "name": "Allow Retraction C1, D1, E1, F1", "macro": "EEP_ALLOW_RETRACTION", "description": "Allow user-configurable retraction for this board's ingredients", "address": 276, "boardName": "Pump Board 0", "boardId": 0, "paramType": "BOOLEAN", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 133, "isPrivate": false}, {"id": 83915, "name": "Allow Runout C1, D1, E1, F1", "macro": "EEP_ALLOW_RUNOUT", "description": "Allow runout errors for this board's ingredients during drink requests", "address": 277, "boardName": "Pump Board 0", "boardId": 0, "paramType": "BOOLEAN", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 133, "isPrivate": false}, {"id": 83916, "name": "Bubble Detection - A1", "macro": "EEP_BUBBLE_DETECTION_0", "description": "Flag indicating whether a bubble/air is detected in A1's dispensing line", "address": 16, "boardName": "Pump Board 7", "boardId": 7, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 133, "isPrivate": true}, {"id": 83917, "name": "Bubble Detection - A2", "macro": "EEP_BUBBLE_DETECTION_2", "description": "Flag indicating whether a bubble/air is detected in A2's dispensing line", "address": 48, "boardName": "Pump Board 7", "boardId": 7, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 133, "isPrivate": true}, {"id": 83918, "name": "Bubble Detection - A3", "macro": "EEP_BUBBLE_DETECTION_0", "description": "Flag indicating whether a bubble/air is detected in A3's dispensing line", "address": 16, "boardName": "Pump Board 8", "boardId": 8, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 133, "isPrivate": true}, {"id": 83919, "name": "Bubble Detection - A4", "macro": "EEP_BUBBLE_DETECTION_2", "description": "Flag indicating whether a bubble/air is detected in A4's dispensing line", "address": 48, "boardName": "Pump Board 8", "boardId": 8, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 133, "isPrivate": true}, {"id": 83920, "name": "Bubble Detection - A5", "macro": "EEP_BUBBLE_DETECTION_0", "description": "Flag indicating whether a bubble/air is detected in A5's dispensing line", "address": 16, "boardName": "Pump Board 9", "boardId": 9, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 133, "isPrivate": true}, {"id": 83921, "name": "Bubble Detection - A6", "macro": "EEP_BUBBLE_DETECTION_2", "description": "Flag indicating whether a bubble/air is detected in A6's dispensing line", "address": 48, "boardName": "Pump Board 9", "boardId": 9, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 133, "isPrivate": true}, {"id": 83922, "name": "Bubble Detection - B1", "macro": "EEP_BUBBLE_DETECTION_1", "description": "Flag indicating whether a bubble/air is detected in B1's dispensing line", "address": 32, "boardName": "Pump Board 7", "boardId": 7, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 133, "isPrivate": true}, {"id": 83923, "name": "Bubble Detection - B2", "macro": "EEP_BUBBLE_DETECTION_3", "description": "Flag indicating whether a bubble/air is detected in B2's dispensing line", "address": 64, "boardName": "Pump Board 7", "boardId": 7, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 133, "isPrivate": true}, {"id": 83924, "name": "Bubble Detection - B3", "macro": "EEP_BUBBLE_DETECTION_1", "description": "Flag indicating whether a bubble/air is detected in B3's dispensing line", "address": 32, "boardName": "Pump Board 8", "boardId": 8, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 133, "isPrivate": true}, {"id": 83925, "name": "Bubble Detection - B4", "macro": "EEP_BUBBLE_DETECTION_3", "description": "Flag indicating whether a bubble/air is detected in B4's dispensing line", "address": 64, "boardName": "Pump Board 8", "boardId": 8, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 133, "isPrivate": true}, {"id": 83926, "name": "Bubble Detection - B5", "macro": "EEP_BUBBLE_DETECTION_1", "description": "Flag indicating whether a bubble/air is detected in B5's dispensing line", "address": 32, "boardName": "Pump Board 9", "boardId": 9, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 133, "isPrivate": true}, {"id": 83927, "name": "Bubble Detection - B6", "macro": "EEP_BUBBLE_DETECTION_3", "description": "Flag indicating whether a bubble/air is detected in B6's dispensing line", "address": 64, "boardName": "Pump Board 9", "boardId": 9, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 133, "isPrivate": true}, {"id": 83928, "name": "Bubble Detection - C1", "macro": "EEP_BUBBLE_DETECTION_0", "description": "Flag indicating whether a bubble/air is detected in C1's dispensing line", "address": 16, "boardName": "Pump Board 0", "boardId": 0, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 133, "isPrivate": true}, {"id": 83929, "name": "Bubble Detection - C2", "macro": "EEP_BUBBLE_DETECTION_0", "description": "Flag indicating whether a bubble/air is detected in C2's dispensing line", "address": 16, "boardName": "Pump Board 1", "boardId": 1, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 133, "isPrivate": true}, {"id": 83930, "name": "Bubble Detection - C3", "macro": "EEP_BUBBLE_DETECTION_0", "description": "Flag indicating whether a bubble/air is detected in C3's dispensing line", "address": 16, "boardName": "Pump Board 2", "boardId": 2, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 133, "isPrivate": true}, {"id": 83931, "name": "Bubble Detection - C4", "macro": "EEP_BUBBLE_DETECTION_0", "description": "Flag indicating whether a bubble/air is detected in C4's dispensing line", "address": 16, "boardName": "Pump Board 3", "boardId": 3, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 133, "isPrivate": true}, {"id": 83932, "name": "Bubble Detection - C5", "macro": "EEP_BUBBLE_DETECTION_0", "description": "Flag indicating whether a bubble/air is detected in C5's dispensing line", "address": 16, "boardName": "Pump Board 4", "boardId": 4, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 133, "isPrivate": true}, {"id": 83933, "name": "Bubble Detection - C6", "macro": "EEP_BUBBLE_DETECTION_0", "description": "Flag indicating whether a bubble/air is detected in C6's dispensing line", "address": 16, "boardName": "Pump Board 5", "boardId": 5, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 133, "isPrivate": true}, {"id": 83934, "name": "Bubble Detection - D1", "macro": "EEP_BUBBLE_DETECTION_1", "description": "Flag indicating whether a bubble/air is detected in D1's dispensing line", "address": 32, "boardName": "Pump Board 0", "boardId": 0, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 133, "isPrivate": true}, {"id": 83935, "name": "Bubble Detection - D2", "macro": "EEP_BUBBLE_DETECTION_1", "description": "Flag indicating whether a bubble/air is detected in D2's dispensing line", "address": 32, "boardName": "Pump Board 1", "boardId": 1, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 133, "isPrivate": true}, {"id": 83936, "name": "Bubble Detection - D3", "macro": "EEP_BUBBLE_DETECTION_1", "description": "Flag indicating whether a bubble/air is detected in D3's dispensing line", "address": 32, "boardName": "Pump Board 2", "boardId": 2, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 133, "isPrivate": true}, {"id": 83937, "name": "Bubble Detection - D4", "macro": "EEP_BUBBLE_DETECTION_1", "description": "Flag indicating whether a bubble/air is detected in D4's dispensing line", "address": 32, "boardName": "Pump Board 3", "boardId": 3, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 133, "isPrivate": true}, {"id": 83938, "name": "Bubble Detection - D5", "macro": "EEP_BUBBLE_DETECTION_1", "description": "Flag indicating whether a bubble/air is detected in D5's dispensing line", "address": 32, "boardName": "Pump Board 4", "boardId": 4, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 133, "isPrivate": true}, {"id": 83939, "name": "Bubble Detection - D6", "macro": "EEP_BUBBLE_DETECTION_1", "description": "Flag indicating whether a bubble/air is detected in D6's dispensing line", "address": 32, "boardName": "Pump Board 5", "boardId": 5, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 133, "isPrivate": true}, {"id": 83940, "name": "Bubble Detection - E1", "macro": "EEP_BUBBLE_DETECTION_2", "description": "Flag indicating whether a bubble/air is detected in E1's dispensing line", "address": 48, "boardName": "Pump Board 0", "boardId": 0, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 133, "isPrivate": true}, {"id": 83941, "name": "Bubble Detection - E2", "macro": "EEP_BUBBLE_DETECTION_2", "description": "Flag indicating whether a bubble/air is detected in E2's dispensing line", "address": 48, "boardName": "Pump Board 1", "boardId": 1, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 133, "isPrivate": true}, {"id": 83942, "name": "Bubble Detection - E3", "macro": "EEP_BUBBLE_DETECTION_2", "description": "Flag indicating whether a bubble/air is detected in E3's dispensing line", "address": 48, "boardName": "Pump Board 2", "boardId": 2, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 133, "isPrivate": true}, {"id": 83943, "name": "Bubble Detection - E4", "macro": "EEP_BUBBLE_DETECTION_2", "description": "Flag indicating whether a bubble/air is detected in E4's dispensing line", "address": 48, "boardName": "Pump Board 3", "boardId": 3, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 133, "isPrivate": true}, {"id": 83944, "name": "Bubble Detection - E5", "macro": "EEP_BUBBLE_DETECTION_2", "description": "Flag indicating whether a bubble/air is detected in E5's dispensing line", "address": 48, "boardName": "Pump Board 4", "boardId": 4, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 133, "isPrivate": true}, {"id": 83945, "name": "Bubble Detection - E6", "macro": "EEP_BUBBLE_DETECTION_2", "description": "Flag indicating whether a bubble/air is detected in E6's dispensing line", "address": 48, "boardName": "Pump Board 5", "boardId": 5, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 133, "isPrivate": true}, {"id": 83946, "name": "Bubble Detection - F1", "macro": "EEP_BUBBLE_DETECTION_3", "description": "Flag indicating whether a bubble/air is detected in F1's dispensing line", "address": 64, "boardName": "Pump Board 0", "boardId": 0, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 133, "isPrivate": true}, {"id": 83947, "name": "Bubble Detection - F2", "macro": "EEP_BUBBLE_DETECTION_3", "description": "Flag indicating whether a bubble/air is detected in F2's dispensing line", "address": 64, "boardName": "Pump Board 1", "boardId": 1, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 133, "isPrivate": true}, {"id": 83948, "name": "Bubble Detection - F3", "macro": "EEP_BUBBLE_DETECTION_3", "description": "Flag indicating whether a bubble/air is detected in F3's dispensing line", "address": 64, "boardName": "Pump Board 2", "boardId": 2, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 133, "isPrivate": true}, {"id": 83949, "name": "Bubble Detection - F4", "macro": "EEP_BUBBLE_DETECTION_3", "description": "Flag indicating whether a bubble/air is detected in F4's dispensing line", "address": 64, "boardName": "Pump Board 3", "boardId": 3, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 133, "isPrivate": true}, {"id": 83950, "name": "Bubble Detection - F5", "macro": "EEP_BUBBLE_DETECTION_3", "description": "Flag indicating whether a bubble/air is detected in F5's dispensing line", "address": 64, "boardName": "Pump Board 4", "boardId": 4, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 133, "isPrivate": true}, {"id": 83951, "name": "Bubble Detection - F6", "macro": "EEP_BUBBLE_DETECTION_3", "description": "Flag indicating whether a bubble/air is detected in F6's dispensing line", "address": 64, "boardName": "Pump Board 5", "boardId": 5, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 133, "isPrivate": true}, {"id": 83952, "name": "Bubble Detection - T1", "macro": "EEP_BUBBLE_DETECTION_0", "description": "Flag indicating whether a bubble/air is detected in T1's dispensing line", "address": 16, "boardName": "Pump Board 13", "boardId": 13, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 133, "isPrivate": true}, {"id": 83953, "name": "Bubble Detection - T2", "macro": "EEP_BUBBLE_DETECTION_1", "description": "Flag indicating whether a bubble/air is detected in T2's dispensing line", "address": 32, "boardName": "Pump Board 13", "boardId": 13, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 133, "isPrivate": true}, {"id": 83954, "name": "Bubble Detection - T3", "macro": "EEP_BUBBLE_DETECTION_2", "description": "Flag indicating whether a bubble/air is detected in T3's dispensing line", "address": 48, "boardName": "Pump Board 13", "boardId": 13, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 133, "isPrivate": true}, {"id": 83955, "name": "Bubble Detection - T4", "macro": "EEP_BUBBLE_DETECTION_3", "description": "Flag indicating whether a bubble/air is detected in T4's dispensing line", "address": 64, "boardName": "Pump Board 13", "boardId": 13, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 133, "isPrivate": true}, {"id": 83956, "name": "Bubble Detection - T5", "macro": "EEP_BUBBLE_DETECTION_0", "description": "Flag indicating whether a bubble/air is detected in A5's dispensing line", "address": 16, "boardName": "Pump Board 14", "boardId": 14, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 133, "isPrivate": true}, {"id": 83957, "name": "Bubble Detection - T6", "macro": "EEP_BUBBLE_DETECTION_1", "description": "Flag indicating whether a bubble/air is detected in T6's dispensing line", "address": 32, "boardName": "Pump Board 14", "boardId": 14, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 133, "isPrivate": true}, {"id": 83958, "name": "Bubble Detection - T7", "macro": "EEP_BUBBLE_DETECTION_2", "description": "Flag indicating whether a bubble/air is detected in T7's dispensing line", "address": 48, "boardName": "Pump Board 14", "boardId": 14, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 133, "isPrivate": true}, {"id": 83959, "name": "Bubble Detection - T8", "macro": "EEP_BUBBLE_DETECTION_3", "description": "Flag indicating whether a bubble/air is detected in T8's dispensing line", "address": 64, "boardName": "Pump Board 14", "boardId": 14, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 133, "isPrivate": true}, {"id": 83960, "name": "Bubble Detection Threshold - A1", "macro": "EEP_BUBBLE_ROC_THRESHOLD_0", "description": "Rate of change value required to signify a runout. (Default Oat milk = 30, almond/dairy = 50, all other 75+)", "address": 368, "boardName": "Pump Board 7", "boardId": 7, "paramType": "BYTE", "defaultParam": "50", "currentValue": "50", "newValue": "50", "minParam": "15", "maxParam": "150", "machineId": 133, "isPrivate": false}, {"id": 83961, "name": "Bubble Detection Threshold - A2", "macro": "EEP_BUBBLE_ROC_THRESHOLD_2", "description": "Rate of change value required to signify a runout. (Default Oat milk = 30, almond/dairy = 50, all other 75+)", "address": 370, "boardName": "Pump Board 7", "boardId": 7, "paramType": "BYTE", "defaultParam": "50", "currentValue": "50", "newValue": "50", "minParam": "15", "maxParam": "150", "machineId": 133, "isPrivate": false}, {"id": 83962, "name": "Bubble Detection Threshold - A3", "macro": "EEP_BUBBLE_ROC_THRESHOLD_0", "description": "Rate of change value required to signify a runout. (Default Oat milk = 30, almond/dairy = 50, all other 75+)", "address": 368, "boardName": "Pump Board 8", "boardId": 8, "paramType": "BYTE", "defaultParam": "50", "currentValue": "50", "newValue": "50", "minParam": "15", "maxParam": "150", "machineId": 133, "isPrivate": false}, {"id": 83963, "name": "Bubble Detection Threshold - A4", "macro": "EEP_BUBBLE_ROC_THRESHOLD_2", "description": "Rate of change value required to signify a runout. (Default Oat milk = 30, almond/dairy = 50, all other 75+)", "address": 370, "boardName": "Pump Board 8", "boardId": 8, "paramType": "BYTE", "defaultParam": "50", "currentValue": "50", "newValue": "50", "minParam": "15", "maxParam": "150", "machineId": 133, "isPrivate": false}, {"id": 83964, "name": "Bubble Detection Threshold - A5", "macro": "EEP_BUBBLE_ROC_THRESHOLD_0", "description": "Rate of change value required to signify a runout. (Default Oat milk = 30, almond/dairy = 50, all other 75+)", "address": 368, "boardName": "Pump Board 9", "boardId": 9, "paramType": "BYTE", "defaultParam": "50", "currentValue": "50", "newValue": "50", "minParam": "15", "maxParam": "150", "machineId": 133, "isPrivate": false}, {"id": 83965, "name": "Bubble Detection Threshold - A6", "macro": "EEP_BUBBLE_ROC_THRESHOLD_2", "description": "Rate of change value required to signify a runout. (Default Oat milk = 30, almond/dairy = 50, all other 75+)", "address": 370, "boardName": "Pump Board 9", "boardId": 9, "paramType": "BYTE", "defaultParam": "50", "currentValue": "50", "newValue": "0", "minParam": "15", "maxParam": "150", "machineId": 133, "isPrivate": false}, {"id": 83966, "name": "Bubble Detection Threshold - B5", "macro": "EEP_BUBBLE_ROC_THRESHOLD_1", "description": "Rate of change value required to signify a runout. (Default Oat milk = 30, almond/dairy = 50, all other 75+)", "address": 369, "boardName": "Pump Board 9", "boardId": 9, "paramType": "BYTE", "defaultParam": "50", "currentValue": "50", "newValue": "50", "minParam": "15", "maxParam": "150", "machineId": 133, "isPrivate": false}, {"id": 83967, "name": "Bubble Detection Threshold - B6", "macro": "EEP_BUBBLE_ROC_THRESHOLD_3", "description": "Rate of change value required to signify a runout. (Default Oat milk = 30, almond/dairy = 50, all other 75+)", "address": 371, "boardName": "Pump Board 9", "boardId": 9, "paramType": "BYTE", "defaultParam": "50", "currentValue": "50", "newValue": "0", "minParam": "15", "maxParam": "150", "machineId": 133, "isPrivate": false}, {"id": 83968, "name": "Bubble Detection Threshold - C5", "macro": "EEP_BUBBLE_ROC_THRESHOLD_0", "description": "Rate of change value required to signify a runout. (Default Oat milk = 30, almond/dairy = 50, all other 75+)", "address": 368, "boardName": "Pump Board 4", "boardId": 4, "paramType": "BYTE", "defaultParam": "50", "currentValue": "50", "newValue": "50", "minParam": "15", "maxParam": "150", "machineId": 133, "isPrivate": false}, {"id": 83969, "name": "Bubble Detection Threshold - C6", "macro": "EEP_BUBBLE_ROC_THRESHOLD_0", "description": "Rate of change value required to signify a runout. (Default Oat milk = 30, almond/dairy = 50, all other 75+)", "address": 368, "boardName": "Pump Board 5", "boardId": 5, "paramType": "BYTE", "defaultParam": "50", "currentValue": "50", "newValue": "50", "minParam": "15", "maxParam": "150", "machineId": 133, "isPrivate": false}, {"id": 83970, "name": "Bubble Detection Threshold - D5", "macro": "EEP_BUBBLE_ROC_THRESHOLD_1", "description": "Rate of change value required to signify a runout. (Default Oat milk = 30, almond/dairy = 50, all other 75+)", "address": 369, "boardName": "Pump Board 4", "boardId": 4, "paramType": "BYTE", "defaultParam": "50", "currentValue": "50", "newValue": "50", "minParam": "15", "maxParam": "150", "machineId": 133, "isPrivate": false}, {"id": 83971, "name": "Bubble Detection Threshold - D6", "macro": "EEP_BUBBLE_ROC_THRESHOLD_1", "description": "Rate of change value required to signify a runout. (Default Oat milk = 30, almond/dairy = 50, all other 75+)", "address": 369, "boardName": "Pump Board 5", "boardId": 5, "paramType": "BYTE", "defaultParam": "50", "currentValue": "50", "newValue": "50", "minParam": "15", "maxParam": "150", "machineId": 133, "isPrivate": false}, {"id": 83972, "name": "Bubble Detection Threshold - E5", "macro": "EEP_BUBBLE_ROC_THRESHOLD_2", "description": "Rate of change value required to signify a runout. (Default Oat milk = 30, almond/dairy = 50, all other 75+)", "address": 370, "boardName": "Pump Board 4", "boardId": 4, "paramType": "BYTE", "defaultParam": "50", "currentValue": "50", "newValue": "50", "minParam": "15", "maxParam": "150", "machineId": 133, "isPrivate": false}, {"id": 83973, "name": "Bubble Detection Threshold - E6", "macro": "EEP_BUBBLE_ROC_THRESHOLD_2", "description": "Rate of change value required to signify a runout. (Default Oat milk = 30, almond/dairy = 50, all other 75+)", "address": 370, "boardName": "Pump Board 5", "boardId": 5, "paramType": "BYTE", "defaultParam": "50", "currentValue": "50", "newValue": "50", "minParam": "15", "maxParam": "150", "machineId": 133, "isPrivate": false}, {"id": 83974, "name": "Bubble Detection Threshold - F5", "macro": "EEP_BUBBLE_ROC_THRESHOLD_3", "description": "Rate of change value required to signify a runout. (Default Oat milk = 30, almond/dairy = 50, all other 75+)", "address": 371, "boardName": "Pump Board 4", "boardId": 4, "paramType": "BYTE", "defaultParam": "50", "currentValue": "50", "newValue": "50", "minParam": "15", "maxParam": "150", "machineId": 133, "isPrivate": false}, {"id": 83975, "name": "Bubble Detection Threshold - F6", "macro": "EEP_BUBBLE_ROC_THRESHOLD_3", "description": "Rate of change value required to signify a runout. (Default Oat milk = 30, almond/dairy = 50, all other 75+)", "address": 371, "boardName": "Pump Board 5", "boardId": 5, "paramType": "BYTE", "defaultParam": "50", "currentValue": "50", "newValue": "50", "minParam": "15", "maxParam": "150", "machineId": 133, "isPrivate": false}, {"id": 83976, "name": "Bubble Detection Threshold - T1", "macro": "EEP_BUBBLE_ROC_THRESHOLD_0", "description": "Rate of change value required to signify a runout. (Default Oat milk = 30, almond/dairy = 50, all other 75+)", "address": 368, "boardName": "Pump Board 13", "boardId": 13, "paramType": "BYTE", "defaultParam": "50", "currentValue": "50", "newValue": null, "minParam": "15", "maxParam": "150", "machineId": 133, "isPrivate": false}, {"id": 83977, "name": "Bubble Detection Threshold - T2", "macro": "EEP_BUBBLE_ROC_THRESHOLD_1", "description": "Rate of change value required to signify a runout. (Default Oat milk = 30, almond/dairy = 50, all other 75+)", "address": 369, "boardName": "Pump Board 13", "boardId": 13, "paramType": "BYTE", "defaultParam": "50", "currentValue": "50", "newValue": null, "minParam": "15", "maxParam": "150", "machineId": 133, "isPrivate": false}, {"id": 83978, "name": "Bubble Detection Threshold - T3", "macro": "EEP_BUBBLE_ROC_THRESHOLD_2", "description": "Rate of change value required to signify a runout. (Default Oat milk = 30, almond/dairy = 50, all other 75+)", "address": 370, "boardName": "Pump Board 13", "boardId": 13, "paramType": "BYTE", "defaultParam": "50", "currentValue": "50", "newValue": null, "minParam": "15", "maxParam": "150", "machineId": 133, "isPrivate": false}, {"id": 83979, "name": "Bubble Detection Threshold - T4", "macro": "EEP_BUBBLE_ROC_THRESHOLD_3", "description": "Rate of change value required to signify a runout. (Default Oat milk = 30, almond/dairy = 50, all other 75+)", "address": 371, "boardName": "Pump Board 13", "boardId": 13, "paramType": "BYTE", "defaultParam": "50", "currentValue": "50", "newValue": null, "minParam": "15", "maxParam": "150", "machineId": 133, "isPrivate": false}, {"id": 83980, "name": "Bubble Detection Threshold - T5", "macro": "EEP_BUBBLE_ROC_THRESHOLD_0", "description": "Rate of change value required to signify a runout. (Default Oat milk = 30, almond/dairy = 50, all other 75+)", "address": 368, "boardName": "Pump Board 14", "boardId": 14, "paramType": "BYTE", "defaultParam": "50", "currentValue": "50", "newValue": null, "minParam": "15", "maxParam": "150", "machineId": 133, "isPrivate": false}, {"id": 83981, "name": "Bubble Detection Threshold - T6", "macro": "EEP_BUBBLE_ROC_THRESHOLD_1", "description": "Rate of change value required to signify a runout. (Default Oat milk = 30, almond/dairy = 50, all other 75+)", "address": 369, "boardName": "Pump Board 14", "boardId": 14, "paramType": "BYTE", "defaultParam": "50", "currentValue": "50", "newValue": null, "minParam": "15", "maxParam": "150", "machineId": 133, "isPrivate": false}, {"id": 83982, "name": "Bubble Detection Threshold - T7", "macro": "EEP_BUBBLE_ROC_THRESHOLD_2", "description": "Rate of change value required to signify a runout. (Default Oat milk = 30, almond/dairy = 50, all other 75+)", "address": 370, "boardName": "Pump Board 14", "boardId": 14, "paramType": "BYTE", "defaultParam": "50", "currentValue": "50", "newValue": null, "minParam": "15", "maxParam": "150", "machineId": 133, "isPrivate": false}, {"id": 83983, "name": "Bubble Detection Threshold - T8", "macro": "EEP_BUBBLE_ROC_THRESHOLD_3", "description": "Rate of change value required to signify a runout. (Default Oat milk = 30, almond/dairy = 50, all other 75+)", "address": 371, "boardName": "Pump Board 14", "boardId": 14, "paramType": "BYTE", "defaultParam": "50", "currentValue": "50", "newValue": null, "minParam": "15", "maxParam": "150", "machineId": 133, "isPrivate": false}, {"id": 83984, "name": "Calibration Factor - Bib 1", "macro": "EEP_BIB_0_CAL_VAL", "description": "Ingredient calibration factor (Number of ms per fluid oz)", "address": 16, "boardName": "Nozzle", "boardId": 560, "paramType": "WORD", "defaultParam": "2000", "currentValue": "2182", "newValue": null, "minParam": "100", "maxParam": "10000", "machineId": 133, "isPrivate": true}, {"id": 83985, "name": "Calibration Factor - Bib 2", "macro": "EEP_BIB_1_CAL_VAL", "description": "Ingredient calibration factor (Number of ms per fluid oz)", "address": 32, "boardName": "Nozzle", "boardId": 560, "paramType": "WORD", "defaultParam": "2000", "currentValue": "2284", "newValue": null, "minParam": "100", "maxParam": "10000", "machineId": 133, "isPrivate": true}, {"id": 83986, "name": "Calibration Factor - Bib 3", "macro": "EEP_BIB_2_CAL_VAL", "description": "Ingredient calibration factor (Number of ms per fluid oz)", "address": 48, "boardName": "Nozzle", "boardId": 560, "paramType": "WORD", "defaultParam": "2000", "currentValue": "2263", "newValue": null, "minParam": "100", "maxParam": "10000", "machineId": 133, "isPrivate": true}, {"id": 83987, "name": "Calibration Factor - Bib 4", "macro": "EEP_BIB_3_CAL_VAL", "description": "Ingredient calibration factor (Number of ms per fluid oz)", "address": 64, "boardName": "Nozzle", "boardId": 560, "paramType": "WORD", "defaultParam": "2000", "currentValue": "2552", "newValue": null, "minParam": "100", "maxParam": "10000", "machineId": 133, "isPrivate": true}, {"id": 83988, "name": "Calibration Factor - Soda Bib", "macro": "EEP_SODA_CAL_VAL", "description": "Ingredient calibration factor (Number of ms per fluid oz)", "address": 80, "boardName": "Nozzle", "boardId": 560, "paramType": "WORD", "defaultParam": "2000", "currentValue": "591", "newValue": null, "minParam": "100", "maxParam": "10000", "machineId": 133, "isPrivate": true}, {"id": 83989, "name": "Calibration Factor - Water Bib", "macro": "EEP_WATER_CAL_VAL", "description": "Ingredient calibration factor (Number of ms per fluid oz)", "address": 96, "boardName": "Nozzle", "boardId": 560, "paramType": "WORD", "defaultParam": "2000", "currentValue": "555", "newValue": null, "minParam": "100", "maxParam": "10000", "machineId": 133, "isPrivate": true}, {"id": 83990, "name": "Dispensing Calibration Factor - A1", "macro": "EEP_INGR_CALIBRATION_FACTOR_0", "description": "Ingredient calibration factor (Number of pulses per fluid oz)", "address": 384, "boardName": "Pump Board 7", "boardId": 7, "paramType": "DWORD", "defaultParam": "48600", "currentValue": "58320", "newValue": "48600", "minParam": "9720", "maxParam": "194400", "machineId": 133, "isPrivate": true}, {"id": 83991, "name": "Dispensing Calibration Factor - A2", "macro": "EEP_INGR_CALIBRATION_FACTOR_2", "description": "Ingredient calibration factor (Number of pulses per fluid oz)", "address": 392, "boardName": "Pump Board 7", "boardId": 7, "paramType": "DWORD", "defaultParam": "48600", "currentValue": "48600", "newValue": "48600", "minParam": "9720", "maxParam": "194400", "machineId": 133, "isPrivate": true}, {"id": 83992, "name": "Dispensing Calibration Factor - A3", "macro": "EEP_INGR_CALIBRATION_FACTOR_0", "description": "Ingredient calibration factor (Number of pulses per fluid oz)", "address": 384, "boardName": "Pump Board 8", "boardId": 8, "paramType": "DWORD", "defaultParam": "48600", "currentValue": "48600", "newValue": "48600", "minParam": "9720", "maxParam": "194400", "machineId": 133, "isPrivate": true}, {"id": 83993, "name": "Dispensing Calibration Factor - A4", "macro": "EEP_INGR_CALIBRATION_FACTOR_2", "description": "Ingredient calibration factor (Number of pulses per fluid oz)", "address": 392, "boardName": "Pump Board 8", "boardId": 8, "paramType": "DWORD", "defaultParam": "48600", "currentValue": "48600", "newValue": "48600", "minParam": "9720", "maxParam": "194400", "machineId": 133, "isPrivate": true}, {"id": 83994, "name": "Dispensing Calibration Factor - A5", "macro": "EEP_INGR_CALIBRATION_FACTOR_0", "description": "Ingredient calibration factor (Number of pulses per fluid oz)", "address": 384, "boardName": "Pump Board 9", "boardId": 9, "paramType": "DWORD", "defaultParam": "48600", "currentValue": "48600", "newValue": "48600", "minParam": "9720", "maxParam": "194400", "machineId": 133, "isPrivate": true}, {"id": 83995, "name": "Dispensing Calibration Factor - A6", "macro": "EEP_INGR_CALIBRATION_FACTOR_2", "description": "Ingredient calibration factor (Number of pulses per fluid oz)", "address": 392, "boardName": "Pump Board 9", "boardId": 9, "paramType": "DWORD", "defaultParam": "48600", "currentValue": "48600", "newValue": "0", "minParam": "9720", "maxParam": "194400", "machineId": 133, "isPrivate": true}, {"id": 83996, "name": "Dispensing Calibration Factor - B1", "macro": "EEP_INGR_CALIBRATION_FACTOR_1", "description": "Ingredient calibration factor (Number of pulses per fluid oz)", "address": 388, "boardName": "Pump Board 7", "boardId": 7, "paramType": "DWORD", "defaultParam": "48600", "currentValue": "58320", "newValue": "48600", "minParam": "9720", "maxParam": "194400", "machineId": 133, "isPrivate": true}, {"id": 83997, "name": "Dispensing Calibration Factor - B2", "macro": "EEP_INGR_CALIBRATION_FACTOR_3", "description": "Ingredient calibration factor (Number of pulses per fluid oz)", "address": 396, "boardName": "Pump Board 7", "boardId": 7, "paramType": "DWORD", "defaultParam": "48600", "currentValue": "48600", "newValue": "48600", "minParam": "9720", "maxParam": "194400", "machineId": 133, "isPrivate": true}, {"id": 83998, "name": "Dispensing Calibration Factor - B3", "macro": "EEP_INGR_CALIBRATION_FACTOR_1", "description": "Ingredient calibration factor (Number of pulses per fluid oz)", "address": 388, "boardName": "Pump Board 8", "boardId": 8, "paramType": "DWORD", "defaultParam": "48600", "currentValue": "48600", "newValue": "48600", "minParam": "9720", "maxParam": "194400", "machineId": 133, "isPrivate": true}, {"id": 83999, "name": "Dispensing Calibration Factor - B4", "macro": "EEP_INGR_CALIBRATION_FACTOR_3", "description": "Ingredient calibration factor (Number of pulses per fluid oz)", "address": 396, "boardName": "Pump Board 8", "boardId": 8, "paramType": "DWORD", "defaultParam": "48600", "currentValue": "48600", "newValue": "48600", "minParam": "9720", "maxParam": "194400", "machineId": 133, "isPrivate": true}, {"id": 84000, "name": "Dispensing Calibration Factor - B5", "macro": "EEP_INGR_CALIBRATION_FACTOR_1", "description": "Ingredient calibration factor (Number of pulses per fluid oz)", "address": 388, "boardName": "Pump Board 9", "boardId": 9, "paramType": "DWORD", "defaultParam": "48600", "currentValue": "58320", "newValue": "48600", "minParam": "9720", "maxParam": "194400", "machineId": 133, "isPrivate": true}, {"id": 84001, "name": "Dispensing Calibration Factor - B6", "macro": "EEP_INGR_CALIBRATION_FACTOR_3", "description": "Ingredient calibration factor (Number of pulses per fluid oz)", "address": 396, "boardName": "Pump Board 9", "boardId": 9, "paramType": "DWORD", "defaultParam": "48600", "currentValue": "48600", "newValue": "0", "minParam": "9720", "maxParam": "194400", "machineId": 133, "isPrivate": true}, {"id": 84002, "name": "Dispensing Calibration Factor - C1", "macro": "EEP_INGR_CALIBRATION_FACTOR_0", "description": "Ingredient calibration factor (Number of pulses per fluid oz)", "address": 384, "boardName": "Pump Board 0", "boardId": 0, "paramType": "DWORD", "defaultParam": "24300", "currentValue": "27760", "newValue": "48600", "minParam": "9720", "maxParam": "194400", "machineId": 133, "isPrivate": true}, {"id": 84003, "name": "Dispensing Calibration Factor - C2", "macro": "EEP_INGR_CALIBRATION_FACTOR_0", "description": "Ingredient calibration factor (Number of pulses per fluid oz)", "address": 384, "boardName": "Pump Board 1", "boardId": 1, "paramType": "DWORD", "defaultParam": "48600", "currentValue": "48600", "newValue": "48600", "minParam": "9720", "maxParam": "194400", "machineId": 133, "isPrivate": true}, {"id": 84004, "name": "Dispensing Calibration Factor - C3", "macro": "EEP_INGR_CALIBRATION_FACTOR_0", "description": "Ingredient calibration factor (Number of pulses per fluid oz)", "address": 384, "boardName": "Pump Board 2", "boardId": 2, "paramType": "DWORD", "defaultParam": "48600", "currentValue": "48600", "newValue": "48600", "minParam": "9720", "maxParam": "194400", "machineId": 133, "isPrivate": true}, {"id": 84005, "name": "Dispensing Calibration Factor - C4", "macro": "EEP_INGR_CALIBRATION_FACTOR_0", "description": "Ingredient calibration factor (Number of pulses per fluid oz)", "address": 384, "boardName": "Pump Board 3", "boardId": 3, "paramType": "DWORD", "defaultParam": "48600", "currentValue": "54000", "newValue": "48600", "minParam": "9720", "maxParam": "194400", "machineId": 133, "isPrivate": true}, {"id": 84006, "name": "Dispensing Calibration Factor - C5", "macro": "EEP_INGR_CALIBRATION_FACTOR_0", "description": "Ingredient calibration factor (Number of pulses per fluid oz)", "address": 384, "boardName": "Pump Board 4", "boardId": 4, "paramType": "DWORD", "defaultParam": "48600", "currentValue": "57160", "newValue": "48600", "minParam": "9720", "maxParam": "194400", "machineId": 133, "isPrivate": true}, {"id": 84007, "name": "Dispensing Calibration Factor - C6", "macro": "EEP_INGR_CALIBRATION_FACTOR_0", "description": "Ingredient calibration factor (Number of pulses per fluid oz)", "address": 384, "boardName": "Pump Board 5", "boardId": 5, "paramType": "DWORD", "defaultParam": "48600", "currentValue": "54000", "newValue": "48600", "minParam": "9720", "maxParam": "194400", "machineId": 133, "isPrivate": true}, {"id": 84008, "name": "Dispensing Calibration Factor - D1", "macro": "EEP_INGR_CALIBRATION_FACTOR_1", "description": "Ingredient calibration factor (Number of pulses per fluid oz)", "address": 388, "boardName": "Pump Board 0", "boardId": 0, "paramType": "DWORD", "defaultParam": "24300", "currentValue": "32400", "newValue": "48600", "minParam": "9720", "maxParam": "194400", "machineId": 133, "isPrivate": true}, {"id": 84009, "name": "Dispensing Calibration Factor - D2", "macro": "EEP_INGR_CALIBRATION_FACTOR_1", "description": "Ingredient calibration factor (Number of pulses per fluid oz)", "address": 388, "boardName": "Pump Board 1", "boardId": 1, "paramType": "DWORD", "defaultParam": "48600", "currentValue": "48600", "newValue": "48600", "minParam": "9720", "maxParam": "194400", "machineId": 133, "isPrivate": true}, {"id": 84010, "name": "Dispensing Calibration Factor - D3", "macro": "EEP_INGR_CALIBRATION_FACTOR_1", "description": "Ingredient calibration factor (Number of pulses per fluid oz)", "address": 388, "boardName": "Pump Board 2", "boardId": 2, "paramType": "DWORD", "defaultParam": "48600", "currentValue": "48600", "newValue": "48600", "minParam": "9720", "maxParam": "194400", "machineId": 133, "isPrivate": true}, {"id": 84011, "name": "Dispensing Calibration Factor - D4", "macro": "EEP_INGR_CALIBRATION_FACTOR_1", "description": "Ingredient calibration factor (Number of pulses per fluid oz)", "address": 388, "boardName": "Pump Board 3", "boardId": 3, "paramType": "DWORD", "defaultParam": "48600", "currentValue": "54000", "newValue": "48600", "minParam": "9720", "maxParam": "194400", "machineId": 133, "isPrivate": true}, {"id": 84012, "name": "Dispensing Calibration Factor - D5", "macro": "EEP_INGR_CALIBRATION_FACTOR_1", "description": "Ingredient calibration factor (Number of pulses per fluid oz)", "address": 388, "boardName": "Pump Board 4", "boardId": 4, "paramType": "DWORD", "defaultParam": "48600", "currentValue": "48600", "newValue": "48600", "minParam": "9720", "maxParam": "194400", "machineId": 133, "isPrivate": true}, {"id": 84013, "name": "Dispensing Calibration Factor - D6", "macro": "EEP_INGR_CALIBRATION_FACTOR_1", "description": "Ingredient calibration factor (Number of pulses per fluid oz)", "address": 388, "boardName": "Pump Board 5", "boardId": 5, "paramType": "DWORD", "defaultParam": "48600", "currentValue": "48600", "newValue": "48600", "minParam": "9720", "maxParam": "194400", "machineId": 133, "isPrivate": true}, {"id": 84014, "name": "Dispensing Calibration Factor - E1", "macro": "EEP_INGR_CALIBRATION_FACTOR_2", "description": "Ingredient calibration factor (Number of pulses per fluid oz)", "address": 392, "boardName": "Pump Board 0", "boardId": 0, "paramType": "DWORD", "defaultParam": "24300", "currentValue": "31340", "newValue": "0", "minParam": "9720", "maxParam": "194400", "machineId": 133, "isPrivate": true}, {"id": 84015, "name": "Dispensing Calibration Factor - E2", "macro": "EEP_INGR_CALIBRATION_FACTOR_2", "description": "Ingredient calibration factor (Number of pulses per fluid oz)", "address": 392, "boardName": "Pump Board 1", "boardId": 1, "paramType": "DWORD", "defaultParam": "48600", "currentValue": "48600", "newValue": "48600", "minParam": "9720", "maxParam": "194400", "machineId": 133, "isPrivate": true}, {"id": 84016, "name": "Dispensing Calibration Factor - E3", "macro": "EEP_INGR_CALIBRATION_FACTOR_2", "description": "Ingredient calibration factor (Number of pulses per fluid oz)", "address": 392, "boardName": "Pump Board 2", "boardId": 2, "paramType": "DWORD", "defaultParam": "48600", "currentValue": "48600", "newValue": "48600", "minParam": "9720", "maxParam": "194400", "machineId": 133, "isPrivate": true}, {"id": 84017, "name": "Dispensing Calibration Factor - E4", "macro": "EEP_INGR_CALIBRATION_FACTOR_2", "description": "Ingredient calibration factor (Number of pulses per fluid oz)", "address": 392, "boardName": "Pump Board 3", "boardId": 3, "paramType": "DWORD", "defaultParam": "48600", "currentValue": "48600", "newValue": "48600", "minParam": "9720", "maxParam": "194400", "machineId": 133, "isPrivate": true}, {"id": 84018, "name": "Dispensing Calibration Factor - E5", "macro": "EEP_INGR_CALIBRATION_FACTOR_2", "description": "Ingredient calibration factor (Number of pulses per fluid oz)", "address": 392, "boardName": "Pump Board 4", "boardId": 4, "paramType": "DWORD", "defaultParam": "48600", "currentValue": "54000", "newValue": "48600", "minParam": "9720", "maxParam": "194400", "machineId": 133, "isPrivate": true}, {"id": 84019, "name": "Dispensing Calibration Factor - E6", "macro": "EEP_INGR_CALIBRATION_FACTOR_2", "description": "Ingredient calibration factor (Number of pulses per fluid oz)", "address": 392, "boardName": "Pump Board 5", "boardId": 5, "paramType": "DWORD", "defaultParam": "48600", "currentValue": "54000", "newValue": "48600", "minParam": "9720", "maxParam": "194400", "machineId": 133, "isPrivate": true}, {"id": 84020, "name": "Dispensing Calibration Factor - F1", "macro": "EEP_INGR_CALIBRATION_FACTOR_3", "description": "Ingredient calibration factor (Number of pulses per fluid oz)", "address": 396, "boardName": "Pump Board 0", "boardId": 0, "paramType": "DWORD", "defaultParam": "24300", "currentValue": "36000", "newValue": "48600", "minParam": "9720", "maxParam": "194400", "machineId": 133, "isPrivate": true}, {"id": 84021, "name": "Dispensing Calibration Factor - F2", "macro": "EEP_INGR_CALIBRATION_FACTOR_3", "description": "Ingredient calibration factor (Number of pulses per fluid oz)", "address": 396, "boardName": "Pump Board 1", "boardId": 1, "paramType": "DWORD", "defaultParam": "48600", "currentValue": "54000", "newValue": "48600", "minParam": "9720", "maxParam": "194400", "machineId": 133, "isPrivate": true}, {"id": 84022, "name": "Dispensing Calibration Factor - F3", "macro": "EEP_INGR_CALIBRATION_FACTOR_3", "description": "Ingredient calibration factor (Number of pulses per fluid oz)", "address": 396, "boardName": "Pump Board 2", "boardId": 2, "paramType": "DWORD", "defaultParam": "48600", "currentValue": "48600", "newValue": "48600", "minParam": "9720", "maxParam": "194400", "machineId": 133, "isPrivate": true}, {"id": 84023, "name": "Dispensing Calibration Factor - F4", "macro": "EEP_INGR_CALIBRATION_FACTOR_3", "description": "Ingredient calibration factor (Number of pulses per fluid oz)", "address": 396, "boardName": "Pump Board 3", "boardId": 3, "paramType": "DWORD", "defaultParam": "48600", "currentValue": "48600", "newValue": "48600", "minParam": "9720", "maxParam": "194400", "machineId": 133, "isPrivate": true}, {"id": 84024, "name": "Dispensing Calibration Factor - F5", "macro": "EEP_INGR_CALIBRATION_FACTOR_3", "description": "Ingredient calibration factor (Number of pulses per fluid oz)", "address": 396, "boardName": "Pump Board 4", "boardId": 4, "paramType": "DWORD", "defaultParam": "48600", "currentValue": "54000", "newValue": "48600", "minParam": "9720", "maxParam": "194400", "machineId": 133, "isPrivate": true}, {"id": 84025, "name": "Dispensing Calibration Factor - F6", "macro": "EEP_INGR_CALIBRATION_FACTOR_3", "description": "Ingredient calibration factor (Number of pulses per fluid oz)", "address": 396, "boardName": "Pump Board 5", "boardId": 5, "paramType": "DWORD", "defaultParam": "48600", "currentValue": "81000", "newValue": "48600", "minParam": "9720", "maxParam": "194400", "machineId": 133, "isPrivate": true}, {"id": 84026, "name": "Dispensing Calibration Factor - T1", "macro": "EEP_INGR_CALIBRATION_FACTOR_0", "description": "Ingredient calibration factor (Number of pulses per fluid oz)", "address": 384, "boardName": "Pump Board 13", "boardId": 13, "paramType": "DWORD", "defaultParam": "48600", "currentValue": "64800", "newValue": null, "minParam": "9720", "maxParam": "194400", "machineId": 133, "isPrivate": true}, {"id": 84027, "name": "Dispensing Calibration Factor - T2", "macro": "EEP_INGR_CALIBRATION_FACTOR_1", "description": "Ingredient calibration factor (Number of pulses per fluid oz)", "address": 388, "boardName": "Pump Board 13", "boardId": 13, "paramType": "DWORD", "defaultParam": "48600", "currentValue": "60740", "newValue": null, "minParam": "9720", "maxParam": "194400", "machineId": 133, "isPrivate": true}, {"id": 84028, "name": "Dispensing Calibration Factor - T3", "macro": "EEP_INGR_CALIBRATION_FACTOR_2", "description": "Ingredient calibration factor (Number of pulses per fluid oz)", "address": 392, "boardName": "Pump Board 13", "boardId": 13, "paramType": "DWORD", "defaultParam": "48600", "currentValue": "60740", "newValue": null, "minParam": "9720", "maxParam": "194400", "machineId": 133, "isPrivate": true}, {"id": 84029, "name": "Dispensing Calibration Factor - T4", "macro": "EEP_INGR_CALIBRATION_FACTOR_3", "description": "Ingredient calibration factor (Number of pulses per fluid oz)", "address": 396, "boardName": "Pump Board 13", "boardId": 13, "paramType": "DWORD", "defaultParam": "48600", "currentValue": "60740", "newValue": null, "minParam": "9720", "maxParam": "194400", "machineId": 133, "isPrivate": true}, {"id": 84030, "name": "Dispensing Calibration Factor - T5", "macro": "EEP_INGR_CALIBRATION_FACTOR_0", "description": "Ingredient calibration factor (Number of pulses per fluid oz)", "address": 384, "boardName": "Pump Board 14", "boardId": 14, "paramType": "DWORD", "defaultParam": "48600", "currentValue": "4294967295", "newValue": null, "minParam": "9720", "maxParam": "194400", "machineId": 133, "isPrivate": true}, {"id": 84031, "name": "Dispensing Calibration Factor - T6", "macro": "EEP_INGR_CALIBRATION_FACTOR_1", "description": "Ingredient calibration factor (Number of pulses per fluid oz)", "address": 388, "boardName": "Pump Board 14", "boardId": 14, "paramType": "DWORD", "defaultParam": "48600", "currentValue": "57160", "newValue": null, "minParam": "9720", "maxParam": "194400", "machineId": 133, "isPrivate": true}, {"id": 84032, "name": "Dispensing Calibration Factor - T7", "macro": "EEP_INGR_CALIBRATION_FACTOR_2", "description": "Ingredient calibration factor (Number of pulses per fluid oz)", "address": 392, "boardName": "Pump Board 14", "boardId": 14, "paramType": "DWORD", "defaultParam": "48600", "currentValue": "4294967295", "newValue": null, "minParam": "9720", "maxParam": "194400", "machineId": 133, "isPrivate": true}, {"id": 84033, "name": "Dispensing Calibration Factor - T8", "macro": "EEP_INGR_CALIBRATION_FACTOR_3", "description": "Ingredient calibration factor (Number of pulses per fluid oz)", "address": 396, "boardName": "Pump Board 14", "boardId": 14, "paramType": "DWORD", "defaultParam": "48600", "currentValue": "4294967295", "newValue": null, "minParam": "9720", "maxParam": "194400", "machineId": 133, "isPrivate": true}, {"id": 84034, "name": "First Dispense Overpour Quantity - C1, D1, E1, F1", "macro": "EEP_STARTUP_QTY", "description": "Quantity (expressed in 1/20 fl. oz. increments) to overprime this board's ingredients beyond their priming lengths for the first pour of the day", "address": 305, "boardName": "Pump Board 0", "boardId": 0, "paramType": "BYTE", "defaultParam": "10", "currentValue": "10", "newValue": null, "minParam": "0", "maxParam": "100", "machineId": 133, "isPrivate": false}, {"id": 84035, "name": "Global Ingredient Sensor Active Threshold", "macro": "EEP_LEVEL_THRESHOLD_GLOABL", "description": "Ingredient level (expressed in 1/20 fl. oz. increments) below which runout detection becomes active for ALL ingredients", "address": 338, "boardName": "Master", "boardId": 65535, "paramType": "WORD", "defaultParam": "100", "currentValue": "100", "newValue": null, "minParam": "10", "maxParam": "3500", "machineId": 133, "isPrivate": false}, {"id": 84036, "name": "Ingredient Line Status - A1", "macro": "EEP_INGR_LINE_STATUS_0", "description": "Bitfield for current ingredient prime status of pumped ingredients", "address": 432, "boardName": "Pump Board 7", "boardId": 7, "paramType": "BYTE", "defaultParam": "0", "currentValue": "4", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 133, "isPrivate": true}, {"id": 84037, "name": "Ingredient Line Status - A2", "macro": "EEP_INGR_LINE_STATUS_2", "description": "Bitfield for current ingredient prime status of pumped ingredients", "address": 434, "boardName": "Pump Board 7", "boardId": 7, "paramType": "BYTE", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 133, "isPrivate": true}, {"id": 84038, "name": "Ingredient Line Status - A3", "macro": "EEP_INGR_LINE_STATUS_0", "description": "Bitfield for current ingredient prime status of pumped ingredients", "address": 432, "boardName": "Pump Board 8", "boardId": 8, "paramType": "BYTE", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 133, "isPrivate": true}, {"id": 84039, "name": "Ingredient Line Status - A4", "macro": "EEP_INGR_LINE_STATUS_2", "description": "Bitfield for current ingredient prime status of pumped ingredients", "address": 434, "boardName": "Pump Board 8", "boardId": 8, "paramType": "BYTE", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 133, "isPrivate": true}, {"id": 84040, "name": "Ingredient Line Status - A5", "macro": "EEP_INGR_LINE_STATUS_0", "description": "Bitfield for current ingredient prime status of pumped ingredients", "address": 432, "boardName": "Pump Board 9", "boardId": 9, "paramType": "BYTE", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 133, "isPrivate": true}, {"id": 84041, "name": "Ingredient Line Status - B2", "macro": "EEP_INGR_LINE_STATUS_3", "description": "Bitfield for current ingredient prime status of pumped ingredients", "address": 435, "boardName": "Pump Board 7", "boardId": 7, "paramType": "BYTE", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 133, "isPrivate": true}, {"id": 84042, "name": "Ingredient Line Status - B3", "macro": "EEP_INGR_LINE_STATUS_1", "description": "Bitfield for current ingredient prime status of pumped ingredients", "address": 433, "boardName": "Pump Board 8", "boardId": 8, "paramType": "BYTE", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 133, "isPrivate": true}, {"id": 84043, "name": "Ingredient Line Status - B4", "macro": "EEP_INGR_LINE_STATUS_3", "description": "Bitfield for current ingredient prime status of pumped ingredients", "address": 435, "boardName": "Pump Board 8", "boardId": 8, "paramType": "BYTE", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 133, "isPrivate": true}, {"id": 84044, "name": "Ingredient Line Status - B5", "macro": "EEP_INGR_LINE_STATUS_1", "description": "Bitfield for current ingredient prime status of pumped ingredients", "address": 433, "boardName": "Pump Board 9", "boardId": 9, "paramType": "BYTE", "defaultParam": "0", "currentValue": "4", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 133, "isPrivate": true}, {"id": 84045, "name": "Ingredient Line Status - B6", "macro": "EEP_INGR_LINE_STATUS_3", "description": "Bitfield for current ingredient prime status of pumped ingredients", "address": 435, "boardName": "Pump Board 9", "boardId": 9, "paramType": "BYTE", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 133, "isPrivate": true}, {"id": 84046, "name": "Ingredient Line Status - C1", "macro": "EEP_INGR_LINE_STATUS_0", "description": "Bitfield for current ingredient prime status of pumped ingredients", "address": 432, "boardName": "Pump Board 0", "boardId": 0, "paramType": "BYTE", "defaultParam": "0", "currentValue": "4", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 133, "isPrivate": true}, {"id": 84047, "name": "Ingredient Line Status - C2", "macro": "EEP_INGR_LINE_STATUS_0", "description": "Bitfield for current ingredient prime status of pumped ingredients", "address": 432, "boardName": "Pump Board 1", "boardId": 1, "paramType": "BYTE", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 133, "isPrivate": true}, {"id": 84048, "name": "Ingredient Line Status - C3", "macro": "EEP_INGR_LINE_STATUS_0", "description": "Bitfield for current ingredient prime status of pumped ingredients", "address": 432, "boardName": "Pump Board 2", "boardId": 2, "paramType": "BYTE", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 133, "isPrivate": true}, {"id": 84049, "name": "Ingredient Line Status - C4", "macro": "EEP_INGR_LINE_STATUS_0", "description": "Bitfield for current ingredient prime status of pumped ingredients", "address": 432, "boardName": "Pump Board 3", "boardId": 3, "paramType": "BYTE", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 133, "isPrivate": true}, {"id": 84050, "name": "Ingredient Line Status - C5", "macro": "EEP_INGR_LINE_STATUS_0", "description": "Bitfield for current ingredient prime status of pumped ingredients", "address": 432, "boardName": "Pump Board 4", "boardId": 4, "paramType": "BYTE", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 133, "isPrivate": true}, {"id": 84051, "name": "Ingredient Line Status - C6", "macro": "EEP_INGR_LINE_STATUS_0", "description": "Bitfield for current ingredient prime status of pumped ingredients", "address": 432, "boardName": "Pump Board 5", "boardId": 5, "paramType": "BYTE", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 133, "isPrivate": true}, {"id": 84052, "name": "Ingredient Line Status - D1", "macro": "EEP_INGR_LINE_STATUS_1", "description": "Bitfield for current ingredient prime status of pumped ingredients", "address": 433, "boardName": "Pump Board 0", "boardId": 0, "paramType": "BYTE", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 133, "isPrivate": true}, {"id": 84053, "name": "Ingredient Line Status - D2", "macro": "EEP_INGR_LINE_STATUS_1", "description": "Bitfield for current ingredient prime status of pumped ingredients", "address": 433, "boardName": "Pump Board 1", "boardId": 1, "paramType": "BYTE", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 133, "isPrivate": true}, {"id": 84054, "name": "Ingredient Line Status - D3", "macro": "EEP_INGR_LINE_STATUS_1", "description": "Bitfield for current ingredient prime status of pumped ingredients", "address": 433, "boardName": "Pump Board 2", "boardId": 2, "paramType": "BYTE", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 133, "isPrivate": true}, {"id": 84055, "name": "Ingredient Line Status - D4", "macro": "EEP_INGR_LINE_STATUS_1", "description": "Bitfield for current ingredient prime status of pumped ingredients", "address": 433, "boardName": "Pump Board 3", "boardId": 3, "paramType": "BYTE", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 133, "isPrivate": true}, {"id": 84056, "name": "Ingredient Line Status - D5", "macro": "EEP_INGR_LINE_STATUS_1", "description": "Bitfield for current ingredient prime status of pumped ingredients", "address": 433, "boardName": "Pump Board 4", "boardId": 4, "paramType": "BYTE", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 133, "isPrivate": true}, {"id": 84057, "name": "Ingredient Line Status - D6", "macro": "EEP_INGR_LINE_STATUS_1", "description": "Bitfield for current ingredient prime status of pumped ingredients", "address": 433, "boardName": "Pump Board 5", "boardId": 5, "paramType": "BYTE", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 133, "isPrivate": true}, {"id": 84058, "name": "Ingredient Line Status - E1", "macro": "EEP_INGR_LINE_STATUS_2", "description": "Bitfield for current ingredient prime status of pumped ingredients", "address": 434, "boardName": "Pump Board 0", "boardId": 0, "paramType": "BYTE", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 133, "isPrivate": true}, {"id": 84059, "name": "Ingredient Line Status - E2", "macro": "EEP_INGR_LINE_STATUS_2", "description": "Bitfield for current ingredient prime status of pumped ingredients", "address": 434, "boardName": "Pump Board 1", "boardId": 1, "paramType": "BYTE", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 133, "isPrivate": true}, {"id": 84060, "name": "Ingredient Line Status - E3", "macro": "EEP_INGR_LINE_STATUS_2", "description": "Bitfield for current ingredient prime status of pumped ingredients", "address": 434, "boardName": "Pump Board 2", "boardId": 2, "paramType": "BYTE", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 133, "isPrivate": true}, {"id": 84061, "name": "Ingredient Line Status - E4", "macro": "EEP_INGR_LINE_STATUS_2", "description": "Bitfield for current ingredient prime status of pumped ingredients", "address": 434, "boardName": "Pump Board 3", "boardId": 3, "paramType": "BYTE", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 133, "isPrivate": true}, {"id": 84062, "name": "Ingredient Line Status - E5", "macro": "EEP_INGR_LINE_STATUS_2", "description": "Bitfield for current ingredient prime status of pumped ingredients", "address": 434, "boardName": "Pump Board 4", "boardId": 4, "paramType": "BYTE", "defaultParam": "0", "currentValue": "4", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 133, "isPrivate": true}, {"id": 84063, "name": "Ingredient Line Status - E6", "macro": "EEP_INGR_LINE_STATUS_2", "description": "Bitfield for current ingredient prime status of pumped ingredients", "address": 434, "boardName": "Pump Board 5", "boardId": 5, "paramType": "BYTE", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 133, "isPrivate": true}, {"id": 84064, "name": "Ingredient Line Status - F1", "macro": "EEP_INGR_LINE_STATUS_3", "description": "Bitfield for current ingredient prime status of pumped ingredients", "address": 435, "boardName": "Pump Board 0", "boardId": 0, "paramType": "BYTE", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 133, "isPrivate": true}, {"id": 84065, "name": "Ingredient Line Status - F2", "macro": "EEP_INGR_LINE_STATUS_3", "description": "Bitfield for current ingredient prime status of pumped ingredients", "address": 435, "boardName": "Pump Board 1", "boardId": 1, "paramType": "BYTE", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 133, "isPrivate": true}, {"id": 84066, "name": "Ingredient Line Status - F3", "macro": "EEP_INGR_LINE_STATUS_3", "description": "Bitfield for current ingredient prime status of pumped ingredients", "address": 435, "boardName": "Pump Board 2", "boardId": 2, "paramType": "BYTE", "defaultParam": "0", "currentValue": "4", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 133, "isPrivate": true}, {"id": 84067, "name": "Ingredient Line Status - F4", "macro": "EEP_INGR_LINE_STATUS_3", "description": "Bitfield for current ingredient prime status of pumped ingredients", "address": 435, "boardName": "Pump Board 3", "boardId": 3, "paramType": "BYTE", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 133, "isPrivate": true}, {"id": 84068, "name": "Ingredient Line Status - F5", "macro": "EEP_INGR_LINE_STATUS_3", "description": "Bitfield for current ingredient prime status of pumped ingredients", "address": 435, "boardName": "Pump Board 4", "boardId": 4, "paramType": "BYTE", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 133, "isPrivate": true}, {"id": 84069, "name": "Ingredient Line Status - F6", "macro": "EEP_INGR_LINE_STATUS_3", "description": "Bitfield for current ingredient prime status of pumped ingredients", "address": 435, "boardName": "Pump Board 5", "boardId": 5, "paramType": "BYTE", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 133, "isPrivate": true}, {"id": 84070, "name": "Ingredient Line Status - T1", "macro": "EEP_INGR_LINE_STATUS_0", "description": "Bitfield for current ingredient prime status of pumped ingredients", "address": 432, "boardName": "Pump Board 13", "boardId": 13, "paramType": "BYTE", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 133, "isPrivate": true}, {"id": 84071, "name": "Ingredient Line Status - T2", "macro": "EEP_INGR_LINE_STATUS_1", "description": "Bitfield for current ingredient prime status of pumped ingredients", "address": 433, "boardName": "Pump Board 13", "boardId": 13, "paramType": "BYTE", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 133, "isPrivate": true}, {"id": 84072, "name": "Ingredient Line Status - T3", "macro": "EEP_INGR_LINE_STATUS_2", "description": "Bitfield for current ingredient prime status of pumped ingredients", "address": 434, "boardName": "Pump Board 13", "boardId": 13, "paramType": "BYTE", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 133, "isPrivate": true}, {"id": 84073, "name": "Ingredient Line Status - T4", "macro": "EEP_INGR_LINE_STATUS_3", "description": "Bitfield for current ingredient prime status of pumped ingredients", "address": 435, "boardName": "Pump Board 13", "boardId": 13, "paramType": "BYTE", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 133, "isPrivate": true}, {"id": 84074, "name": "Ingredient Line Status - T5", "macro": "EEP_INGR_LINE_STATUS_0", "description": "Bitfield for current ingredient prime status of pumped ingredients", "address": 432, "boardName": "Pump Board 14", "boardId": 14, "paramType": "BYTE", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 133, "isPrivate": true}, {"id": 84075, "name": "Ingredient Line Status - T6", "macro": "EEP_INGR_LINE_STATUS_1", "description": "Bitfield for current ingredient prime status of pumped ingredients", "address": 433, "boardName": "Pump Board 14", "boardId": 14, "paramType": "BYTE", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 133, "isPrivate": true}, {"id": 84076, "name": "Ingredient Line Status - T7", "macro": "EEP_INGR_LINE_STATUS_2", "description": "Bitfield for current ingredient prime status of pumped ingredients", "address": 434, "boardName": "Pump Board 14", "boardId": 14, "paramType": "BYTE", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 133, "isPrivate": true}, {"id": 84077, "name": "Ingredient Line Status - T8", "macro": "EEP_INGR_LINE_STATUS_3", "description": "Bitfield for current ingredient prime status of pumped ingredients", "address": 435, "boardName": "Pump Board 14", "boardId": 14, "paramType": "BYTE", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 133, "isPrivate": true}, {"id": 84078, "name": "Ingredient Line Status -A6", "macro": "EEP_INGR_LINE_STATUS_2", "description": "Bitfield for current ingredient prime status of pumped ingredients", "address": 434, "boardName": "Pump Board 9", "boardId": 9, "paramType": "BYTE", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 133, "isPrivate": true}, {"id": 84079, "name": "Ingredient Line Status -B1", "macro": "EEP_INGR_LINE_STATUS_1", "description": "Bitfield for current ingredient prime status of pumped ingredients", "address": 433, "boardName": "Pump Board 7", "boardId": 7, "paramType": "BYTE", "defaultParam": "0", "currentValue": "4", "newValue": null, "minParam": "0", "maxParam": "8", "machineId": 133, "isPrivate": true}, {"id": 84080, "name": "Ingredient Line Status Enable (C1, D1, E1, F1)", "macro": "EEP_INGR_LINE_STATUS_EN", "description": "Enable ingredient prime status of pumped ingredients", "address": 436, "boardName": "Pump Board 0", "boardId": 0, "paramType": "BOOLEAN", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 133, "isPrivate": false}, {"id": 84081, "name": "Ingredient Mixing Enable - A1", "macro": "EEP_MIXING_ENABLE_28", "description": "Enable ingredient mixing for A1", "address": 124, "boardName": "Master", "boardId": 65535, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 133, "isPrivate": false}, {"id": 84082, "name": "Ingredient Mixing Enable - A2", "macro": "EEP_MIXING_ENABLE_30", "description": "Enable ingredient mixing for A2", "address": 126, "boardName": "Master", "boardId": 65535, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 133, "isPrivate": false}, {"id": 84083, "name": "Ingredient Mixing Enable - A3", "macro": "EEP_MIXING_ENABLE_32", "description": "Enable ingredient mixing for A3", "address": 128, "boardName": "Master", "boardId": 65535, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 133, "isPrivate": false}, {"id": 84084, "name": "Ingredient Mixing Enable - A4", "macro": "EEP_MIXING_ENABLE_34", "description": "Enable ingredient mixing for A4", "address": 130, "boardName": "Master", "boardId": 65535, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 133, "isPrivate": false}, {"id": 84085, "name": "Ingredient Mixing Enable - A5", "macro": "EEP_MIXING_ENABLE_36", "description": "Enable ingredient mixing for A5", "address": 132, "boardName": "Master", "boardId": 65535, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 133, "isPrivate": false}, {"id": 84086, "name": "Ingredient Mixing Enable - A6", "macro": "EEP_MIXING_ENABLE_38", "description": "Enable ingredient mixing for A6", "address": 134, "boardName": "Master", "boardId": 65535, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 133, "isPrivate": false}, {"id": 84087, "name": "Ingredient Mixing Enable - B1", "macro": "EEP_MIXING_ENABLE_29", "description": "Enable ingredient mixing for B1", "address": 125, "boardName": "Master", "boardId": 65535, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 133, "isPrivate": false}, {"id": 84088, "name": "Ingredient Mixing Enable - B2", "macro": "EEP_MIXING_ENABLE_31", "description": "Enable ingredient mixing for B2", "address": 127, "boardName": "Master", "boardId": 65535, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 133, "isPrivate": false}, {"id": 84089, "name": "Ingredient Mixing Enable - B3", "macro": "EEP_MIXING_ENABLE_33", "description": "Enable ingredient mixing for B3", "address": 129, "boardName": "Master", "boardId": 65535, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 133, "isPrivate": false}, {"id": 84090, "name": "Ingredient Mixing Enable - B4", "macro": "EEP_MIXING_ENABLE_35", "description": "Enable ingredient mixing for B4", "address": 131, "boardName": "Master", "boardId": 65535, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 133, "isPrivate": false}, {"id": 84091, "name": "Ingredient Mixing Enable - B5", "macro": "EEP_MIXING_ENABLE_37", "description": "Enable ingredient mixing for B5", "address": 133, "boardName": "Master", "boardId": 65535, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 133, "isPrivate": false}, {"id": 84092, "name": "Ingredient Mixing Enable - B6", "macro": "EEP_MIXING_ENABLE_39", "description": "Enable ingredient mixing for B6", "address": 135, "boardName": "Master", "boardId": 65535, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 133, "isPrivate": false}, {"id": 84093, "name": "Ingredient Mixing Enable - C1", "macro": "EEP_MIXING_ENABLE_0", "description": "Enable ingredient mixing for A1", "address": 96, "boardName": "Master", "boardId": 65535, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 133, "isPrivate": true}, {"id": 84094, "name": "Ingredient Mixing Enable - C2", "macro": "EEP_MIXING_ENABLE_4", "description": "Enable ingredient mixing for B1", "address": 100, "boardName": "Master", "boardId": 65535, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 133, "isPrivate": true}, {"id": 84095, "name": "Ingredient Mixing Enable - C3", "macro": "EEP_MIXING_ENABLE_8", "description": "Enable ingredient mixing for C1", "address": 104, "boardName": "Master", "boardId": 65535, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 133, "isPrivate": true}, {"id": 84096, "name": "Ingredient Mixing Enable - C4", "macro": "EEP_MIXING_ENABLE_12", "description": "Enable ingredient mixing for D1", "address": 108, "boardName": "Master", "boardId": 65535, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 133, "isPrivate": true}, {"id": 84097, "name": "Ingredient Mixing Enable - C5", "macro": "EEP_MIXING_ENABLE_16", "description": "Enable ingredient mixing for E1", "address": 112, "boardName": "Master", "boardId": 65535, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 133, "isPrivate": true}, {"id": 84098, "name": "Ingredient Mixing Enable - C6", "macro": "EEP_MIXING_ENABLE_20", "description": "Enable ingredient mixing for F1", "address": 116, "boardName": "Master", "boardId": 65535, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 133, "isPrivate": true}, {"id": 84099, "name": "Ingredient Mixing Enable - D1", "macro": "EEP_MIXING_ENABLE_1", "description": "Enable ingredient mixing for A2", "address": 97, "boardName": "Master", "boardId": 65535, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 133, "isPrivate": true}, {"id": 84100, "name": "Ingredient Mixing Enable - D2", "macro": "EEP_MIXING_ENABLE_5", "description": "Enable ingredient mixing for B2", "address": 101, "boardName": "Master", "boardId": 65535, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 133, "isPrivate": true}, {"id": 84101, "name": "Ingredient Mixing Enable - D3", "macro": "EEP_MIXING_ENABLE_9", "description": "Enable ingredient mixing for C2", "address": 105, "boardName": "Master", "boardId": 65535, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 133, "isPrivate": true}, {"id": 84102, "name": "Ingredient Mixing Enable - D4", "macro": "EEP_MIXING_ENABLE_13", "description": "Enable ingredient mixing for D2", "address": 109, "boardName": "Master", "boardId": 65535, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 133, "isPrivate": true}, {"id": 84103, "name": "Ingredient Mixing Enable - D5", "macro": "EEP_MIXING_ENABLE_17", "description": "Enable ingredient mixing for E2", "address": 113, "boardName": "Master", "boardId": 65535, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 133, "isPrivate": true}, {"id": 84104, "name": "Ingredient Mixing Enable - D6", "macro": "EEP_MIXING_ENABLE_21", "description": "Enable ingredient mixing for F2", "address": 117, "boardName": "Master", "boardId": 65535, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 133, "isPrivate": true}, {"id": 84105, "name": "Ingredient Mixing Enable - E1", "macro": "EEP_MIXING_ENABLE_2", "description": "Enable ingredient mixing for A3", "address": 98, "boardName": "Master", "boardId": 65535, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 133, "isPrivate": true}, {"id": 84106, "name": "Ingredient Mixing Enable - E2", "macro": "EEP_MIXING_ENABLE_6", "description": "Enable ingredient mixing for B3", "address": 102, "boardName": "Master", "boardId": 65535, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 133, "isPrivate": true}, {"id": 84107, "name": "Ingredient Mixing Enable - E3", "macro": "EEP_MIXING_ENABLE_10", "description": "Enable ingredient mixing for C3", "address": 106, "boardName": "Master", "boardId": 65535, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 133, "isPrivate": true}, {"id": 84108, "name": "Ingredient Mixing Enable - E4", "macro": "EEP_MIXING_ENABLE_14", "description": "Enable ingredient mixing for D3", "address": 110, "boardName": "Master", "boardId": 65535, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 133, "isPrivate": true}, {"id": 84109, "name": "Ingredient Mixing Enable - E5", "macro": "EEP_MIXING_ENABLE_18", "description": "Enable ingredient mixing for E3", "address": 114, "boardName": "Master", "boardId": 65535, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 133, "isPrivate": true}, {"id": 84110, "name": "Ingredient Mixing Enable - E6", "macro": "EEP_MIXING_ENABLE_22", "description": "Enable ingredient mixing for F3", "address": 118, "boardName": "Master", "boardId": 65535, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 133, "isPrivate": true}, {"id": 84111, "name": "Ingredient Mixing Enable - F1", "macro": "EEP_MIXING_ENABLE_3", "description": "Enable ingredient mixing for A4", "address": 99, "boardName": "Master", "boardId": 65535, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 133, "isPrivate": true}, {"id": 84112, "name": "Ingredient Mixing Enable - F2", "macro": "EEP_MIXING_ENABLE_7", "description": "Enable ingredient mixing for B4", "address": 103, "boardName": "Master", "boardId": 65535, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 133, "isPrivate": true}, {"id": 84113, "name": "Ingredient Mixing Enable - F3", "macro": "EEP_MIXING_ENABLE_11", "description": "Enable ingredient mixing for C4", "address": 107, "boardName": "Master", "boardId": 65535, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 133, "isPrivate": true}, {"id": 84114, "name": "Ingredient Mixing Enable - F4", "macro": "EEP_MIXING_ENABLE_15", "description": "Enable ingredient mixing for D4", "address": 111, "boardName": "Master", "boardId": 65535, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 133, "isPrivate": true}, {"id": 84115, "name": "Ingredient Mixing Enable - F5", "macro": "EEP_MIXING_ENABLE_19", "description": "Enable ingredient mixing for E4", "address": 115, "boardName": "Master", "boardId": 65535, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 133, "isPrivate": true}, {"id": 84116, "name": "Ingredient Mixing Enable - F6", "macro": "EEP_MIXING_ENABLE_23", "description": "Enable ingredient mixing for F4", "address": 119, "boardName": "Master", "boardId": 65535, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 133, "isPrivate": true}, {"id": 84117, "name": "Ingredient Mixing Enable - G1", "macro": "EEP_MIXING_ENABLE_24", "description": "Enable ingredient mixing for G1", "address": 120, "boardName": "Master", "boardId": 65535, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 133, "isPrivate": true}, {"id": 84118, "name": "Ingredient Mixing Enable - G2", "macro": "EEP_MIXING_ENABLE_25", "description": "Enable ingredient mixing for G2", "address": 121, "boardName": "Master", "boardId": 65535, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 133, "isPrivate": true}, {"id": 84119, "name": "Ingredient Mixing Enable - G3", "macro": "EEP_MIXING_ENABLE_26", "description": "Enable ingredient mixing for G3", "address": 122, "boardName": "Master", "boardId": 65535, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 133, "isPrivate": true}, {"id": 84120, "name": "Ingredient Mixing Enable - G4", "macro": "EEP_MIXING_ENABLE_27", "description": "Enable ingredient mixing for G4", "address": 123, "boardName": "Master", "boardId": 65535, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "false", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 133, "isPrivate": true}, {"id": 84121, "name": "Ingredient Sensor Active Threshold - A1, A2, B1, B2", "macro": "EEP_LEVEL_THRESHOLD", "description": "Ingredient level (expressed in 1/20 fl. oz. increments) below which runout detection becomes active for ingredients A1, A2, B1, B2", "address": 256, "boardName": "Pump Board 7", "boardId": 7, "paramType": "WORD", "defaultParam": "50", "currentValue": "100", "newValue": null, "minParam": "10", "maxParam": "3500", "machineId": 133, "isPrivate": false}, {"id": 84122, "name": "Ingredient Sensor Active Threshold - A3, A4, B3, B4", "macro": "EEP_LEVEL_THRESHOLD", "description": "Ingredient level (expressed in 1/20 fl. oz. increments) below which runout detection becomes active for ingredients A3, A4, B3, B4", "address": 256, "boardName": "Pump Board 8", "boardId": 8, "paramType": "WORD", "defaultParam": "50", "currentValue": "100", "newValue": null, "minParam": "10", "maxParam": "3500", "machineId": 133, "isPrivate": false}, {"id": 84123, "name": "Ingredient Sensor Active Threshold - A5, A6, B5, B6", "macro": "EEP_LEVEL_THRESHOLD", "description": "Ingredient level (expressed in 1/20 fl. oz. increments) below which runout detection becomes active for ingredients A5, A6, B5, B6", "address": 256, "boardName": "Pump Board 9", "boardId": 9, "paramType": "WORD", "defaultParam": "50", "currentValue": "100", "newValue": null, "minParam": "10", "maxParam": "3500", "machineId": 133, "isPrivate": false}, {"id": 84124, "name": "Ingredient Sensor Active Threshold - C1,D1,E1,F1", "macro": "EEP_LEVEL_THRESHOLD", "description": "Ingredient level (expressed in 1/20 fl. oz. increments) below which runout detection becomes active for ingredients C1,D1,E1,F1", "address": 256, "boardName": "Pump Board 0", "boardId": 0, "paramType": "WORD", "defaultParam": "50", "currentValue": "65535", "newValue": null, "minParam": "10", "maxParam": "3500", "machineId": 133, "isPrivate": false}, {"id": 84125, "name": "Ingredient Sensor Active Threshold - C2, E2, D2, F2", "macro": "EEP_LEVEL_THRESHOLD", "description": "Ingredient level (expressed in 1/20 fl. oz. increments) below which runout detection becomes active for ingredients C2, E2, D2, F2", "address": 256, "boardName": "Pump Board 1", "boardId": 1, "paramType": "WORD", "defaultParam": "50", "currentValue": "100", "newValue": null, "minParam": "10", "maxParam": "3500", "machineId": 133, "isPrivate": false}, {"id": 84126, "name": "Ingredient Sensor Active Threshold - C3, D3, E3, F3", "macro": "EEP_LEVEL_THRESHOLD", "description": "Ingredient level (expressed in 1/20 fl. oz. increments) below which runout detection becomes active for ingredients C3, D3, E3, F3", "address": 256, "boardName": "Pump Board 2", "boardId": 2, "paramType": "WORD", "defaultParam": "50", "currentValue": "100", "newValue": null, "minParam": "10", "maxParam": "3500", "machineId": 133, "isPrivate": false}, {"id": 84127, "name": "Ingredient Sensor Active Threshold - C4, D4, E4, F4", "macro": "EEP_LEVEL_THRESHOLD", "description": "Ingredient level (expressed in 1/20 fl. oz. increments) below which runout detection becomes active for ingredients C4, D4, E4, F4", "address": 256, "boardName": "Pump Board 3", "boardId": 3, "paramType": "WORD", "defaultParam": "50", "currentValue": "100", "newValue": null, "minParam": "10", "maxParam": "3500", "machineId": 133, "isPrivate": false}, {"id": 84128, "name": "Ingredient Sensor Active Threshold - C5, D5, E5, F5", "macro": "EEP_LEVEL_THRESHOLD", "description": "Ingredient level (expressed in 1/20 fl. oz. increments) below which runout detection becomes active for ingredients C5, D5, E5, F5", "address": 256, "boardName": "Pump Board 4", "boardId": 4, "paramType": "WORD", "defaultParam": "50", "currentValue": "100", "newValue": null, "minParam": "10", "maxParam": "3500", "machineId": 133, "isPrivate": false}, {"id": 84129, "name": "Ingredient Sensor Active Threshold - C6, D6, E6, F6", "macro": "EEP_LEVEL_THRESHOLD", "description": "Ingredient level (expressed in 1/20 fl. oz. increments) below which runout detection becomes active for ingredients C6, D6, E6, F6", "address": 256, "boardName": "Pump Board 5", "boardId": 5, "paramType": "WORD", "defaultParam": "50", "currentValue": "100", "newValue": null, "minParam": "10", "maxParam": "3500", "machineId": 133, "isPrivate": false}, {"id": 84130, "name": "Ingredient Sensor Active Threshold - T1 - T4", "macro": "EEP_LEVEL_THRESHOLD", "description": "Ingredient level (expressed in 1/20 fl. oz. increments) below which runout detection becomes active for ingredients T1 - T4", "address": 256, "boardName": "Pump Board 13", "boardId": 13, "paramType": "WORD", "defaultParam": "100", "currentValue": "65535", "newValue": null, "minParam": "10", "maxParam": "3500", "machineId": 133, "isPrivate": false}, {"id": 84131, "name": "Ingredient Sensor Active Threshold - T5 - T8", "macro": "EEP_LEVEL_THRESHOLD", "description": "Ingredient level (expressed in 1/20 fl. oz. increments) below which runout detection becomes active for ingredients T4 - T8", "address": 256, "boardName": "Pump Board 14", "boardId": 14, "paramType": "WORD", "defaultParam": "100", "currentValue": "65535", "newValue": null, "minParam": "10", "maxParam": "3500", "machineId": 133, "isPrivate": false}, {"id": 84132, "name": "Ingredient Sensor Enable - C1,D1,E1,F1", "macro": "EEP_BUBBLE_DETECT_ENABLE", "description": "Enable the use of ingredient detection sensors for an entire column of ingredients", "address": 160, "boardName": "Pump Board 0", "boardId": 0, "paramType": "BOOLEAN", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 133, "isPrivate": false}, {"id": 84133, "name": "Leave ALL Ingredients Unprimed at Startup", "macro": "EEP_ENABLE_UNPRIMED_STARTUP", "description": "At startup, leave ALL lines that were cleaned unprimed in order to save as much ingredient as possible throughout the day. This causes first dispenses of the day to be SLOW! Use with Caution.", "address": 370, "boardName": "Master", "boardId": 65535, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 133, "isPrivate": false}, {"id": 84134, "name": "<PERSON>/<PERSON> Default Ingredient Speed- A1, B1, A2, B2", "macro": "EEP_OZMIN_INGR_SPEED_DEFAULT", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 416, "boardName": "Pump Board 7", "boardId": 7, "paramType": "SINGLE", "defaultParam": "22.5", "currentValue": "22.5", "newValue": null, "minParam": "7.5", "maxParam": "37.5", "machineId": 133, "isPrivate": true}, {"id": 84135, "name": "<PERSON>/<PERSON> De<PERSON>ult Ingredient Speed- A3, B3, A4, B4", "macro": "EEP_OZMIN_INGR_SPEED_DEFAULT", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 416, "boardName": "Pump Board 8", "boardId": 8, "paramType": "SINGLE", "defaultParam": "22.5", "currentValue": "22.5", "newValue": null, "minParam": "7.5", "maxParam": "37.5", "machineId": 133, "isPrivate": true}, {"id": 84136, "name": "<PERSON>/<PERSON> De<PERSON> Ingredient Speed- A5, B5, A6, B6", "macro": "EEP_OZMIN_INGR_SPEED_DEFAULT", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 416, "boardName": "Pump Board 9", "boardId": 9, "paramType": "SINGLE", "defaultParam": "22.5", "currentValue": "22.5", "newValue": null, "minParam": "7.5", "maxParam": "37.5", "machineId": 133, "isPrivate": true}, {"id": 84137, "name": "<PERSON>/<PERSON> De<PERSON>ult Ingredient Speed- C1, D1, E1, F1", "macro": "EEP_OZMIN_INGR_SPEED_DEFAULT", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 416, "boardName": "Pump Board 0", "boardId": 0, "paramType": "SINGLE", "defaultParam": "22.5", "currentValue": "NaN", "newValue": null, "minParam": "7.5", "maxParam": "37.5", "machineId": 133, "isPrivate": true}, {"id": 84138, "name": "<PERSON>/<PERSON> Default Ingredient Speed- C2, D2, E2, F2", "macro": "EEP_OZMIN_INGR_SPEED_DEFAULT", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 416, "boardName": "Pump Board 1", "boardId": 1, "paramType": "SINGLE", "defaultParam": "22.5", "currentValue": "22.5", "newValue": null, "minParam": "7.5", "maxParam": "37.5", "machineId": 133, "isPrivate": true}, {"id": 84139, "name": "<PERSON>/<PERSON> Default Ingredient Speed- C3, D3, E3, F3", "macro": "EEP_OZMIN_INGR_SPEED_DEFAULT", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 416, "boardName": "Pump Board 2", "boardId": 2, "paramType": "SINGLE", "defaultParam": "22.5", "currentValue": "22.5", "newValue": null, "minParam": "7.5", "maxParam": "37.5", "machineId": 133, "isPrivate": true}, {"id": 84140, "name": "<PERSON>/<PERSON> De<PERSON>ult Ingredient Speed- C4, D4, E4, F4", "macro": "EEP_OZMIN_INGR_SPEED_DEFAULT", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 416, "boardName": "Pump Board 3", "boardId": 3, "paramType": "SINGLE", "defaultParam": "22.5", "currentValue": "22.5", "newValue": null, "minParam": "7.5", "maxParam": "37.5", "machineId": 133, "isPrivate": true}, {"id": 84141, "name": "<PERSON>/<PERSON> De<PERSON>ult Ingredient Speed- C5, D5, E5, F5", "macro": "EEP_OZMIN_INGR_SPEED_DEFAULT", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 416, "boardName": "Pump Board 4", "boardId": 4, "paramType": "SINGLE", "defaultParam": "22.5", "currentValue": "22.5", "newValue": null, "minParam": "7.5", "maxParam": "37.5", "machineId": 133, "isPrivate": true}, {"id": 84142, "name": "<PERSON>/<PERSON> De<PERSON>ult Ingredient Speed- C6, D6, E6, F6", "macro": "EEP_OZMIN_INGR_SPEED_DEFAULT", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 416, "boardName": "Pump Board 5", "boardId": 5, "paramType": "SINGLE", "defaultParam": "22.5", "currentValue": "22.5", "newValue": null, "minParam": "7.5", "maxParam": "37.5", "machineId": 133, "isPrivate": true}, {"id": 84143, "name": "<PERSON>/<PERSON> De<PERSON>ult Ingredient Speed- T1 - T4", "macro": "EEP_OZMIN_INGR_SPEED_DEFAULT", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 416, "boardName": "Pump Board 13", "boardId": 13, "paramType": "SINGLE", "defaultParam": "8.0", "currentValue": "NaN", "newValue": null, "minParam": "7.5", "maxParam": "37.5", "machineId": 133, "isPrivate": true}, {"id": 84144, "name": "<PERSON>/<PERSON> De<PERSON> Ingredient Speed- T5 - T8", "macro": "EEP_OZMIN_INGR_SPEED_DEFAULT", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 416, "boardName": "Pump Board 14", "boardId": 14, "paramType": "SINGLE", "defaultParam": "8.0", "currentValue": "NaN", "newValue": null, "minParam": "7.5", "maxParam": "37.5", "machineId": 133, "isPrivate": true}, {"id": 84145, "name": "<PERSON>/<PERSON> Ingredient Speed - A1", "macro": "EEP_OZMIN_INGR_SPEED_0", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 400, "boardName": "Pump Board 7", "boardId": 7, "paramType": "SINGLE", "defaultParam": "20", "currentValue": "34", "newValue": "15", "minParam": "7.5", "maxParam": "37.5", "machineId": 133, "isPrivate": false}, {"id": 84146, "name": "<PERSON>/Min Ingredient Speed - A2", "macro": "EEP_OZMIN_INGR_SPEED_2", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 408, "boardName": "Pump Board 7", "boardId": 7, "paramType": "SINGLE", "defaultParam": "20", "currentValue": "7.5", "newValue": "15", "minParam": "7.5", "maxParam": "37.5", "machineId": 133, "isPrivate": false}, {"id": 84147, "name": "Oz/Min Ingredient Speed - A3", "macro": "EEP_OZMIN_INGR_SPEED_0", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 400, "boardName": "Pump Board 8", "boardId": 8, "paramType": "SINGLE", "defaultParam": "20", "currentValue": "7.5", "newValue": "15", "minParam": "7.5", "maxParam": "37.5", "machineId": 133, "isPrivate": false}, {"id": 84148, "name": "Oz/Min Ingredient Speed - A4", "macro": "EEP_OZMIN_INGR_SPEED_2", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 408, "boardName": "Pump Board 8", "boardId": 8, "paramType": "SINGLE", "defaultParam": "20", "currentValue": "7.5", "newValue": "15", "minParam": "7.5", "maxParam": "37.5", "machineId": 133, "isPrivate": false}, {"id": 84149, "name": "Oz/Min Ingredient Speed - A5", "macro": "EEP_OZMIN_INGR_SPEED_0", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 400, "boardName": "Pump Board 9", "boardId": 9, "paramType": "SINGLE", "defaultParam": "20", "currentValue": "7.5", "newValue": "15", "minParam": "7.5", "maxParam": "37.5", "machineId": 133, "isPrivate": false}, {"id": 84150, "name": "Oz/Min Ingredient Speed - A6", "macro": "EEP_OZMIN_INGR_SPEED_2", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 408, "boardName": "Pump Board 9", "boardId": 9, "paramType": "SINGLE", "defaultParam": "20", "currentValue": "7.5", "newValue": "0", "minParam": "7.5", "maxParam": "37.5", "machineId": 133, "isPrivate": false}, {"id": 84151, "name": "Oz/Min Ingredient Speed - B1", "macro": "EEP_OZMIN_INGR_SPEED_1", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 404, "boardName": "Pump Board 7", "boardId": 7, "paramType": "SINGLE", "defaultParam": "20", "currentValue": "34", "newValue": "15", "minParam": "7.5", "maxParam": "37.5", "machineId": 133, "isPrivate": false}, {"id": 84152, "name": "Oz/Min Ingredient Speed - B2", "macro": "EEP_OZMIN_INGR_SPEED_3", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 412, "boardName": "Pump Board 7", "boardId": 7, "paramType": "SINGLE", "defaultParam": "20", "currentValue": "7.5", "newValue": "15", "minParam": "7.5", "maxParam": "37.5", "machineId": 133, "isPrivate": false}, {"id": 84153, "name": "Oz/Min Ingredient Speed - B3", "macro": "EEP_OZMIN_INGR_SPEED_1", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 404, "boardName": "Pump Board 8", "boardId": 8, "paramType": "SINGLE", "defaultParam": "20", "currentValue": "7.5", "newValue": "15", "minParam": "7.5", "maxParam": "37.5", "machineId": 133, "isPrivate": false}, {"id": 84154, "name": "Oz/Min Ingredient Speed - B4", "macro": "EEP_OZMIN_INGR_SPEED_3", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 412, "boardName": "Pump Board 8", "boardId": 8, "paramType": "SINGLE", "defaultParam": "20", "currentValue": "7.5", "newValue": "15", "minParam": "7.5", "maxParam": "37.5", "machineId": 133, "isPrivate": false}, {"id": 84155, "name": "Oz/Min Ingredient Speed - B5", "macro": "EEP_OZMIN_INGR_SPEED_1", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 404, "boardName": "Pump Board 9", "boardId": 9, "paramType": "SINGLE", "defaultParam": "20", "currentValue": "34.5", "newValue": "15", "minParam": "7.5", "maxParam": "37.5", "machineId": 133, "isPrivate": false}, {"id": 84156, "name": "Oz/Min Ingredient Speed - B6", "macro": "EEP_OZMIN_INGR_SPEED_3", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 412, "boardName": "Pump Board 9", "boardId": 9, "paramType": "SINGLE", "defaultParam": "20", "currentValue": "7.5", "newValue": "0", "minParam": "7.5", "maxParam": "37.5", "machineId": 133, "isPrivate": false}, {"id": 84157, "name": "Oz/Min Ingredient Speed - C1", "macro": "EEP_OZMIN_INGR_SPEED_0", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 400, "boardName": "Pump Board 0", "boardId": 0, "paramType": "SINGLE", "defaultParam": "7.5", "currentValue": "7.5", "newValue": "15", "minParam": "7.5", "maxParam": "37.5", "machineId": 133, "isPrivate": false}, {"id": 84158, "name": "Oz/Min Ingredient Speed - C2", "macro": "EEP_OZMIN_INGR_SPEED_0", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 400, "boardName": "Pump Board 1", "boardId": 1, "paramType": "SINGLE", "defaultParam": "20", "currentValue": "7.5", "newValue": "15", "minParam": "7.5", "maxParam": "37.5", "machineId": 133, "isPrivate": false}, {"id": 84159, "name": "Oz/Min Ingredient Speed - C3", "macro": "EEP_OZMIN_INGR_SPEED_0", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 400, "boardName": "Pump Board 2", "boardId": 2, "paramType": "SINGLE", "defaultParam": "20", "currentValue": "7.5", "newValue": "15", "minParam": "7.5", "maxParam": "37.5", "machineId": 133, "isPrivate": false}, {"id": 84160, "name": "Oz/Min Ingredient Speed - C4", "macro": "EEP_OZMIN_INGR_SPEED_0", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 400, "boardName": "Pump Board 3", "boardId": 3, "paramType": "SINGLE", "defaultParam": "20", "currentValue": "7.5", "newValue": "15", "minParam": "7.5", "maxParam": "37.5", "machineId": 133, "isPrivate": false}, {"id": 84161, "name": "Oz/Min Ingredient Speed - C5", "macro": "EEP_OZMIN_INGR_SPEED_0", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 400, "boardName": "Pump Board 4", "boardId": 4, "paramType": "SINGLE", "defaultParam": "20", "currentValue": "7.5", "newValue": "15", "minParam": "7.5", "maxParam": "37.5", "machineId": 133, "isPrivate": false}, {"id": 84162, "name": "Oz/Min Ingredient Speed - C6", "macro": "EEP_OZMIN_INGR_SPEED_0", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 400, "boardName": "Pump Board 5", "boardId": 5, "paramType": "SINGLE", "defaultParam": "20", "currentValue": "7.5", "newValue": "15", "minParam": "7.5", "maxParam": "37.5", "machineId": 133, "isPrivate": false}, {"id": 84163, "name": "Oz/Min Ingredient Speed - D1", "macro": "EEP_OZMIN_INGR_SPEED_1", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 404, "boardName": "Pump Board 0", "boardId": 0, "paramType": "SINGLE", "defaultParam": "7.5", "currentValue": "7.5", "newValue": "15", "minParam": "7.5", "maxParam": "37.5", "machineId": 133, "isPrivate": false}, {"id": 84164, "name": "Oz/Min Ingredient Speed - D2", "macro": "EEP_OZMIN_INGR_SPEED_1", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 404, "boardName": "Pump Board 1", "boardId": 1, "paramType": "SINGLE", "defaultParam": "20", "currentValue": "7.5", "newValue": "15", "minParam": "7.5", "maxParam": "37.5", "machineId": 133, "isPrivate": false}, {"id": 84165, "name": "Oz/Min Ingredient Speed - D3", "macro": "EEP_OZMIN_INGR_SPEED_1", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 404, "boardName": "Pump Board 2", "boardId": 2, "paramType": "SINGLE", "defaultParam": "20", "currentValue": "7.5", "newValue": "15", "minParam": "7.5", "maxParam": "37.5", "machineId": 133, "isPrivate": false}, {"id": 84166, "name": "Oz/Min Ingredient Speed - D4", "macro": "EEP_OZMIN_INGR_SPEED_1", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 404, "boardName": "Pump Board 3", "boardId": 3, "paramType": "SINGLE", "defaultParam": "20", "currentValue": "7.5", "newValue": "15", "minParam": "7.5", "maxParam": "37.5", "machineId": 133, "isPrivate": false}, {"id": 84167, "name": "Oz/Min Ingredient Speed - D5", "macro": "EEP_OZMIN_INGR_SPEED_1", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 404, "boardName": "Pump Board 4", "boardId": 4, "paramType": "SINGLE", "defaultParam": "20", "currentValue": "7.5", "newValue": "15", "minParam": "7.5", "maxParam": "37.5", "machineId": 133, "isPrivate": false}, {"id": 84168, "name": "Oz/Min Ingredient Speed - D6", "macro": "EEP_OZMIN_INGR_SPEED_1", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 404, "boardName": "Pump Board 5", "boardId": 5, "paramType": "SINGLE", "defaultParam": "20", "currentValue": "7.5", "newValue": "15", "minParam": "7.5", "maxParam": "37.5", "machineId": 133, "isPrivate": false}, {"id": 84169, "name": "Oz/Min Ingredient Speed - E1", "macro": "EEP_OZMIN_INGR_SPEED_2", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 408, "boardName": "Pump Board 0", "boardId": 0, "paramType": "SINGLE", "defaultParam": "7.5", "currentValue": "7.5", "newValue": "0", "minParam": "7.5", "maxParam": "37.5", "machineId": 133, "isPrivate": false}, {"id": 84170, "name": "Oz/Min Ingredient Speed - E2", "macro": "EEP_OZMIN_INGR_SPEED_2", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 408, "boardName": "Pump Board 1", "boardId": 1, "paramType": "SINGLE", "defaultParam": "20", "currentValue": "7.5", "newValue": "15", "minParam": "7.5", "maxParam": "37.5", "machineId": 133, "isPrivate": false}, {"id": 84171, "name": "Oz/Min Ingredient Speed - E3", "macro": "EEP_OZMIN_INGR_SPEED_2", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 408, "boardName": "Pump Board 2", "boardId": 2, "paramType": "SINGLE", "defaultParam": "20", "currentValue": "7.5", "newValue": "15", "minParam": "7.5", "maxParam": "37.5", "machineId": 133, "isPrivate": false}, {"id": 84172, "name": "Oz/Min Ingredient Speed - E4", "macro": "EEP_OZMIN_INGR_SPEED_2", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 408, "boardName": "Pump Board 3", "boardId": 3, "paramType": "SINGLE", "defaultParam": "20", "currentValue": "7.5", "newValue": "15", "minParam": "7.5", "maxParam": "37.5", "machineId": 133, "isPrivate": false}, {"id": 84173, "name": "Oz/Min Ingredient Speed - E5", "macro": "EEP_OZMIN_INGR_SPEED_2", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 408, "boardName": "Pump Board 4", "boardId": 4, "paramType": "SINGLE", "defaultParam": "20", "currentValue": "7.5", "newValue": "15", "minParam": "7.5", "maxParam": "37.5", "machineId": 133, "isPrivate": false}, {"id": 84174, "name": "Oz/Min Ingredient Speed - E6", "macro": "EEP_OZMIN_INGR_SPEED_2", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 408, "boardName": "Pump Board 5", "boardId": 5, "paramType": "SINGLE", "defaultParam": "20", "currentValue": "7.5", "newValue": "15", "minParam": "7.5", "maxParam": "37.5", "machineId": 133, "isPrivate": false}, {"id": 84175, "name": "<PERSON>/<PERSON> Ingredient Speed - F1", "macro": "EEP_OZMIN_INGR_SPEED_3", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 412, "boardName": "Pump Board 0", "boardId": 0, "paramType": "SINGLE", "defaultParam": "7.5", "currentValue": "7.5", "newValue": "15", "minParam": "7.5", "maxParam": "37.5", "machineId": 133, "isPrivate": false}, {"id": 84176, "name": "Oz/Min Ingredient Speed - F2", "macro": "EEP_OZMIN_INGR_SPEED_3", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 412, "boardName": "Pump Board 1", "boardId": 1, "paramType": "SINGLE", "defaultParam": "20", "currentValue": "7.5", "newValue": "15", "minParam": "7.5", "maxParam": "37.5", "machineId": 133, "isPrivate": false}, {"id": 84177, "name": "Oz/Min Ingredient Speed - F3", "macro": "EEP_OZMIN_INGR_SPEED_3", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 412, "boardName": "Pump Board 2", "boardId": 2, "paramType": "SINGLE", "defaultParam": "20", "currentValue": "7.5", "newValue": "15", "minParam": "7.5", "maxParam": "37.5", "machineId": 133, "isPrivate": false}, {"id": 84178, "name": "Oz/Min Ingredient Speed - F4", "macro": "EEP_OZMIN_INGR_SPEED_3", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 412, "boardName": "Pump Board 3", "boardId": 3, "paramType": "SINGLE", "defaultParam": "20", "currentValue": "7.5", "newValue": "15", "minParam": "7.5", "maxParam": "37.5", "machineId": 133, "isPrivate": false}, {"id": 84179, "name": "Oz/Min Ingredient Speed - F5", "macro": "EEP_OZMIN_INGR_SPEED_3", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 412, "boardName": "Pump Board 4", "boardId": 4, "paramType": "SINGLE", "defaultParam": "20", "currentValue": "7.5", "newValue": "15", "minParam": "7.5", "maxParam": "37.5", "machineId": 133, "isPrivate": false}, {"id": 84180, "name": "Oz/Min Ingredient Speed - F6", "macro": "EEP_OZMIN_INGR_SPEED_3", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 412, "boardName": "Pump Board 5", "boardId": 5, "paramType": "SINGLE", "defaultParam": "20", "currentValue": "7.5", "newValue": "15", "minParam": "7.5", "maxParam": "37.5", "machineId": 133, "isPrivate": false}, {"id": 84181, "name": "Oz/Min Ingredient Speed - T1", "macro": "EEP_OZMIN_INGR_SPEED_0", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 400, "boardName": "Pump Board 13", "boardId": 13, "paramType": "SINGLE", "defaultParam": "8.0", "currentValue": "8", "newValue": null, "minParam": "7.5", "maxParam": "37.5", "machineId": 133, "isPrivate": false}, {"id": 84182, "name": "Oz/Min Ingredient Speed - T2", "macro": "EEP_OZMIN_INGR_SPEED_1", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 404, "boardName": "Pump Board 13", "boardId": 13, "paramType": "SINGLE", "defaultParam": "8.0", "currentValue": "8", "newValue": null, "minParam": "7.5", "maxParam": "37.5", "machineId": 133, "isPrivate": false}, {"id": 84183, "name": "Oz/Min Ingredient Speed - T3", "macro": "EEP_OZMIN_INGR_SPEED_2", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 408, "boardName": "Pump Board 13", "boardId": 13, "paramType": "SINGLE", "defaultParam": "8.0", "currentValue": "8", "newValue": null, "minParam": "7.5", "maxParam": "37.5", "machineId": 133, "isPrivate": false}, {"id": 84184, "name": "Oz/Min Ingredient Speed - T4", "macro": "EEP_OZMIN_INGR_SPEED_3", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 412, "boardName": "Pump Board 13", "boardId": 13, "paramType": "SINGLE", "defaultParam": "8.0", "currentValue": "8", "newValue": null, "minParam": "7.5", "maxParam": "37.5", "machineId": 133, "isPrivate": false}, {"id": 84185, "name": "Oz/Min Ingredient Speed - T5", "macro": "EEP_OZMIN_INGR_SPEED_0", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 400, "boardName": "Pump Board 14", "boardId": 14, "paramType": "SINGLE", "defaultParam": "8", "currentValue": "8", "newValue": null, "minParam": "7.5", "maxParam": "37.5", "machineId": 133, "isPrivate": false}, {"id": 84186, "name": "Oz/Min Ingredient Speed - T6", "macro": "EEP_OZMIN_INGR_SPEED_1", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 404, "boardName": "Pump Board 14", "boardId": 14, "paramType": "SINGLE", "defaultParam": "8.0", "currentValue": "8", "newValue": null, "minParam": "7.5", "maxParam": "37.5", "machineId": 133, "isPrivate": false}, {"id": 84187, "name": "Oz/Min Ingredient Speed - T7", "macro": "EEP_OZMIN_INGR_SPEED_2", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 408, "boardName": "Pump Board 14", "boardId": 14, "paramType": "SINGLE", "defaultParam": "22.5", "currentValue": "22.5", "newValue": null, "minParam": "7.5", "maxParam": "37.5", "machineId": 133, "isPrivate": false}, {"id": 84188, "name": "Oz/Min Ingredient Speed - T8", "macro": "EEP_OZMIN_INGR_SPEED_3", "description": "Ingredient dispensing speed in ounces per minute (min=7.5, max=37.5)", "address": 412, "boardName": "Pump Board 14", "boardId": 14, "paramType": "SINGLE", "defaultParam": "22.5", "currentValue": "22.5", "newValue": null, "minParam": "7.5", "maxParam": "37.5", "machineId": 133, "isPrivate": false}, {"id": 84189, "name": "Priming Quantity - A1", "macro": "EEP_PRIMING_QTY_0", "description": "Priming quantity (expressed in 1/20 fl. oz. increments) for ingredient A1", "address": 84, "boardName": "Pump Board 7", "boardId": 7, "paramType": "BYTE", "defaultParam": "46", "currentValue": "51", "newValue": null, "minParam": "40", "maxParam": "110", "machineId": 133, "isPrivate": true}, {"id": 84190, "name": "Priming Quantity - A2", "macro": "EEP_PRIMING_QTY_2", "description": "Priming quantity (expressed in 1/20 fl. oz. increments) for ingredient A2", "address": 116, "boardName": "Pump Board 7", "boardId": 7, "paramType": "BYTE", "defaultParam": "56", "currentValue": "61", "newValue": null, "minParam": "40", "maxParam": "110", "machineId": 133, "isPrivate": true}, {"id": 84191, "name": "Priming Quantity - A3", "macro": "EEP_PRIMING_QTY_0", "description": "Priming quantity (expressed in 1/20 fl. oz. increments) for ingredient A3", "address": 84, "boardName": "Pump Board 8", "boardId": 8, "paramType": "BYTE", "defaultParam": "46", "currentValue": "49", "newValue": null, "minParam": "40", "maxParam": "110", "machineId": 133, "isPrivate": true}, {"id": 84192, "name": "Priming Quantity - A4", "macro": "EEP_PRIMING_QTY_2", "description": "Priming quantity (expressed in 1/20 fl. oz. increments) for ingredient A4", "address": 116, "boardName": "Pump Board 8", "boardId": 8, "paramType": "BYTE", "defaultParam": "57", "currentValue": "59", "newValue": null, "minParam": "40", "maxParam": "110", "machineId": 133, "isPrivate": true}, {"id": 84193, "name": "Priming Quantity - A5", "macro": "EEP_PRIMING_QTY_0", "description": "Priming quantity (expressed in 1/20 fl. oz. increments) for ingredient A5", "address": 84, "boardName": "Pump Board 9", "boardId": 9, "paramType": "BYTE", "defaultParam": "50", "currentValue": "50", "newValue": null, "minParam": "40", "maxParam": "110", "machineId": 133, "isPrivate": true}, {"id": 84194, "name": "Priming Quantity - A6", "macro": "EEP_PRIMING_QTY_2", "description": "Priming quantity (expressed in 1/20 fl. oz. increments) for ingredient A6", "address": 116, "boardName": "Pump Board 9", "boardId": 9, "paramType": "BYTE", "defaultParam": "65", "currentValue": "64", "newValue": null, "minParam": "40", "maxParam": "110", "machineId": 133, "isPrivate": true}, {"id": 84195, "name": "Priming Quantity - B1", "macro": "EEP_PRIMING_QTY_1", "description": "Priming quantity (expressed in 1/20 fl. oz. increments) for ingredient B1", "address": 100, "boardName": "Pump Board 7", "boardId": 7, "paramType": "BYTE", "defaultParam": "51", "currentValue": "51", "newValue": null, "minParam": "40", "maxParam": "110", "machineId": 133, "isPrivate": true}, {"id": 84196, "name": "Priming Quantity - B2", "macro": "EEP_PRIMING_QTY_3", "description": "Priming quantity (expressed in 1/20 fl. oz. increments) for ingredient B2", "address": 132, "boardName": "Pump Board 7", "boardId": 7, "paramType": "BYTE", "defaultParam": "55", "currentValue": "60", "newValue": null, "minParam": "40", "maxParam": "110", "machineId": 133, "isPrivate": true}, {"id": 84197, "name": "Priming Quantity - B3", "macro": "EEP_PRIMING_QTY_1", "description": "Priming quantity (expressed in 1/20 fl. oz. increments) for ingredient B3", "address": 100, "boardName": "Pump Board 8", "boardId": 8, "paramType": "BYTE", "defaultParam": "51", "currentValue": "51", "newValue": null, "minParam": "40", "maxParam": "110", "machineId": 133, "isPrivate": true}, {"id": 84198, "name": "Priming Quantity - B4", "macro": "EEP_PRIMING_QTY_3", "description": "Priming quantity (expressed in 1/20 fl. oz. increments) for ingredient B4", "address": 132, "boardName": "Pump Board 8", "boardId": 8, "paramType": "BYTE", "defaultParam": "55", "currentValue": "60", "newValue": null, "minParam": "40", "maxParam": "110", "machineId": 133, "isPrivate": true}, {"id": 84199, "name": "Priming Quantity - B5", "macro": "EEP_PRIMING_QTY_1", "description": "Priming quantity (expressed in 1/20 fl. oz. increments) for ingredient B5", "address": 100, "boardName": "Pump Board 9", "boardId": 9, "paramType": "BYTE", "defaultParam": "51", "currentValue": "53", "newValue": null, "minParam": "40", "maxParam": "110", "machineId": 133, "isPrivate": true}, {"id": 84200, "name": "Priming Quantity - B6", "macro": "EEP_PRIMING_QTY_3", "description": "Priming quantity (expressed in 1/20 fl. oz. increments) for ingredient B6", "address": 132, "boardName": "Pump Board 9", "boardId": 9, "paramType": "BYTE", "defaultParam": "58", "currentValue": "62", "newValue": null, "minParam": "40", "maxParam": "110", "machineId": 133, "isPrivate": true}, {"id": 84201, "name": "Priming Quantity - Bib 1", "macro": "EEP_BIB_PRIME_QTY_0", "description": "Priming quantity (expressed in 1/20th oz increments) for ingredient Bib 1", "address": 112, "boardName": "Nozzle", "boardId": 560, "paramType": "WORD", "defaultParam": "40", "currentValue": "65535", "newValue": null, "minParam": "40", "maxParam": "400", "machineId": 133, "isPrivate": true}, {"id": 84202, "name": "Priming Quantity - Bib 2", "macro": "EEP_BIB_PRIME_QTY_1", "description": "Priming quantity (expressed in 1/20th oz increments) for ingredient Bib 2", "address": 114, "boardName": "Nozzle", "boardId": 560, "paramType": "WORD", "defaultParam": "40", "currentValue": "65535", "newValue": null, "minParam": "40", "maxParam": "400", "machineId": 133, "isPrivate": true}, {"id": 84203, "name": "Priming Quantity - Bib 3", "macro": "EEP_BIB_PRIME_QTY_2", "description": "Priming quantity (expressed in 1/20th oz increments) for ingredient Bib 3", "address": 116, "boardName": "Nozzle", "boardId": 560, "paramType": "WORD", "defaultParam": "40", "currentValue": "65535", "newValue": null, "minParam": "40", "maxParam": "400", "machineId": 133, "isPrivate": true}, {"id": 84204, "name": "Priming Quantity - Bib 4", "macro": "EEP_BIB_PRIME_QTY_3", "description": "Priming quantity (expressed in 1/20th oz increments) for ingredient Bib 4", "address": 118, "boardName": "Nozzle", "boardId": 560, "paramType": "WORD", "defaultParam": "40", "currentValue": "65535", "newValue": null, "minParam": "40", "maxParam": "400", "machineId": 133, "isPrivate": true}, {"id": 84205, "name": "Priming Quantity - C1", "macro": "EEP_PRIMING_QTY_0", "description": "Priming quantity (expressed in 1/20 fl. oz. increments) for ingredient C1", "address": 84, "boardName": "Pump Board 0", "boardId": 0, "paramType": "BYTE", "defaultParam": "54", "currentValue": "51", "newValue": null, "minParam": "40", "maxParam": "110", "machineId": 133, "isPrivate": true}, {"id": 84206, "name": "Priming Quantity - C2", "macro": "EEP_PRIMING_QTY_0", "description": "Priming quantity (expressed in 1/20 fl. oz. increments) for ingredient C2", "address": 84, "boardName": "Pump Board 1", "boardId": 1, "paramType": "BYTE", "defaultParam": "50", "currentValue": "53", "newValue": null, "minParam": "40", "maxParam": "110", "machineId": 133, "isPrivate": true}, {"id": 84207, "name": "Priming Quantity - C3", "macro": "EEP_PRIMING_QTY_0", "description": "Priming quantity (expressed in 1/20 fl. oz. increments) for ingredient C3", "address": 84, "boardName": "Pump Board 2", "boardId": 2, "paramType": "BYTE", "defaultParam": "57", "currentValue": "58", "newValue": null, "minParam": "40", "maxParam": "110", "machineId": 133, "isPrivate": true}, {"id": 84208, "name": "Priming Quantity - C4", "macro": "EEP_PRIMING_QTY_0", "description": "Priming quantity (expressed in 1/20 fl. oz. increments) for ingredient C4", "address": 84, "boardName": "Pump Board 3", "boardId": 3, "paramType": "BYTE", "defaultParam": "50", "currentValue": "56", "newValue": null, "minParam": "40", "maxParam": "110", "machineId": 133, "isPrivate": true}, {"id": 84209, "name": "Priming Quantity - C5", "macro": "EEP_PRIMING_QTY_0", "description": "Priming quantity (expressed in 1/20 fl. oz. increments) for ingredient C5", "address": 84, "boardName": "Pump Board 4", "boardId": 4, "paramType": "BYTE", "defaultParam": "55", "currentValue": "56", "newValue": null, "minParam": "40", "maxParam": "110", "machineId": 133, "isPrivate": true}, {"id": 84210, "name": "Priming Quantity - C6", "macro": "EEP_PRIMING_QTY_0", "description": "Priming quantity (expressed in 1/20 fl. oz. increments) for ingredient C6", "address": 84, "boardName": "Pump Board 5", "boardId": 5, "paramType": "BYTE", "defaultParam": "55", "currentValue": "60", "newValue": null, "minParam": "40", "maxParam": "110", "machineId": 133, "isPrivate": true}, {"id": 84211, "name": "Priming Quantity - D1", "macro": "EEP_PRIMING_QTY_1", "description": "Priming quantity (expressed in 1/20 fl. oz. increments) for ingredient D1", "address": 100, "boardName": "Pump Board 0", "boardId": 0, "paramType": "BYTE", "defaultParam": "64", "currentValue": "56", "newValue": null, "minParam": "40", "maxParam": "110", "machineId": 133, "isPrivate": true}, {"id": 84212, "name": "Priming Quantity - D2", "macro": "EEP_PRIMING_QTY_1", "description": "Priming quantity (expressed in 1/20 fl. oz. increments) for ingredient D2", "address": 100, "boardName": "Pump Board 1", "boardId": 1, "paramType": "BYTE", "defaultParam": "48", "currentValue": "51", "newValue": null, "minParam": "40", "maxParam": "110", "machineId": 133, "isPrivate": true}, {"id": 84213, "name": "Priming Quantity - D3", "macro": "EEP_PRIMING_QTY_1", "description": "Priming quantity (expressed in 1/20 fl. oz. increments) for ingredient D3", "address": 100, "boardName": "Pump Board 2", "boardId": 2, "paramType": "BYTE", "defaultParam": "56", "currentValue": "60", "newValue": null, "minParam": "40", "maxParam": "110", "machineId": 133, "isPrivate": true}, {"id": 84214, "name": "Priming Quantity - D4", "macro": "EEP_PRIMING_QTY_1", "description": "Priming quantity (expressed in 1/20 fl. oz. increments) for ingredient D4", "address": 100, "boardName": "Pump Board 3", "boardId": 3, "paramType": "BYTE", "defaultParam": "51", "currentValue": "53", "newValue": null, "minParam": "40", "maxParam": "110", "machineId": 133, "isPrivate": true}, {"id": 84215, "name": "Priming Quantity - D5", "macro": "EEP_PRIMING_QTY_1", "description": "Priming quantity (expressed in 1/20 fl. oz. increments) for ingredient D5", "address": 100, "boardName": "Pump Board 4", "boardId": 4, "paramType": "BYTE", "defaultParam": "55", "currentValue": "58", "newValue": null, "minParam": "40", "maxParam": "110", "machineId": 133, "isPrivate": true}, {"id": 84216, "name": "Priming Quantity - D6", "macro": "EEP_PRIMING_QTY_1", "description": "Priming quantity (expressed in 1/20 fl. oz. increments) for ingredient D6", "address": 100, "boardName": "Pump Board 5", "boardId": 5, "paramType": "BYTE", "defaultParam": "52", "currentValue": "56", "newValue": null, "minParam": "40", "maxParam": "110", "machineId": 133, "isPrivate": true}, {"id": 84217, "name": "Priming Quantity - E1", "macro": "EEP_PRIMING_QTY_2", "description": "Priming quantity (expressed in 1/20 fl. oz. increments) for ingredient E1", "address": 116, "boardName": "Pump Board 0", "boardId": 0, "paramType": "BYTE", "defaultParam": "60", "currentValue": "54", "newValue": null, "minParam": "40", "maxParam": "110", "machineId": 133, "isPrivate": true}, {"id": 84218, "name": "Priming Quantity - E2", "macro": "EEP_PRIMING_QTY_2", "description": "Priming quantity (expressed in 1/20 fl. oz. increments) for ingredient E2", "address": 116, "boardName": "Pump Board 1", "boardId": 1, "paramType": "BYTE", "defaultParam": "46", "currentValue": "50", "newValue": null, "minParam": "40", "maxParam": "110", "machineId": 133, "isPrivate": true}, {"id": 84219, "name": "Priming Quantity - E3", "macro": "EEP_PRIMING_QTY_2", "description": "Priming quantity (expressed in 1/20 fl. oz. increments) for ingredient E3", "address": 116, "boardName": "Pump Board 2", "boardId": 2, "paramType": "BYTE", "defaultParam": "63", "currentValue": "68", "newValue": null, "minParam": "40", "maxParam": "110", "machineId": 133, "isPrivate": true}, {"id": 84220, "name": "Priming Quantity - E4", "macro": "EEP_PRIMING_QTY_2", "description": "Priming quantity (expressed in 1/20 fl. oz. increments) for ingredient E4", "address": 116, "boardName": "Pump Board 3", "boardId": 3, "paramType": "BYTE", "defaultParam": "48", "currentValue": "52", "newValue": null, "minParam": "40", "maxParam": "110", "machineId": 133, "isPrivate": true}, {"id": 84221, "name": "Priming Quantity - E5", "macro": "EEP_PRIMING_QTY_2", "description": "Priming quantity (expressed in 1/20 fl. oz. increments) for ingredient E5", "address": 116, "boardName": "Pump Board 4", "boardId": 4, "paramType": "BYTE", "defaultParam": "64", "currentValue": "70", "newValue": null, "minParam": "40", "maxParam": "110", "machineId": 133, "isPrivate": true}, {"id": 84222, "name": "Priming Quantity - E6", "macro": "EEP_PRIMING_QTY_2", "description": "Priming quantity (expressed in 1/20 fl. oz. increments) for ingredient E6", "address": 116, "boardName": "Pump Board 5", "boardId": 5, "paramType": "BYTE", "defaultParam": "51", "currentValue": "55", "newValue": null, "minParam": "40", "maxParam": "110", "machineId": 133, "isPrivate": true}, {"id": 84223, "name": "Priming Quantity - F1", "macro": "EEP_PRIMING_QTY_3", "description": "Priming quantity (expressed in 1/20 fl. oz. increments) for ingredient F1", "address": 132, "boardName": "Pump Board 0", "boardId": 0, "paramType": "BYTE", "defaultParam": "66", "currentValue": "61", "newValue": null, "minParam": "40", "maxParam": "110", "machineId": 133, "isPrivate": true}, {"id": 84224, "name": "Priming Quantity - F2", "macro": "EEP_PRIMING_QTY_3", "description": "Priming quantity (expressed in 1/20 fl. oz. increments) for ingredient F2", "address": 132, "boardName": "Pump Board 1", "boardId": 1, "paramType": "BYTE", "defaultParam": "46", "currentValue": "50", "newValue": null, "minParam": "40", "maxParam": "110", "machineId": 133, "isPrivate": true}, {"id": 84225, "name": "Priming Quantity - F3", "macro": "EEP_PRIMING_QTY_3", "description": "Priming quantity (expressed in 1/20 fl. oz. increments) for ingredient F3", "address": 132, "boardName": "Pump Board 2", "boardId": 2, "paramType": "BYTE", "defaultParam": "62", "currentValue": "70", "newValue": null, "minParam": "40", "maxParam": "110", "machineId": 133, "isPrivate": true}, {"id": 84226, "name": "Priming Quantity - F4", "macro": "EEP_PRIMING_QTY_3", "description": "Priming quantity (expressed in 1/20 fl. oz. increments) for ingredient F4", "address": 132, "boardName": "Pump Board 3", "boardId": 3, "paramType": "BYTE", "defaultParam": "47", "currentValue": "51", "newValue": null, "minParam": "40", "maxParam": "110", "machineId": 133, "isPrivate": true}, {"id": 84227, "name": "Priming Quantity - F5", "macro": "EEP_PRIMING_QTY_3", "description": "Priming quantity (expressed in 1/20 fl. oz. increments) for ingredient F5", "address": 132, "boardName": "Pump Board 4", "boardId": 4, "paramType": "BYTE", "defaultParam": "63", "currentValue": "70", "newValue": null, "minParam": "40", "maxParam": "110", "machineId": 133, "isPrivate": true}, {"id": 84228, "name": "Priming Quantity - F6", "macro": "EEP_PRIMING_QTY_3", "description": "Priming quantity (expressed in 1/20 fl. oz. increments) for ingredient F6", "address": 132, "boardName": "Pump Board 5", "boardId": 5, "paramType": "BYTE", "defaultParam": "48", "currentValue": "53", "newValue": null, "minParam": "40", "maxParam": "110", "machineId": 133, "isPrivate": true}, {"id": 84229, "name": "Priming Quantity - Soda Bib", "macro": "EEP_BIB_PRIME_QTY_SODA", "description": "Priming quantity (expressed in 1/20th oz increments) for the soda bib", "address": 120, "boardName": "Nozzle", "boardId": 560, "paramType": "WORD", "defaultParam": "40", "currentValue": "65535", "newValue": null, "minParam": "40", "maxParam": "400", "machineId": 133, "isPrivate": true}, {"id": 84230, "name": "Priming Quantity - T1", "macro": "EEP_PRIMING_QTY_0", "description": "Priming quantity (expressed in 1/20 fl. oz. increments) for ingredient T1", "address": 84, "boardName": "Pump Board 13", "boardId": 13, "paramType": "BYTE", "defaultParam": "40", "currentValue": "255", "newValue": null, "minParam": "40", "maxParam": "110", "machineId": 133, "isPrivate": true}, {"id": 84231, "name": "Priming Quantity - T3", "macro": "EEP_PRIMING_QTY_2", "description": "Priming quantity (expressed in 1/20 fl. oz. increments) for ingredient T3", "address": 116, "boardName": "Pump Board 13", "boardId": 13, "paramType": "BYTE", "defaultParam": "40", "currentValue": "255", "newValue": null, "minParam": "40", "maxParam": "110", "machineId": 133, "isPrivate": true}, {"id": 84232, "name": "Priming Quantity - T4", "macro": "EEP_PRIMING_QTY_3", "description": "Priming quantity (expressed in 1/20 fl. oz. increments) for ingredient T4", "address": 132, "boardName": "Pump Board 13", "boardId": 13, "paramType": "BYTE", "defaultParam": "40", "currentValue": "255", "newValue": null, "minParam": "40", "maxParam": "110", "machineId": 133, "isPrivate": true}, {"id": 84233, "name": "Priming Quantity - T5", "macro": "EEP_PRIMING_QTY_0", "description": "Priming quantity (expressed in 1/20 fl. oz. increments) for ingredient A5", "address": 84, "boardName": "Pump Board 14", "boardId": 14, "paramType": "BYTE", "defaultParam": "40", "currentValue": "255", "newValue": null, "minParam": "40", "maxParam": "110", "machineId": 133, "isPrivate": true}, {"id": 84234, "name": "Priming Quantity - T6", "macro": "EEP_PRIMING_QTY_1", "description": "Priming quantity (expressed in 1/20 fl. oz. increments) for ingredient T6", "address": 100, "boardName": "Pump Board 14", "boardId": 14, "paramType": "BYTE", "defaultParam": "40", "currentValue": "255", "newValue": null, "minParam": "40", "maxParam": "110", "machineId": 133, "isPrivate": true}, {"id": 84235, "name": "Priming Quantity - T7", "macro": "EEP_PRIMING_QTY_2", "description": "Priming quantity (expressed in 1/20 fl. oz. increments) for ingredient T7", "address": 116, "boardName": "Pump Board 14", "boardId": 14, "paramType": "BYTE", "defaultParam": "65", "currentValue": "255", "newValue": null, "minParam": "40", "maxParam": "90", "machineId": 133, "isPrivate": true}, {"id": 84236, "name": "Priming Quantity - T8", "macro": "EEP_PRIMING_QTY_3", "description": "Priming quantity (expressed in 1/20 fl. oz. increments) for ingredient T8", "address": 132, "boardName": "Pump Board 14", "boardId": 14, "paramType": "BYTE", "defaultParam": "58", "currentValue": "255", "newValue": null, "minParam": "40", "maxParam": "90", "machineId": 133, "isPrivate": true}, {"id": 84237, "name": "Priming Quantity - Water Bib", "macro": "EEP_BIB_PRIME_QTY_WATER", "description": "Priming quantity (expressed in 1/20th oz increments) for the water bib", "address": 122, "boardName": "Nozzle", "boardId": 560, "paramType": "WORD", "defaultParam": "40", "currentValue": "65535", "newValue": null, "minParam": "40", "maxParam": "400", "machineId": 133, "isPrivate": true}, {"id": 84238, "name": "Priming Quantity -T2", "macro": "EEP_PRIMING_QTY_1", "description": "Priming quantity (expressed in 1/20 fl. oz. increments) for ingredient T2", "address": 100, "boardName": "Pump Board 13", "boardId": 13, "paramType": "BYTE", "defaultParam": "40", "currentValue": "255", "newValue": null, "minParam": "40", "maxParam": "110", "machineId": 133, "isPrivate": true}, {"id": 84239, "name": "Refresh Batching Enable", "macro": "EEP_ENABLE_REFRESH_BATCHING ", "description": "Refreshes happen max once/hr", "address": 368, "boardName": "Master", "boardId": 65535, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 133, "isPrivate": false}, {"id": 84240, "name": "Refresh Line Timeout - A1", "macro": "EEP_REFRESH_TIMEOUT_0", "description": "Time (in seconds) this ingredients can remain in the lines without any dispensing operations. Set to 0 for no timeout.", "address": 336, "boardName": "Pump Board 7", "boardId": 7, "paramType": "WORD", "defaultParam": "0", "currentValue": "3600", "newValue": null, "minParam": "0", "maxParam": "10800", "machineId": 133, "isPrivate": true}, {"id": 84241, "name": "Refresh Line Timeout - A2", "macro": "EEP_REFRESH_TIMEOUT_2", "description": "Time (in seconds) this ingredients can remain in the lines without any dispensing operations. Set to 0 for no timeout.", "address": 340, "boardName": "Pump Board 7", "boardId": 7, "paramType": "WORD", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "10800", "machineId": 133, "isPrivate": true}, {"id": 84242, "name": "Refresh Line Timeout - A3", "macro": "EEP_REFRESH_TIMEOUT_0", "description": "Time (in seconds) this ingredients can remain in the lines without any dispensing operations. Set to 0 for no timeout.", "address": 336, "boardName": "Pump Board 8", "boardId": 8, "paramType": "WORD", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "10800", "machineId": 133, "isPrivate": true}, {"id": 84243, "name": "Refresh Line Timeout - A4", "macro": "EEP_REFRESH_TIMEOUT_2", "description": "Time (in seconds) this ingredients can remain in the lines without any dispensing operations. Set to 0 for no timeout.", "address": 340, "boardName": "Pump Board 8", "boardId": 8, "paramType": "WORD", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "10800", "machineId": 133, "isPrivate": true}, {"id": 84244, "name": "Refresh Line Timeout - A5", "macro": "EEP_REFRESH_TIMEOUT_0", "description": "Time (in seconds) this ingredients can remain in the lines without any dispensing operations. Set to 0 for no timeout.", "address": 336, "boardName": "Pump Board 9", "boardId": 9, "paramType": "WORD", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "10800", "machineId": 133, "isPrivate": true}, {"id": 84245, "name": "Refresh Line Timeout - A6", "macro": "EEP_REFRESH_TIMEOUT_2", "description": "Time (in seconds) this ingredients can remain in the lines without any dispensing operations. Set to 0 for no timeout.", "address": 340, "boardName": "Pump Board 9", "boardId": 9, "paramType": "WORD", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "10800", "machineId": 133, "isPrivate": true}, {"id": 84246, "name": "Refresh Line Timeout - B1", "macro": "EEP_REFRESH_TIMEOUT_1", "description": "Time (in seconds) this ingredients can remain in the lines without any dispensing operations. Set to 0 for no timeout.", "address": 338, "boardName": "Pump Board 7", "boardId": 7, "paramType": "WORD", "defaultParam": "0", "currentValue": "3600", "newValue": null, "minParam": "0", "maxParam": "10800", "machineId": 133, "isPrivate": true}, {"id": 84247, "name": "Refresh Line Timeout - B2", "macro": "EEP_REFRESH_TIMEOUT_3", "description": "Time (in seconds) this ingredients can remain in the lines without any dispensing operations. Set to 0 for no timeout.", "address": 342, "boardName": "Pump Board 7", "boardId": 7, "paramType": "WORD", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "10800", "machineId": 133, "isPrivate": true}, {"id": 84248, "name": "Refresh Line Timeout - B3", "macro": "EEP_REFRESH_TIMEOUT_1", "description": "Time (in seconds) this ingredients can remain in the lines without any dispensing operations. Set to 0 for no timeout.", "address": 338, "boardName": "Pump Board 8", "boardId": 8, "paramType": "WORD", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "10800", "machineId": 133, "isPrivate": true}, {"id": 84249, "name": "Refresh Line Timeout - B4", "macro": "EEP_REFRESH_TIMEOUT_3", "description": "Time (in seconds) this ingredients can remain in the lines without any dispensing operations. Set to 0 for no timeout.", "address": 342, "boardName": "Pump Board 8", "boardId": 8, "paramType": "WORD", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "10800", "machineId": 133, "isPrivate": true}, {"id": 84250, "name": "Refresh Line Timeout - B5", "macro": "EEP_REFRESH_TIMEOUT_1", "description": "Time (in seconds) this ingredients can remain in the lines without any dispensing operations. Set to 0 for no timeout.", "address": 338, "boardName": "Pump Board 9", "boardId": 9, "paramType": "WORD", "defaultParam": "0", "currentValue": "3600", "newValue": null, "minParam": "0", "maxParam": "10800", "machineId": 133, "isPrivate": true}, {"id": 84251, "name": "Refresh Line Timeout - B6", "macro": "EEP_REFRESH_TIMEOUT_3", "description": "Time (in seconds) this ingredients can remain in the lines without any dispensing operations. Set to 0 for no timeout.", "address": 342, "boardName": "Pump Board 9", "boardId": 9, "paramType": "WORD", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "10800", "machineId": 133, "isPrivate": true}, {"id": 84252, "name": "Refresh Line Timeout - C1", "macro": "EEP_REFRESH_TIMEOUT_0", "description": "Time (in seconds) this ingredients can remain in the lines without any dispensing operations. Set to 0 for no timeout.", "address": 336, "boardName": "Pump Board 0", "boardId": 0, "paramType": "WORD", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "10800", "machineId": 133, "isPrivate": true}, {"id": 84253, "name": "Refresh Line Timeout - C2", "macro": "EEP_REFRESH_TIMEOUT_0", "description": "Time (in seconds) this ingredients can remain in the lines without any dispensing operations. Set to 0 for no timeout.", "address": 336, "boardName": "Pump Board 1", "boardId": 1, "paramType": "WORD", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "10800", "machineId": 133, "isPrivate": true}, {"id": 84254, "name": "Refresh Line Timeout - C3", "macro": "EEP_REFRESH_TIMEOUT_0", "description": "Time (in seconds) this ingredients can remain in the lines without any dispensing operations. Set to 0 for no timeout.", "address": 336, "boardName": "Pump Board 2", "boardId": 2, "paramType": "WORD", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "10800", "machineId": 133, "isPrivate": true}, {"id": 84255, "name": "Refresh Line Timeout - C4", "macro": "EEP_REFRESH_TIMEOUT_0", "description": "Time (in seconds) this ingredients can remain in the lines without any dispensing operations. Set to 0 for no timeout.", "address": 336, "boardName": "Pump Board 3", "boardId": 3, "paramType": "WORD", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "10800", "machineId": 133, "isPrivate": true}, {"id": 84256, "name": "Refresh Line Timeout - C5", "macro": "EEP_REFRESH_TIMEOUT_0", "description": "Time (in seconds) this ingredients can remain in the lines without any dispensing operations. Set to 0 for no timeout.", "address": 336, "boardName": "Pump Board 4", "boardId": 4, "paramType": "WORD", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "10800", "machineId": 133, "isPrivate": true}, {"id": 84257, "name": "Refresh Line Timeout - C6", "macro": "EEP_REFRESH_TIMEOUT_0", "description": "Time (in seconds) this ingredients can remain in the lines without any dispensing operations. Set to 0 for no timeout.", "address": 336, "boardName": "Pump Board 5", "boardId": 5, "paramType": "WORD", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "10800", "machineId": 133, "isPrivate": true}, {"id": 84258, "name": "Refresh Line Timeout - D1", "macro": "EEP_REFRESH_TIMEOUT_1", "description": "Time (in seconds) this ingredients can remain in the lines without any dispensing operations. Set to 0 for no timeout.", "address": 338, "boardName": "Pump Board 0", "boardId": 0, "paramType": "WORD", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "10800", "machineId": 133, "isPrivate": true}, {"id": 84259, "name": "Refresh Line Timeout - D2", "macro": "EEP_REFRESH_TIMEOUT_1", "description": "Time (in seconds) this ingredients can remain in the lines without any dispensing operations. Set to 0 for no timeout.", "address": 338, "boardName": "Pump Board 1", "boardId": 1, "paramType": "WORD", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "10800", "machineId": 133, "isPrivate": true}, {"id": 84260, "name": "Refresh Line Timeout - D3", "macro": "EEP_REFRESH_TIMEOUT_1", "description": "Time (in seconds) this ingredients can remain in the lines without any dispensing operations. Set to 0 for no timeout.", "address": 338, "boardName": "Pump Board 2", "boardId": 2, "paramType": "WORD", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "10800", "machineId": 133, "isPrivate": true}, {"id": 84261, "name": "Refresh Line Timeout - D4", "macro": "EEP_REFRESH_TIMEOUT_1", "description": "Time (in seconds) this ingredients can remain in the lines without any dispensing operations. Set to 0 for no timeout.", "address": 338, "boardName": "Pump Board 3", "boardId": 3, "paramType": "WORD", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "10800", "machineId": 133, "isPrivate": true}, {"id": 84262, "name": "Refresh Line Timeout - D5", "macro": "EEP_REFRESH_TIMEOUT_1", "description": "Time (in seconds) this ingredients can remain in the lines without any dispensing operations. Set to 0 for no timeout.", "address": 338, "boardName": "Pump Board 4", "boardId": 4, "paramType": "WORD", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "10800", "machineId": 133, "isPrivate": true}, {"id": 84263, "name": "Refresh Line Timeout - D6", "macro": "EEP_REFRESH_TIMEOUT_1", "description": "Time (in seconds) this ingredients can remain in the lines without any dispensing operations. Set to 0 for no timeout.", "address": 338, "boardName": "Pump Board 5", "boardId": 5, "paramType": "WORD", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "10800", "machineId": 133, "isPrivate": true}, {"id": 84264, "name": "Refresh Line Timeout - E1", "macro": "EEP_REFRESH_TIMEOUT_2", "description": "Time (in seconds) this ingredients can remain in the lines without any dispensing operations. Set to 0 for no timeout.", "address": 340, "boardName": "Pump Board 0", "boardId": 0, "paramType": "WORD", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "10800", "machineId": 133, "isPrivate": true}, {"id": 84265, "name": "Refresh Line Timeout - E2", "macro": "EEP_REFRESH_TIMEOUT_2", "description": "Time (in seconds) this ingredients can remain in the lines without any dispensing operations. Set to 0 for no timeout.", "address": 340, "boardName": "Pump Board 1", "boardId": 1, "paramType": "WORD", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "10800", "machineId": 133, "isPrivate": true}, {"id": 84266, "name": "Refresh Line Timeout - E3", "macro": "EEP_REFRESH_TIMEOUT_2", "description": "Time (in seconds) this ingredients can remain in the lines without any dispensing operations. Set to 0 for no timeout.", "address": 340, "boardName": "Pump Board 2", "boardId": 2, "paramType": "WORD", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "10800", "machineId": 133, "isPrivate": true}, {"id": 84267, "name": "Refresh Line Timeout - E4", "macro": "EEP_REFRESH_TIMEOUT_2", "description": "Time (in seconds) this ingredients can remain in the lines without any dispensing operations. Set to 0 for no timeout.", "address": 340, "boardName": "Pump Board 3", "boardId": 3, "paramType": "WORD", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "10800", "machineId": 133, "isPrivate": true}, {"id": 84268, "name": "Refresh Line Timeout - E5", "macro": "EEP_REFRESH_TIMEOUT_2", "description": "Time (in seconds) this ingredients can remain in the lines without any dispensing operations. Set to 0 for no timeout.", "address": 340, "boardName": "Pump Board 4", "boardId": 4, "paramType": "WORD", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "10800", "machineId": 133, "isPrivate": true}, {"id": 84269, "name": "Refresh Line Timeout - E6", "macro": "EEP_REFRESH_TIMEOUT_2", "description": "Time (in seconds) this ingredients can remain in the lines without any dispensing operations. Set to 0 for no timeout.", "address": 340, "boardName": "Pump Board 5", "boardId": 5, "paramType": "WORD", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "10800", "machineId": 133, "isPrivate": true}, {"id": 84270, "name": "Refresh Line Timeout - F1", "macro": "EEP_REFRESH_TIMEOUT_3", "description": "Time (in seconds) this ingredients can remain in the lines without any dispensing operations. Set to 0 for no timeout.", "address": 342, "boardName": "Pump Board 0", "boardId": 0, "paramType": "WORD", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "10800", "machineId": 133, "isPrivate": true}, {"id": 84271, "name": "Refresh Line Timeout - F2", "macro": "EEP_REFRESH_TIMEOUT_3", "description": "Time (in seconds) this ingredients can remain in the lines without any dispensing operations. Set to 0 for no timeout.", "address": 342, "boardName": "Pump Board 1", "boardId": 1, "paramType": "WORD", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "10800", "machineId": 133, "isPrivate": true}, {"id": 84272, "name": "Refresh Line Timeout - F3", "macro": "EEP_REFRESH_TIMEOUT_3", "description": "Time (in seconds) this ingredients can remain in the lines without any dispensing operations. Set to 0 for no timeout.", "address": 342, "boardName": "Pump Board 2", "boardId": 2, "paramType": "WORD", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "10800", "machineId": 133, "isPrivate": true}, {"id": 84273, "name": "Refresh Line Timeout - F4", "macro": "EEP_REFRESH_TIMEOUT_3", "description": "Time (in seconds) this ingredients can remain in the lines without any dispensing operations. Set to 0 for no timeout.", "address": 342, "boardName": "Pump Board 3", "boardId": 3, "paramType": "WORD", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "10800", "machineId": 133, "isPrivate": true}, {"id": 84274, "name": "Refresh Line Timeout - F5", "macro": "EEP_REFRESH_TIMEOUT_3", "description": "Time (in seconds) this ingredients can remain in the lines without any dispensing operations. Set to 0 for no timeout.", "address": 342, "boardName": "Pump Board 4", "boardId": 4, "paramType": "WORD", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "10800", "machineId": 133, "isPrivate": true}, {"id": 84275, "name": "Refresh Line Timeout - F6", "macro": "EEP_REFRESH_TIMEOUT_3", "description": "Time (in seconds) this ingredients can remain in the lines without any dispensing operations. Set to 0 for no timeout.", "address": 342, "boardName": "Pump Board 5", "boardId": 5, "paramType": "WORD", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "10800", "machineId": 133, "isPrivate": true}, {"id": 84276, "name": "Refresh Line Timeout - T1", "macro": "EEP_REFRESH_TIMEOUT_0", "description": "Time (in seconds) this ingredients can remain in the lines without any dispensing operations. Set to 0 for no timeout.", "address": 336, "boardName": "Pump Board 13", "boardId": 13, "paramType": "WORD", "defaultParam": "0", "currentValue": "65535", "newValue": null, "minParam": "0", "maxParam": "10800", "machineId": 133, "isPrivate": true}, {"id": 84277, "name": "Refresh Line Timeout - T2", "macro": "EEP_REFRESH_TIMEOUT_1", "description": "Time (in seconds) this ingredients can remain in the lines without any dispensing operations. Set to 0 for no timeout.", "address": 338, "boardName": "Pump Board 13", "boardId": 13, "paramType": "WORD", "defaultParam": "0", "currentValue": "65535", "newValue": null, "minParam": "0", "maxParam": "10800", "machineId": 133, "isPrivate": true}, {"id": 84278, "name": "Refresh Line Timeout - T3", "macro": "EEP_REFRESH_TIMEOUT_2", "description": "Time (in seconds) this ingredients can remain in the lines without any dispensing operations. Set to 0 for no timeout.", "address": 340, "boardName": "Pump Board 13", "boardId": 13, "paramType": "WORD", "defaultParam": "0", "currentValue": "65535", "newValue": null, "minParam": "0", "maxParam": "10800", "machineId": 133, "isPrivate": true}, {"id": 84279, "name": "Refresh Line Timeout - T4", "macro": "EEP_REFRESH_TIMEOUT_3", "description": "Time (in seconds) this ingredients can remain in the lines without any dispensing operations. Set to 0 for no timeout.", "address": 342, "boardName": "Pump Board 13", "boardId": 13, "paramType": "WORD", "defaultParam": "0", "currentValue": "65535", "newValue": null, "minParam": "0", "maxParam": "10800", "machineId": 133, "isPrivate": true}, {"id": 84280, "name": "Refresh Line Timeout - T5", "macro": "EEP_REFRESH_TIMEOUT_0", "description": "Time (in seconds) this ingredients can remain in the lines without any dispensing operations. Set to 0 for no timeout.", "address": 336, "boardName": "Pump Board 14", "boardId": 14, "paramType": "WORD", "defaultParam": "0", "currentValue": "65535", "newValue": null, "minParam": "0", "maxParam": "10800", "machineId": 133, "isPrivate": true}, {"id": 84281, "name": "Refresh Line Timeout - T6", "macro": "EEP_REFRESH_TIMEOUT_1", "description": "Time (in seconds) this ingredients can remain in the lines without any dispensing operations. Set to 0 for no timeout.", "address": 338, "boardName": "Pump Board 14", "boardId": 14, "paramType": "WORD", "defaultParam": "0", "currentValue": "65535", "newValue": null, "minParam": "0", "maxParam": "10800", "machineId": 133, "isPrivate": true}, {"id": 84282, "name": "Refresh Line Timeout - T7", "macro": "EEP_REFRESH_TIMEOUT_2", "description": "Time (in seconds) this ingredients can remain in the lines without any dispensing operations. Set to 0 for no timeout.", "address": 340, "boardName": "Pump Board 14", "boardId": 14, "paramType": "WORD", "defaultParam": "0", "currentValue": "65535", "newValue": null, "minParam": "0", "maxParam": "10800", "machineId": 133, "isPrivate": true}, {"id": 84283, "name": "Refresh Line Timeout - T8", "macro": "EEP_REFRESH_TIMEOUT_3", "description": "Time (in seconds) this ingredients can remain in the lines without any dispensing operations. Set to 0 for no timeout.", "address": 342, "boardName": "Pump Board 14", "boardId": 14, "paramType": "WORD", "defaultParam": "0", "currentValue": "65535", "newValue": null, "minParam": "0", "maxParam": "10800", "machineId": 133, "isPrivate": true}, {"id": 84284, "name": "Refresh Sync Enable", "macro": "EEP_ENABLE_REFRESH_SYNC", "description": "Attempt to set refresh schedule to the top of the hour every hour. If the top of the next hour is unknown, continue to refresh every hour", "address": 369, "boardName": "Master", "boardId": 65535, "paramType": "BOOLEAN", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 133, "isPrivate": false}, {"id": 85181, "name": "Enable Cleaner Tracking", "macro": "EEP_ENABLE_CLEANER_TRACKING", "description": "Enable cleaner tracking using inline paddle sensors", "address": 137, "boardName": "Nozzle", "boardId": 560, "paramType": "BOOLEAN", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 133, "isPrivate": false}, {"id": 85183, "name": "Ingredient number for Water", "macro": "EEP_WATER_INGR_NUM", "description": "Ingredient number for water. Used for self cleaning procedure", "address": 136, "boardName": "Nozzle", "boardId": 560, "paramType": "BYTE", "defaultParam": "45", "currentValue": "45", "newValue": null, "minParam": "40", "maxParam": "47", "machineId": 133, "isPrivate": false}, {"id": 86572, "name": "Calibration Factor - Bib 5", "macro": "EEP_BIB_0_CAL_VAL", "description": "Ingredient calibration factor (Number of ms per fluid oz)", "address": 16, "boardName": "Solenoid", "boardId": 12, "paramType": "WORD", "defaultParam": "2000", "currentValue": "1934", "newValue": null, "minParam": "100", "maxParam": "10000", "machineId": 133, "isPrivate": true}, {"id": 86573, "name": "Calibration Factor - Bib 6", "macro": "EEP_BIB_1_CAL_VAL", "description": "Ingredient calibration factor (Number of ms per fluid oz)", "address": 32, "boardName": "Solenoid", "boardId": 12, "paramType": "WORD", "defaultParam": "2000", "currentValue": "1621", "newValue": null, "minParam": "100", "maxParam": "10000", "machineId": 133, "isPrivate": true}, {"id": 86585, "name": "Calibration Factor - Bib 7", "macro": "EEP_BIB_2_CAL_VAL", "description": "Ingredient calibration factor (Number of ms per fluid oz)", "address": 48, "boardName": "Solenoid", "boardId": 12, "paramType": "WORD", "defaultParam": "2000", "currentValue": "1967", "newValue": null, "minParam": "100", "maxParam": "10000", "machineId": 133, "isPrivate": true}, {"id": 86586, "name": "Calibration Factor - Bib 8", "macro": "EEP_BIB_3_CAL_VAL", "description": "Ingredient calibration factor (Number of ms per fluid oz)", "address": 64, "boardName": "Solenoid", "boardId": 12, "paramType": "WORD", "defaultParam": "2000", "currentValue": "2000", "newValue": null, "minParam": "100", "maxParam": "10000", "machineId": 133, "isPrivate": true}, {"id": 86587, "name": "Calibration Factor - Cleaner", "macro": "EEP_CLEANER_CONCENTRATE_CAL_VAL", "description": "Cleaner calibration factor (Number of pulses per fluid oz)", "address": 128, "boardName": "Nozzle", "boardId": 560, "paramType": "DWORD", "defaultParam": "52720", "currentValue": "4294967295", "newValue": null, "minParam": "9720", "maxParam": "194400", "machineId": 133, "isPrivate": true}, {"id": 86588, "name": "Current Cleaner Concentrate Volume", "macro": "EEP_CLEANER_CONCENTRATE_QTY", "description": "Current volume of cleaner concentrate (expressed in 1/20th oz increments)", "address": 134, "boardName": "Nozzle", "boardId": 560, "paramType": "WORD", "defaultParam": "0", "currentValue": "43585", "newValue": null, "minParam": "0", "maxParam": "65535", "machineId": 133, "isPrivate": true}, {"id": 86589, "name": "Priming Quantity - Bib 5", "macro": "EEP_BIB_PRIME_QTY_0", "description": "Priming quantity (expressed in 1/20th oz increments) for ingredient bib 5", "address": 112, "boardName": "Solenoid", "boardId": 12, "paramType": "WORD", "defaultParam": "40", "currentValue": "40", "newValue": null, "minParam": "40", "maxParam": "400", "machineId": 133, "isPrivate": true}, {"id": 86590, "name": "Priming Quantity - Bib 6", "macro": "EEP_BIB_PRIME_QTY_1", "description": "Priming quantity (expressed in 1/20th oz increments) for ingredient bib 6", "address": 114, "boardName": "Solenoid", "boardId": 12, "paramType": "WORD", "defaultParam": "40", "currentValue": "40", "newValue": null, "minParam": "40", "maxParam": "400", "machineId": 133, "isPrivate": true}, {"id": 86591, "name": "Priming Quantity - Bib 7", "macro": "EEP_BIB_PRIME_QTY_2", "description": "Priming quantity (expressed in 1/20th oz increments) for ingredient bib 7", "address": 116, "boardName": "Solenoid", "boardId": 12, "paramType": "WORD", "defaultParam": "40", "currentValue": "40", "newValue": null, "minParam": "40", "maxParam": "400", "machineId": 133, "isPrivate": true}, {"id": 86592, "name": "Priming Quantity - Bib 8", "macro": "EEP_BIB_PRIME_QTY_3", "description": "Priming quantity (expressed in 1/20th oz increments) for ingredient bib 8", "address": 118, "boardName": "Solenoid", "boardId": 12, "paramType": "WORD", "defaultParam": "40", "currentValue": "40", "newValue": null, "minParam": "40", "maxParam": "400", "machineId": 133, "isPrivate": true}, {"id": 86593, "name": "Priming Quantity - Cleaner", "macro": "EEP_CLEANER_PRIME_QTY", "description": "Priming quantity (expressed in 1/20th oz increments) for ingredient Cleaner", "address": 132, "boardName": "Nozzle", "boardId": 560, "paramType": "WORD", "defaultParam": "40", "currentValue": "65535", "newValue": null, "minParam": "20", "maxParam": "400", "machineId": 133, "isPrivate": false}, {"id": 99038, "name": "Enable Evaporator Plate Temperature Check", "macro": "EEP_ENABLE_EVAP_PLATE_TEMP_CEHCK", "description": "Enable Evaporator Plate temperature measurements to be reported back to the master PCB", "address": 273, "boardName": "Cooling", "boardId": 576, "paramType": "BOOLEAN", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 133, "isPrivate": false}, {"id": 99039, "name": "<PERSON><PERSON><PERSON> (2) Current Maximum", "macro": "EEP_PERIPH_CHAS_FAN2_MAX_CURRENT", "description": "ADC maximum value of normal operation for the external chassis fan (2) (Current = ADC * 5/4096)", "address": 302, "boardName": "Cooling", "boardId": 576, "paramType": "WORD", "defaultParam": "819", "currentValue": "819", "newValue": null, "minParam": "0", "maxParam": "1500", "machineId": 133, "isPrivate": false}, {"id": 99040, "name": "External Fan 4 Current Maximum", "macro": "EEP_PERIPH_EXT_FAN4_MAX_CURRENT", "description": "ADC maximum value of normal operation for the external fan (4) (Current = ADC * 5/4096)", "address": 300, "boardName": "Cooling", "boardId": 576, "paramType": "WORD", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "1500", "machineId": 133, "isPrivate": false}, {"id": 99041, "name": "<PERSON><PERSON><PERSON> (1) Current Maximum", "macro": "EEP_PERIPH_CHAS_FAN1_MAX_CURRENT", "description": "ADC maximum value of normal operation for the external chassis fan (1) (Current = ADC * 5/4096)", "address": 298, "boardName": "Cooling", "boardId": 576, "paramType": "WORD", "defaultParam": "819", "currentValue": "819", "newValue": null, "minParam": "0", "maxParam": "1500", "machineId": 133, "isPrivate": false}, {"id": 99042, "name": "Compressor Fan Current Maximum", "macro": "EEP_PERIPH_COMP_FAN_MAX_CURRENT", "description": "ADC maximum value of normal operation for the fridge’s external compressor fan (Current = ADC * 5/4096)", "address": 296, "boardName": "Cooling", "boardId": 576, "paramType": "WORD", "defaultParam": "819", "currentValue": "819", "newValue": null, "minParam": "0", "maxParam": "1500", "machineId": 133, "isPrivate": false}, {"id": 99043, "name": "Internal Fan (Right) Current Maximum", "macro": "EEP_PERIPH_INT_RGHT_MAX_CURRENT", "description": "ADC maximum value of normal operation for the fridge’s right internal fan (Current = ADC * 5/4096)", "address": 294, "boardName": "Cooling", "boardId": 576, "paramType": "WORD", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "1500", "machineId": 133, "isPrivate": false}, {"id": 99044, "name": "Internal Fan (Left) Current Maximum", "macro": "EEP_PERIPH_INT_LFT_MAX_CURRENT", "description": "ADC maximum value of normal operation for the fridge’s left internal fan (Current = ADC * 5/4096)", "address": 292, "boardName": "Cooling", "boardId": 576, "paramType": "WORD", "defaultParam": "1229", "currentValue": "1229", "newValue": null, "minParam": "0", "maxParam": "1500", "machineId": 133, "isPrivate": false}, {"id": 99045, "name": "Internal Fan (Top) Current Maximum", "macro": "EEP_PERIPH_INT_TOP_MAX_CURRENT", "description": "ADC maximum value of normal operation for the fridge’s top internal fan (Current = ADC * 5/4096)", "address": 290, "boardName": "Cooling", "boardId": 576, "paramType": "WORD", "defaultParam": "1229", "currentValue": "1229", "newValue": null, "minParam": "0", "maxParam": "1500", "machineId": 133, "isPrivate": false}, {"id": 99046, "name": "Compressor Current Maximum", "macro": "EEP_PERIPH_COMPRESSOR_MAX_CURRENT", "description": "ADC maximum value of normal operation for the fridge compressor (Current = ADC * 102.4)", "address": 288, "boardName": "Cooling", "boardId": 576, "paramType": "WORD", "defaultParam": "204", "currentValue": "204", "newValue": null, "minParam": "0", "maxParam": "1500", "machineId": 133, "isPrivate": false}, {"id": 99047, "name": "<PERSON><PERSON><PERSON> (2) Current Minimum", "macro": "EEP_PERIPH_CHAS_FAN2_MIN_CURRENT", "description": "ADC minimum value of normal operation for the external chassis fan (2) (Current = ADC * 5/4096)", "address": 270, "boardName": "Cooling", "boardId": 576, "paramType": "WORD", "defaultParam": "163", "currentValue": "163", "newValue": null, "minParam": "0", "maxParam": "1500", "machineId": 133, "isPrivate": false}, {"id": 99048, "name": "External Fan 4 Current Minimum", "macro": "EEP_PERIPH_EXT_FAN4_MIN_CURRENT", "description": "ADC minimum value of normal operation for the external fan (4) (Current = ADC * 5/4096)", "address": 268, "boardName": "Cooling", "boardId": 576, "paramType": "WORD", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "1500", "machineId": 133, "isPrivate": false}, {"id": 99049, "name": "<PERSON><PERSON><PERSON> (1) Current Minimum", "macro": "EEP_PERIPH_CHAS_FAN1_MIN_CURRENT", "description": "ADC minimum value of normal operation for the external chassis fan (1) (Current = ADC * 5/4096)", "address": 266, "boardName": "Cooling", "boardId": 576, "paramType": "WORD", "defaultParam": "163", "currentValue": "163", "newValue": null, "minParam": "0", "maxParam": "1500", "machineId": 133, "isPrivate": false}, {"id": 99050, "name": "Compressor Fan Current Minimum", "macro": "EEP_PERIPH_COMP_FAN_MIN_CURRENT", "description": "ADC minimum value of normal operation for the fridge’s external compressor fan (Current = ADC * 5/4096)", "address": 264, "boardName": "Cooling", "boardId": 576, "paramType": "WORD", "defaultParam": "327", "currentValue": "327", "newValue": null, "minParam": "0", "maxParam": "1500", "machineId": 133, "isPrivate": false}, {"id": 99051, "name": "Internal Fan (Right) Current Minimum", "macro": "EEP_PERIPH_INT_RGHT_MIN_CURRENT", "description": "ADC minimum value of normal operation for the fridge’s right internal fan (Current = ADC * 5/4096)", "address": 262, "boardName": "Cooling", "boardId": 576, "paramType": "WORD", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "1500", "machineId": 133, "isPrivate": false}, {"id": 99052, "name": "Internal Fan (Left) Current Minimum", "macro": "EEP_PERIPH_INT_LFT_MIN_CURRENT", "description": "ADC minimum value of normal operation for the fridge’s left internal fan (Current = ADC * 5/4096)", "address": 260, "boardName": "Cooling", "boardId": 576, "paramType": "WORD", "defaultParam": "491", "currentValue": "491", "newValue": null, "minParam": "0", "maxParam": "1500", "machineId": 133, "isPrivate": false}, {"id": 99053, "name": "Internal Fan (Top) Current Minimum", "macro": "EEP_PERIPH_INT_TOP_MIN_CURRENT", "description": "ADC minimum value of normal operation for the fridge’s top internal fan (Current = ADC * 5/4096)", "address": 258, "boardName": "Cooling", "boardId": 576, "paramType": "WORD", "defaultParam": "491", "currentValue": "491", "newValue": null, "minParam": "0", "maxParam": "1500", "machineId": 133, "isPrivate": false}, {"id": 99054, "name": "Compressor Current Minimum", "macro": "EEP_PERIPH_COMPRESSOR_MIN_CURRENT", "description": "ADC minimum value of normal operation for the fridge compressor (Current = ADC * 102.4)", "address": 256, "boardName": "Cooling", "boardId": 576, "paramType": "WORD", "defaultParam": "25", "currentValue": "25", "newValue": null, "minParam": "0", "maxParam": "1500", "machineId": 133, "isPrivate": false}, {"id": 99055, "name": "Compressor Minimum Off Time", "macro": "EEP_COMPRESSOR_MIN_OFFTIME", "description": "Timeout used for allowing evaporator plate to thaw before turning the compressor back on. Ignored in machines that have an evaporator plate sensor", "address": 214, "boardName": "Cooling", "boardId": 576, "paramType": "WORD", "defaultParam": "900", "currentValue": "900", "newValue": null, "minParam": "60", "maxParam": "7200", "machineId": 133, "isPrivate": false}, {"id": 99056, "name": "Ingredient Sensor Enable - A5, A6, B5, B6", "macro": "EEP_BUBBLE_DETECT_ENABLE", "description": "Enable the use of ingredient detection sensors for a group of 4 house mix ingredients", "address": 160, "boardName": "Pump Board 9", "boardId": 9, "paramType": "BOOLEAN", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "1", "machineId": 133, "isPrivate": false}, {"id": 99057, "name": "Retraction Quantity C1, D1, E1, F1", "macro": "EEP_RETRACTION_QTY", "description": "Amount to retract (expressed in 1/20 fl. oz. increments) after a forward dispense ", "address": 278, "boardName": "Pump Board 0", "boardId": 0, "paramType": "BYTE", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "20", "machineId": 133, "isPrivate": false}, {"id": 99058, "name": "Retraction Quantity C2, E2, D2, F2", "macro": "EEP_RETRACTION_QTY", "description": "Amount to retract (expressed in 1/20 fl. oz. increments) after a forward dispense ", "address": 278, "boardName": "Pump Board 1", "boardId": 1, "paramType": "BYTE", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "20", "machineId": 133, "isPrivate": false}, {"id": 99059, "name": "Retraction Quantity C3, D3, E3, F3", "macro": "EEP_RETRACTION_QTY", "description": "Amount to retract (expressed in 1/20 fl. oz. increments) after a forward dispense ", "address": 278, "boardName": "Pump Board 2", "boardId": 2, "paramType": "BYTE", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "20", "machineId": 133, "isPrivate": false}, {"id": 99060, "name": "Retraction Quantity C4, D4, E4, F4", "macro": "EEP_RETRACTION_QTY", "description": "Amount to retract (expressed in 1/20 fl. oz. increments) after a forward dispense ", "address": 278, "boardName": "Pump Board 3", "boardId": 3, "paramType": "BYTE", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "20", "machineId": 133, "isPrivate": false}, {"id": 99061, "name": "Retraction Quantity C5, D5, E5, F5", "macro": "EEP_RETRACTION_QTY", "description": "Amount to retract (expressed in 1/20 fl. oz. increments) after a forward dispense ", "address": 278, "boardName": "Pump Board 4", "boardId": 4, "paramType": "BYTE", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "20", "machineId": 133, "isPrivate": false}, {"id": 99062, "name": "Retraction Quantity C6, D6, E6, F6", "macro": "EEP_RETRACTION_QTY", "description": "Amount to retract (expressed in 1/20 fl. oz. increments) after a forward dispense ", "address": 278, "boardName": "Pump Board 5", "boardId": 5, "paramType": "BYTE", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "20", "machineId": 133, "isPrivate": false}, {"id": 99063, "name": "Retraction Quantity A1, A2, B1, B2", "macro": "EEP_RETRACTION_QTY", "description": "Amount to retract (expressed in 1/20 fl. oz. increments) after a forward dispense ", "address": 278, "boardName": "Pump Board 7", "boardId": 7, "paramType": "BYTE", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "20", "machineId": 133, "isPrivate": false}, {"id": 99064, "name": "Retraction Quantity A3, A4, B3, B4", "macro": "EEP_RETRACTION_QTY", "description": "Amount to retract (expressed in 1/20 fl. oz. increments) after a forward dispense ", "address": 278, "boardName": "Pump Board 8", "boardId": 8, "paramType": "BYTE", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "20", "machineId": 133, "isPrivate": false}, {"id": 99065, "name": "Retraction Quantity A5, A6, B5, B6", "macro": "EEP_RETRACTION_QTY", "description": "Amount to retract (expressed in 1/20 fl. oz. increments) after a forward dispense ", "address": 278, "boardName": "Pump Board 9", "boardId": 9, "paramType": "BYTE", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "20", "machineId": 133, "isPrivate": false}, {"id": 99066, "name": "Startup Prime Setting - C1, D1, E1, F1", "macro": "EEP_STARTUP_PRIME_MODE", "description": "Setting to indicate what ingredients to prime at startup.\n0: Prime Shelf Stable ingredients ONLY\n1: Prime ALL ingredients regardless of refresh timer\n2: Leave ALL ingredients UNPRIMED", "address": 306, "boardName": "Pump Board 0", "boardId": 0, "paramType": "BYTE", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "2", "machineId": 133, "isPrivate": false}, {"id": 99067, "name": "Startup Prime Setting - C2, E2, D2, F2", "macro": "EEP_STARTUP_PRIME_MODE", "description": "Setting to indicate what ingredients to prime at startup.\n0: Prime Shelf Stable ingredients ONLY\n1: Prime ALL ingredients regardless of refresh timer\n2: Leave ALL ingredients UNPRIMED", "address": 306, "boardName": "Pump Board 1", "boardId": 1, "paramType": "BYTE", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "2", "machineId": 133, "isPrivate": false}, {"id": 99068, "name": "Startup Prime Setting - C3, D3, E3, F3", "macro": "EEP_STARTUP_PRIME_MODE", "description": "Setting to indicate what ingredients to prime at startup.\n0: Prime Shelf Stable ingredients ONLY\n1: Prime ALL ingredients regardless of refresh timer\n2: Leave ALL ingredients UNPRIMED", "address": 306, "boardName": "Pump Board 2", "boardId": 2, "paramType": "BYTE", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "2", "machineId": 133, "isPrivate": false}, {"id": 99069, "name": "Startup Prime Setting - C4, D4, E4, F4", "macro": "EEP_STARTUP_PRIME_MODE", "description": "Setting to indicate what ingredients to prime at startup.\n0: Prime Shelf Stable ingredients ONLY\n1: Prime ALL ingredients regardless of refresh timer\n2: Leave ALL ingredients UNPRIMED", "address": 306, "boardName": "Pump Board 3", "boardId": 3, "paramType": "BYTE", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "2", "machineId": 133, "isPrivate": false}, {"id": 99070, "name": "Startup Prime Setting - C5, D5, E5, F5", "macro": "EEP_STARTUP_PRIME_MODE", "description": "Setting to indicate what ingredients to prime at startup.\n0: Prime Shelf Stable ingredients ONLY\n1: Prime ALL ingredients regardless of refresh timer\n2: Leave ALL ingredients UNPRIMED", "address": 306, "boardName": "Pump Board 4", "boardId": 4, "paramType": "BYTE", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "2", "machineId": 133, "isPrivate": false}, {"id": 99071, "name": "Startup Prime Setting - C6, D6, E6, F6", "macro": "EEP_STARTUP_PRIME_MODE", "description": "Setting to indicate what ingredients to prime at startup.\n0: Prime Shelf Stable ingredients ONLY\n1: Prime ALL ingredients regardless of refresh timer\n2: Leave ALL ingredients UNPRIMED", "address": 306, "boardName": "Pump Board 5", "boardId": 5, "paramType": "BYTE", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "2", "machineId": 133, "isPrivate": false}, {"id": 99072, "name": "Startup Prime Setting - A1, A2, B1, B2", "macro": "EEP_STARTUP_PRIME_MODE", "description": "Setting to indicate what ingredients to prime at startup.\n0: Prime Shelf Stable ingredients ONLY\n1: Prime ALL ingredients regardless of refresh timer\n2: Leave ALL ingredients UNPRIMED", "address": 306, "boardName": "Pump Board 7", "boardId": 7, "paramType": "BYTE", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "2", "machineId": 133, "isPrivate": false}, {"id": 99073, "name": "Startup Prime Setting - A3, A4, B3, B4", "macro": "EEP_STARTUP_PRIME_MODE", "description": "Setting to indicate what ingredients to prime at startup.\n0: Prime Shelf Stable ingredients ONLY\n1: Prime ALL ingredients regardless of refresh timer\n2: Leave ALL ingredients UNPRIMED", "address": 306, "boardName": "Pump Board 8", "boardId": 8, "paramType": "BYTE", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "2", "machineId": 133, "isPrivate": false}, {"id": 99074, "name": "Startup Prime Setting - A5, A6, B5, B6", "macro": "EEP_STARTUP_PRIME_MODE", "description": "Setting to indicate what ingredients to prime at startup.\n0: Prime Shelf Stable ingredients ONLY\n1: Prime ALL ingredients regardless of refresh timer\n2: Leave ALL ingredients UNPRIMED", "address": 306, "boardName": "Pump Board 9", "boardId": 9, "paramType": "BYTE", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "2", "machineId": 133, "isPrivate": false}, {"id": 99112, "name": "Retraction Quantity T5 - T8", "macro": "EEP_RETRACTION_QTY", "description": "Amount to retract (expressed in 1/20 fl. oz. increments) after a forward dispense ", "address": 278, "boardName": "Pump Board 14", "boardId": 14, "paramType": "BYTE", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "20", "machineId": 133, "isPrivate": false}, {"id": 99113, "name": "Retraction Quantity T1 - T4", "macro": "EEP_RETRACTION_QTY", "description": "Amount to retract (expressed in 1/20 fl. oz. increments) after a forward dispense ", "address": 278, "boardName": "Pump Board 13", "boardId": 13, "paramType": "BYTE", "defaultParam": "1", "currentValue": "1", "newValue": null, "minParam": "0", "maxParam": "20", "machineId": 133, "isPrivate": false}, {"id": 99114, "name": "Startup Prime Setting - T1 - T4", "macro": "EEP_STARTUP_PRIME_MODE", "description": "Setting to indicate what ingredients to prime at startup.\n0: Prime Shelf Stable ingredients ONLY\n1: Prime ALL ingredients regardless of refresh timer\n2: Leave ALL ingredients UNPRIMED", "address": 306, "boardName": "Pump Board 13", "boardId": 13, "paramType": "BYTE", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "2", "machineId": 133, "isPrivate": false}, {"id": 99115, "name": "Startup Prime Setting - T5 - T8", "macro": "EEP_STARTUP_PRIME_MODE", "description": "Setting to indicate what ingredients to prime at startup.\n0: Prime Shelf Stable ingredients ONLY\n1: Prime ALL ingredients regardless of refresh timer\n2: Leave ALL ingredients UNPRIMED", "address": 306, "boardName": "Pump Board 14", "boardId": 14, "paramType": "BYTE", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "2", "machineId": 133, "isPrivate": false}, {"id": 99116, "name": "Calibration Factor - Cleaner", "macro": "EEP_CLEANER_CAL_VAL", "description": "Cleaner calibration factor (Number of pulses per fluid oz)", "address": 128, "boardName": "Nozzle", "boardId": 560, "paramType": "DWORD", "defaultParam": "52720", "currentValue": "52720", "newValue": null, "minParam": "9720", "maxParam": "194400", "machineId": 133, "isPrivate": false}, {"id": 99117, "name": "Current Cleaner Level", "macro": "EEP_CLEANER_LEVEL", "description": "Current volume of cleaner concentrate (expressed in 1/20th oz increments)", "address": 134, "boardName": "Nozzle", "boardId": 560, "paramType": "WORD", "defaultParam": "0", "currentValue": "0", "newValue": null, "minParam": "0", "maxParam": "65535", "machineId": 133, "isPrivate": true}, {"id": 99118, "name": "Shutdown Prep Volume", "macro": "EEP_SHUTDOWN_PREP_VOL", "description": "The volume of liquid to backtrack before shutdown to ensure there is no cross contamination between ingredients in the nozzle cap (in 1/20th oz increments)", "address": 394, "boardName": "Master", "boardId": 65535, "paramType": "BYTE", "defaultParam": "2", "currentValue": "2", "newValue": null, "minParam": "0", "maxParam": "60", "machineId": 133, "isPrivate": false}, {"id": 99119, "name": "Startup Prep Volume", "macro": "EEP_STARTUP_PREP_VOL", "description": "The volume of liquid to dispense before startup to ensure all ingredients are primed and ready to dispense into the first drink (in 1/20th oz increments)", "address": 395, "boardName": "Master", "boardId": 65535, "paramType": "BYTE", "defaultParam": "7", "currentValue": "7", "newValue": null, "minParam": "0", "maxParam": "60", "machineId": 133, "isPrivate": false}, {"id": 99120, "name": "Cleaning Paddle Target (Water)", "macro": "EEP_CLEANING_PADDLE_TARGET_WATER", "description": "Expected target for average value of water paddle sensor during self cleaning procedure", "address": 162, "boardName": "Nozzle", "boardId": 560, "paramType": "WORD", "defaultParam": "40", "currentValue": "40", "newValue": null, "minParam": "0", "maxParam": "254", "machineId": 133, "isPrivate": false}, {"id": 99121, "name": "Cleaning Paddle Target (Cleaner)", "macro": "EEP_CLEANING_PADDLE_TARGET_CLEANER", "description": "Expected target for average value of cleaner paddle sensor during self cleaning procedure", "address": 160, "boardName": "Nozzle", "boardId": 560, "paramType": "WORD", "defaultParam": "100", "currentValue": "100", "newValue": null, "minParam": "0", "maxParam": "254", "machineId": 133, "isPrivate": false}, {"id": 99122, "name": "Shutdown Prep Quantity", "macro": "EEP_SHUTDOWN_PREP_QTY", "description": "The volume of liquid to backtrack before shutdown to ensure there is no cross contamination between ingredients in the nozzle cap (in 1/20th oz increments)", "address": 394, "boardName": "Master", "boardId": 65535, "paramType": "BYTE", "defaultParam": "2", "currentValue": "2", "newValue": null, "minParam": "0", "maxParam": "60", "machineId": 133, "isPrivate": false}, {"id": 99123, "name": "Startup Prep Quantity", "macro": "EEP_STARTUP_PREP_QTY", "description": "The volume of liquid to dispense before startup to ensure all ingredients are primed and ready to dispense into the first drink (in 1/20th oz increments)", "address": 395, "boardName": "Master", "boardId": 65535, "paramType": "BYTE", "defaultParam": "7", "currentValue": "7", "newValue": null, "minParam": "0", "maxParam": "60", "machineId": 133, "isPrivate": false}]