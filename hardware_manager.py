#!/usr/bin/env python3
import argparse
import json
import os
import sys
import requests
from dotenv import load_dotenv
from enum import Enum
from typing import Dict, List, Optional, Any, Union
import time

# Make sure to run pip install requests python-dotenv

# Load environment variables from .env file
load_dotenv()

class HardwareType(str, Enum):
    HARDWARE = "hardware"
    SUBSYSTEM = "subsystem"
    ADDON = "addon"

class ParamType(str, Enum):
    BOOLEAN = "BOOLEAN"
    BYTE = "BYTE"
    SINGLE = "SINGLE"
    STRING = "STRING"
    WORD = "WORD"

class SettingParamType(str, Enum):
    BOOLEAN = "BOOLEAN"
    BYTE = "BYTE"
    WORD = "WORD"
    DWORD = "DWORD"
    SINGLE = "SINGLE"

class HardwareManager:
    def __init__(self, api_key: str = None, api_endpoint: str = None):
        self.api_key = api_key or os.getenv("API_KEY")
        self.api_endpoint = api_endpoint or os.getenv("API_ENDPOINT")
        self.auth_token = os.getenv("AUTH_TOKEN")
        
        if not self.api_key:
            raise ValueError("API key is required. Set it in .env file or pass as argument.")
        
        if not self.api_endpoint:
            raise ValueError("API endpoint is required. Set it in .env file or pass as argument.")
        
        # Remove trailing slash if present
        if self.api_endpoint.endswith('/'):
            self.api_endpoint = self.api_endpoint[:-1]
        
        self.headers = {
            "x-api-key": self.api_key,
            "Content-Type": "application/json",
            "Authorization" : str(self.auth_token)
        }
    
    def _make_request(self, method: str, path: str, data: Dict = None) -> Dict:
        """Make a request to the API"""
        url = f"{self.api_endpoint}{path}"

        retries = 5
        while retries > 0:
            try:
                if method.upper() == "GET":
                    response = requests.get(url, headers=self.headers, timeout=15)
                elif method.upper() == "POST":
                    response = requests.post(url, headers=self.headers, json=data, timeout=15)
                elif method.upper() == "PUT":
                    response = requests.put(url, headers=self.headers, json=data, timeout=15)
                elif method.upper() == "DELETE":
                    response = requests.delete(url, headers=self.headers, timeout=15)
                else:
                    raise ValueError(f"Unsupported HTTP method: {method}")
                
                response.raise_for_status()
                
                if response.status_code == 204:  # No content
                    return {"success": True}
                
                return response.json()
            except requests.exceptions.RequestException as e:
                print(f"Error making request: {e}")
                if hasattr(e, 'response') and e.response is not None:
                    try:
                        error_data = e.response.json()
                        print(f"API Error: {error_data}")
                    except:
                        print(f"API Error: {e.response.text}")
                    retries -= 1
                    if retries == 0:
                        print("Max retries reached!")
                    else:
                        print(f"Retrying in {retries} seconds...")
                        time.sleep(retries)
    
    # Hardware methods
    def list_hardware(self, include_settings: bool = False, machine_id: int = None, model_id: int = None, all_revisions: bool = False, include_profile_values: bool = False, include_boards: bool = False) -> List[Dict]:
        """List all hardware"""
        # Build query parameters
        params = {}
        if include_settings:
            params["includeSettings"] = "true"
        if all_revisions:
            params["allRevisions"] = "true"
        if include_profile_values:
            params["includeProfileValues"] = "true"
        if include_boards:
            params["includeBoards"] = "true"
        
        # Convert params to query string
        query_string = "&".join([f"{k}={v}" for k, v in params.items()])
        
        if machine_id:
            return self._make_request("GET", f"/hardware/machine/{machine_id}?{query_string}")
        elif model_id:
            return self._make_request("GET", f"/hardware/bymodel/{model_id}?{query_string}")
        else:
            return self._make_request("GET", f"/hardware?{query_string}")
    
    def get_hardware(self, hardware_id: int, include_settings: bool = False, include_profile_values: bool = False) -> Dict:
        """Get a specific hardware"""
        # Build query parameters
        params = {}
        if include_settings:
            params["includeSettings"] = "true"
        if include_profile_values:
            params["includeProfileValues"] = "true"
            
        # Convert params to query string
        query_string = "&".join([f"{k}={v}" for k, v in params.items()])
        
        return self._make_request("GET", f"/hardware/{hardware_id}?{query_string}")
    
    def create_hardware(self, name: str, hardware_type: str, rev: str = "A", description: str = None, settings: List[Dict] = None) -> Dict:
        """Create a new hardware"""
        data = {
            "name": name,
            "type": hardware_type,
            "rev": rev,
            "description": description
        }
        
        if settings:
            data["settings"] = settings
        
        return self._make_request("POST", "/hardware", data)
    
    def update_hardware(self, hardware_id: int, name: str, hardware_type: str, rev: str = "A", description: str = None) -> Dict:
        """Update an existing hardware"""
        data = {
            "name": name,
            "type": hardware_type,
            "rev": rev,
            "description": description
        }
        
        return self._make_request("PUT", f"/hardware/{hardware_id}", data)
    
    def delete_hardware(self, hardware_id: int) -> Dict:
        """Delete a hardware after removing all associated boards"""
        # First, get all boards for this hardware
        try:
            boards = self.list_hardware_boards(hardware_id)
            
            # Delete each board first
            for board in boards:
                self.delete_hardware_board(board['id'])
                print(f"Deleted board ID: {board['id']}")
            
            # Now delete the hardware
            return self._make_request("DELETE", f"/hardware/{hardware_id}")
        except Exception as e:
            print(f"Error deleting hardware: {e}")
            raise
    
    # Hardware Settings methods
    def list_hardware_settings(self, hardware_id: int) -> List[Dict]:
        """List all settings for a hardware"""
        return self._make_request("GET", f"/hardware/{hardware_id}/settings")
    
    def create_hardware_setting(self, hardware_id: int, name: str, macro: str, description: str, 
                               address: int, board_name: str, board_id: int, param_type: str,
                               default_param: str, min_param: str, max_param: str, is_private: bool = False) -> Dict:
        """Create a new hardware setting"""
        data = {
            "hardwareId": hardware_id,
            "name": name,
            "macro": macro,
            "description": description,
            "address": address,
            "boardName": board_name,
            "boardId": board_id,
            "paramType": param_type,
            "defaultParam": default_param,
            "minParam": min_param,
            "maxParam": max_param,
            "isPrivate": is_private
        }
        
        return self._make_request("POST", "/hardware/settings", data)
    
    def update_hardware_setting(self, setting_id: int, hardware_id: int, name: str, macro: str, description: str, 
                               address: int, board_name: str, board_id: int, param_type: str,
                               default_param: str, min_param: str, max_param: str, is_private: bool = False) -> Dict:
        """Update an existing hardware setting"""
        data = {
            "hardwareId": hardware_id,
            "name": name,
            "macro": macro,
            "description": description,
            "address": address,
            "boardName": board_name,
            "boardId": board_id,
            "paramType": param_type,
            "defaultParam": default_param,
            "minParam": min_param,
            "maxParam": max_param,
            "isPrivate": is_private
        }
        
        return self._make_request("PUT", f"/hardware/settings/{setting_id}", data)
    
    def delete_hardware_setting(self, setting_id: int) -> Dict:
        """Delete a hardware setting"""
        return self._make_request("DELETE", f"/hardware/settings/{setting_id}")
    
    # Machine Hardware methods
    def add_hardware_to_machine(self, machine_id: int, hardware_id: int, from_model: bool = False) -> Dict:
        """Add hardware to a machine"""
        data = {
            "hardwareId": hardware_id,
            "fromModel": from_model
        }
        
        return self._make_request("POST", f"/hardware/machine/{machine_id}/add", data)
    
    def remove_hardware_from_machine(self, machine_id: int, hardware_id: int) -> Dict:
        """Remove hardware from a machine"""
        return self._make_request("DELETE", f"/hardware/machine/{machine_id}/hardware/{hardware_id}")
    
    # Model Hardware methods
    def add_hardware_to_model(self, model_id: int, hardware_id: int) -> Dict:
        """Add hardware to a model"""
        data = {
            "hardwareId": hardware_id
        }
        
        return self._make_request("POST", f"/hardware/model/{model_id}/add", data)

    # Add a method to list models for a hardware
    def list_models_for_hardware(self, hardware_id: int) -> List[Dict]:
        """List all models that a hardware is associated with"""
        return self._make_request("GET", f"/hardware/{hardware_id}/models")

    # Add a method to remove hardware from a model
    def remove_hardware_from_model(self, model_id: int, hardware_id: int) -> Dict:
        """Remove hardware from a model"""
        return self._make_request("DELETE", f"/hardware/model/{model_id}/hardware/{hardware_id}")

    def create_revision(self, hardware_id: int) -> Dict:
        """Create a new revision of an existing hardware"""
        return self._make_request("POST", f"/hardware/{hardware_id}/revision")

    def get_revisions(self, hardware_id: int) -> List[Dict]:
        """Get all revisions for a specific hardware"""
        return self._make_request("GET", f"/hardware/{hardware_id}/revisions")

    # Hardware Profile Values methods
    def list_hardware_profile_values(self, hardware_id: int) -> List[Dict]:
        """List all profile values for a hardware"""
        return self._make_request("GET", f"/hardware/{hardware_id}/profilevalues")
    
    def create_hardware_profile_value(self, hardware_id: int, name: str, macro: str, description: str, 
                                     param_type: str, default_value: str, min_value: str, max_value: str, 
                                     category: str = "", security_level: int = 1) -> Dict:
        """Create a new hardware profile value"""
        data = {
            "hardwareId": hardware_id,
            "name": name,
            "macro": macro,
            "description": description,
            "paramType": param_type,
            "defaultValue": default_value,
            "minValue": min_value,
            "maxValue": max_value,
            "category": category,
            "securityLevel": security_level
        }
        
        return self._make_request("POST", "/hardware/profilevalues", data)
    
    def update_hardware_profile_value(self, profile_value_id: int, hardware_id: int, name: str, macro: str, 
                                     description: str, param_type: str, default_value: str, min_value: str, 
                                     max_value: str, category: str = "", security_level: int = 1) -> Dict:
        """Update an existing hardware profile value"""
        data = {
            "hardwareId": hardware_id,
            "name": name,
            "macro": macro,
            "description": description,
            "paramType": param_type,
            "defaultValue": default_value,
            "minValue": min_value,
            "maxValue": max_value,
            "category": category,
            "securityLevel": security_level
        }
        
        return self._make_request("PUT", f"/hardware/profilevalues/{profile_value_id}", data)
    
    def delete_hardware_profile_value(self, profile_value_id: int) -> Dict:
        """Delete a hardware profile value"""
        return self._make_request("DELETE", f"/hardware/profilevalues/{profile_value_id}")

    def apply_hardware(self, hardware_id: int, machine_id: int = None, all_machines: bool = False) -> Dict:
        """Apply hardware settings and profile values to a machine or all machines

        Args:
            hardware_id: Hardware ID
            machine_id: Machine ID (required if all_machines is False)
            all_machines: Whether to apply to all machines with this hardware

        Returns:
            Dict: Response from the API
        """
        # Build query parameters
        params = {}
        if all_machines:
            params["allMachines"] = "true"
        elif machine_id is not None:
            params["machineId"] = str(machine_id)
        else:
            raise ValueError("Either machine_id or all_machines must be specified")

        # Convert params to query string
        query_string = "&".join([f"{k}={v}" for k, v in params.items()])

        return self._make_request("POST", f"/hardware/{hardware_id}/apply?{query_string}")

    def apply_settings(self, hardware_id: int, machine_id: int) -> Dict:
        """Apply hardware settings to a specific machine

        Args:
            hardware_id: Hardware ID
            machine_id: Machine ID

        Returns:
            Dict: Response from the API
        """
        return self._make_request("POST", f"/hardware/{hardware_id}/machine/{machine_id}/applysettings")

    def apply_boards(self, hardware_id: int, machine_id: int) -> Dict:
        """Apply hardware boards to a specific machine

        Args:
            hardware_id: Hardware ID
            machine_id: Machine ID

        Returns:
            Dict: Response from the API
        """
        return self._make_request("POST", f"/hardware/{hardware_id}/machine/{machine_id}/applyboards")

    def apply_profile_values(self, hardware_id: int, machine_id: int) -> Dict:
        """Apply hardware profile values to a specific machine

        Args:
            hardware_id: Hardware ID
            machine_id: Machine ID

        Returns:
            Dict: Response from the API
        """
        return self._make_request("POST", f"/hardware/{hardware_id}/machine/{machine_id}/applyprofilevalues")

    # Hardware Board methods
    def list_hardware_boards(self, hardware_id: int) -> List[Dict]:
        """List all boards for a hardware"""
        return self._make_request("GET", f"/hardware/{hardware_id}/boards")
    
    def get_machine_info(self, machine_id: int) -> Dict:
        """Get machine info"""
        return self._make_request("GET", f"/machine/{machine_id}")
    
    def update_machine_serial_number(self, machine_id: int, serial_number: str) -> Dict:
        """Update machine serial number"""
        data = {
            "serialNumber": serial_number
        }
        return self._make_request("PUT", f"/machine/{machine_id}", data)

    def get_machine_settings(self, machine_id: int) -> List[Dict]:
        """Get all settings currently on a machine"""
        return self._make_request("GET", f"/settings?machineId={machine_id}")

    def delete_machine_setting(self, setting_id: int) -> Dict:
        """Delete a specific setting from a machine"""
        return self._make_request("DELETE", f"/settings/{setting_id}")

    def audit_machine_settings(self, machine_id: int, interactive: bool = True) -> Dict[str, any]:
        """Audit machine settings against hardware definitions

        Args:
            machine_id: Machine ID to audit
            interactive: Whether to prompt user for actions on mismatched settings

        Returns:
            Dict with audit results and statistics
        """
        print(f"\n🔍 Starting audit for machine ID: {machine_id}")

        # Get machine info
        try:
            machine_info = self.get_machine_info(machine_id)
            machine_name = machine_info.get('name', f'Machine {machine_id}')
            serial_number = machine_info.get('serialNumber', 'Unknown')
            print(f"Machine: {machine_name} (Serial: {serial_number})")
        except Exception as e:
            print(f"❌ Error getting machine info: {e}")
            return {"error": str(e)}

        # Get hardware assigned to this machine
        print("\n📋 Getting hardware assigned to machine...")
        try:
            machine_hardware = self.list_hardware(machine_id=machine_id, include_settings=True)
            if not machine_hardware:
                print("✅ No hardware assigned to this machine")
                return {"status": "no_hardware", "machine_id": machine_id}

            print(f"Found {len(machine_hardware)} hardware items:")
            for hw in machine_hardware:
                print(f"  - {hw['name']} Rev {hw['rev']} [ID: {hw['id']}]")
        except Exception as e:
            print(f"❌ Error getting machine hardware: {e}")
            return {"error": str(e)}

        # Get current settings on the machine
        print("\n⚙️ Getting current settings on machine...")
        try:
            machine_settings = self.get_machine_settings(machine_id)
            print(f"Found {len(machine_settings)} settings on machine")
        except Exception as e:
            print(f"❌ Error getting machine settings: {e}")
            return {"error": str(e)}

        # Build expected settings from hardware
        print("\n🔧 Building expected settings from hardware definitions...")
        expected_settings = {}  # key: (name, address, macro) -> hardware_info

        for hw in machine_hardware:
            hardware_settings = self.list_hardware_settings(hw['id'])
            if hardware_settings:
                for setting in hardware_settings:
                    key = (setting['name'], setting['address'], setting['macro'])
                    expected_settings[key] = {
                        'hardware_name': hw['name'],
                        'hardware_rev': hw['rev'],
                        'hardware_id': hw['id'],
                        'setting': setting
                    }
        print(f"Expected {len(expected_settings)} unique settings from hardware definitions")

        # Compare machine settings with expected settings
        print("\n🔍 Comparing machine settings with hardware definitions...")

        audit_results = {
            "machine_id": machine_id,
            "machine_name": machine_name,
            "serial_number": serial_number,
            "expected_settings": len(expected_settings),
            "actual_settings": len(machine_settings),
            "matching_settings": [],
            "mismatched_settings": [],
            "orphaned_settings": [],
            "missing_settings": [],
            "deleted_settings": [],
            "kept_settings": []
        }

        # Check each machine setting against expected settings
        machine_setting_keys = set()
        for machine_setting in machine_settings:
            try:
                # Safely extract key components with error handling
                name = machine_setting.get('name', 'UNKNOWN')
                address = machine_setting.get('address', 0)
                macro = machine_setting.get('macro', 'UNKNOWN')
                key = (name, address, macro)
                machine_setting_keys.add(key)

                if key in expected_settings:
                    audit_results["matching_settings"].append({
                        "setting": machine_setting,
                        "hardware": expected_settings[key]
                    })
                else:
                    audit_results["orphaned_settings"].append(machine_setting)
            except Exception as e:
                print(f"⚠️ Error processing machine setting: {e}")
                print(f"   Setting data: {machine_setting}")
                audit_results["orphaned_settings"].append(machine_setting)

        # Find missing settings (in hardware but not on machine)
        expected_keys = set(expected_settings.keys())
        missing_keys = expected_keys - machine_setting_keys
        for key in missing_keys:
            audit_results["missing_settings"].append({
                "key": key,
                "hardware": expected_settings[key]
            })

        # Print audit summary
        print(f"\n📊 Audit Summary:")
        print(f"  ✅ Matching settings: {len(audit_results['matching_settings'])}")
        print(f"  ❌ Orphaned settings (on machine but not in hardware): {len(audit_results['orphaned_settings'])}")
        print(f"  ⚠️ Missing settings (in hardware but not on machine): {len(audit_results['missing_settings'])}")

        # Handle orphaned settings interactively
        if audit_results["orphaned_settings"] and interactive:
            print(f"\n🧹 Found {len(audit_results['orphaned_settings'])} orphaned settings:")

            for orphaned_setting in audit_results["orphaned_settings"]:
                print(f"\n❌ Orphaned Setting:")
                # Add safety checks for dictionary access
                if isinstance(orphaned_setting, dict):
                    print(f"   Name: {orphaned_setting.get('name', 'UNKNOWN')}")
                    print(f"   Address: {orphaned_setting.get('address', 'UNKNOWN')}")
                    print(f"   Macro: {orphaned_setting.get('macro', 'UNKNOWN')}")
                    print(f"   ID: {orphaned_setting.get('id', 'UNKNOWN')}")
                else:
                    print(f"   ⚠️ Unexpected data type: {type(orphaned_setting)}")
                    print(f"   Data: {orphaned_setting}")
                    continue

                while True:
                    action = input("   Action: (k)eep, (d)elete, (s)kip remaining: ").lower().strip()

                    if action == 'k':
                        audit_results["kept_settings"].append(orphaned_setting)
                        print("   ✅ Keeping setting")
                        break
                    elif action == 'd':
                        try:
                            if isinstance(orphaned_setting, dict) and 'id' in orphaned_setting:
                                self.delete_machine_setting(orphaned_setting['id'])
                                audit_results["deleted_settings"].append(orphaned_setting)
                                print("   🗑️ Setting deleted")
                            else:
                                print(f"   ❌ Cannot delete setting - invalid data format")
                        except Exception as e:
                            print(f"   ❌ Error deleting setting: {e}")
                        break
                    elif action == 's':
                        print("   ⏭️ Skipping remaining orphaned settings")
                        # Add remaining settings to kept_settings
                        current_index = audit_results["orphaned_settings"].index(orphaned_setting)
                        remaining_settings = audit_results["orphaned_settings"][current_index:]
                        audit_results["kept_settings"].extend(remaining_settings)
                        break
                    else:
                        print("   Invalid choice. Please enter 'k' for keep, 'd' for delete, or 's' to skip remaining.")

                if action == 's':
                    break

        # Print final summary
        print(f"\n🎯 Final Results:")
        print(f"  ✅ Matching settings: {len(audit_results['matching_settings'])}")
        print(f"  🗑️ Deleted orphaned settings: {len(audit_results['deleted_settings'])}")
        print(f"  📌 Kept orphaned settings: {len(audit_results['kept_settings'])}")
        print(f"  ⚠️ Missing settings: {len(audit_results['missing_settings'])}")

        if audit_results["missing_settings"]:
            print(f"\n⚠️ Missing settings (consider re-applying hardware):")
            for missing in audit_results["missing_settings"]:
                key = missing["key"]
                hw = missing["hardware"]
                print(f"   - {key[0]} (Address: {key[1]}, Macro: {key[2]}) from {hw['hardware_name']} Rev {hw['hardware_rev']}")

        return audit_results
    
    def list_machines(self) -> Dict[str, int]:
        """List all machines"""
        machines = self._make_request("GET", "/machine")
        serial_to_admin = {}
        for machine in machines:
            serial_to_admin[str(machine['serialNumber'][-5:])] = str(machine['id'])
        return serial_to_admin
    
    def create_hardware_board(self, hardware_id: int, protocol_id: int, pcb_major: int, pcb_minor: int, pcb_patch: int,
                             application_id: int, type_id: int, pump_rating: int = 0) -> Dict:
        """Create a new hardware board"""
        data = {
            "hardwareId": hardware_id,
            "protocolId": protocol_id,
            "pcbMajor": pcb_major,
            "pcbMinor": pcb_minor,
            "pcbPatch": pcb_patch,
            "pumpRating": pump_rating,
            "applicationId": application_id,
            "typeId": type_id
        }
        
        return self._make_request("POST", "/hardware/boards", data)

    def update_hardware_board(self, board_id: int, hardware_id: int, protocol_id: int, pcb_major: int, pcb_minor: int, pcb_patch: int,
                             application_id: int, type_id: int, pump_rating: int = 0, slot_numbers: str = None) -> Dict:
        """Update an existing hardware board"""
        data = {
            "hardwareId": hardware_id,
            "protocolId": protocol_id,
            "pcbMajor": pcb_major,
            "pcbMinor": pcb_minor,
            "pcbPatch": pcb_patch,
            "pumpRating": pump_rating,
            "applicationId": application_id,
            "typeId": type_id,
            "slotNumbers": slot_numbers
        }
        
        return self._make_request("PUT", f"/hardware/boards/{board_id}", data)

    def delete_hardware_board(self, board_id: int) -> Dict:
        """Delete a hardware board"""
        return self._make_request("DELETE", f"/hardware/boards/{board_id}")

# Interactive functions
def interactive_create_hardware(manager: HardwareManager) -> Dict:
    """Interactive hardware creation"""
    print("=== Create New Hardware ===")
    
    name = input("Hardware Name: ")
    
    # Display hardware type options
    print("\nHardware Types:")
    for i, hw_type in enumerate(HardwareType, 1):
        print(f"{i}. {hw_type.value}")
    
    type_choice = int(input("\nSelect Hardware Type (number): "))
    hardware_type = list(HardwareType)[type_choice-1].value
    
    rev = input("Hardware Revision [A]: ") or "A"
    description = input("Description (optional): ")
    
    # Create the hardware without model association
    hardware = manager.create_hardware(name, hardware_type, rev, description)
    
    print(f"\nHardware created with ID: {hardware['id']}")
    
    # Ask if user wants to add settings
    add_settings = input("\nDo you want to add settings to this hardware? (y/n): ").lower() == 'y'
    
    if add_settings:
        settings = []
        while True:
            setting = interactive_create_hardware_setting(manager, hardware['id'])
            settings.append(setting)
            
            if input("\nAdd another setting? (y/n): ").lower() != 'y':
                break
    
    # Ask if user wants to add profile values
    add_profile_values = input("\nDo you want to add profile values to this hardware? (y/n): ").lower() == 'y'
    
    if add_profile_values:
        profile_values = []
        while True:
            profile_value = interactive_create_hardware_profile_value(manager, hardware['id'])
            profile_values.append(profile_value)
            
            if input("\nAdd another profile value? (y/n): ").lower() != 'y':
                break
    
    # Ask if user wants to associate with a model
    add_to_model = input("\nDo you want to associate this hardware with a model? (y/n): ").lower() == 'y'
    
    if add_to_model:
        model_id = int(input("Model ID: "))
        manager.add_hardware_to_model(model_id, hardware['id'])
        print(f"Hardware associated with model ID: {model_id}")
    
    return hardware

def get_machine_list(manager: HardwareManager) ->Dict[str, int]:
    print("Retrieving list of machines...")

def interactive_create_hardware_setting(manager: HardwareManager, hardware_id: int) -> Dict:
    """Interactive hardware setting creation"""
    print("\n=== Create New Hardware Setting ===")
    
    name = input("Setting Name: ")
    macro = input("Macro Name: ")
    description = input("Description: ")
    
    address = int(input("EEPROM Address: "))
    board_name = input("Board Name: ")
    board_id = int(input("Board ID: "))
    
    # Display parameter type options
    print("\nParameter Type:")
    for i, param_type in enumerate(SettingParamType, 1):
        print(f"{i}. {param_type.value} - {get_param_type_description(param_type.value)}")
    
    type_choice = int(input("\nSelect parameter type (1-5): "))
    param_type = list(SettingParamType)[type_choice-1].value
    
    default_param = input("Default Value: ")
    
    # Handle min/max values based on parameter type
    if param_type == SettingParamType.BOOLEAN.value:
        min_param = "0"
        max_param = "1"
        print("Min/Max values automatically set to 0/1 for BOOLEAN type")
    else:
        min_param = input("Minimum Value: ")
        max_param = input("Maximum Value: ")
    
    is_private = input("Is Private? (y/n): ").lower() == 'y'
    
    # Create the setting
    setting = manager.create_hardware_setting(
        hardware_id, name, macro, description, address, board_name, board_id,
        param_type, default_param, min_param, max_param, is_private
    )
    
    print(f"Setting created with ID: {setting['id']}")
    
    return setting

def get_param_type_description(param_type: str) -> str:
    """Get a description for a parameter type"""
    descriptions = {
        "BOOLEAN": "Boolean (true/false)",
        "BYTE": "8-bit value (0-255)",
        "WORD": "16-bit value (0-65535)",
        "DWORD": "32-bit value (0-4294967295)",
        "SINGLE": "Single precision floating point",
        "STRING": "Text string"
    }
    return descriptions.get(param_type, "")

def interactive_update_hardware(manager: HardwareManager, hardware_id: int) -> Dict:
    """Interactive hardware update"""
    # First get the current hardware
    hardware = manager.get_hardware(hardware_id)
    
    print(f"\n=== Update Hardware ID: {hardware_id} ===")
    print(f"Current values: {json.dumps(hardware, indent=2)}")
    
    name = input(f"Hardware Name [{hardware['name']}]: ") or hardware['name']
    
    # Display hardware type options
    print("\nHardware Types:")
    for i, hw_type in enumerate(HardwareType, 1):
        print(f"{i}. {hw_type.value}")
    
    current_type_index = next((i for i, t in enumerate(HardwareType, 1) if t.value == hardware['type']), 1)
    type_choice = input(f"Select Hardware Type (number) [{current_type_index}]: ") or current_type_index
    hardware_type = list(HardwareType)[int(type_choice)-1].value
    
    rev = input(f"Hardware Revision [{hardware['rev']}]: ") or hardware['rev']
    description = input(f"Description [{hardware.get('description', '')}]: ") or hardware.get('description', '')
    
    # Update the hardware
    updated_hardware = manager.update_hardware(hardware_id, name, hardware_type, rev, description)
    
    print(f"\nHardware updated successfully")
    
    # Ask if user wants to associate with a model
    add_to_model = input("\nDo you want to associate this hardware with a model? (y/n): ").lower() == 'y'
    
    if add_to_model:
        model_id = int(input("Model ID: "))
        manager.add_hardware_to_model(model_id, hardware_id)
        print(f"Hardware associated with model ID: {model_id}")
    
    return updated_hardware

def interactive_update_hardware_setting(manager: HardwareManager, setting_id: int) -> Dict:
    """Interactive hardware setting update"""
    print("\n=== Update Hardware Setting ===")
    
    # We need to know the hardware ID
    hardware_id = int(input("Hardware ID: "))
    
    settings = manager.list_hardware_settings(hardware_id)
    setting = next((s for s in settings if s['id'] == setting_id), None)
    
    if not setting:
        print(f"Setting with ID {setting_id} not found for hardware {hardware_id}")
        sys.exit(1)
    
    print(f"Current values: {json.dumps(setting, indent=2)}")
    
    name = input(f"Setting Name [{setting['name']}]: ") or setting['name']
    macro = input(f"Macro Name [{setting['macro']}]: ") or setting['macro']
    description = input(f"Description [{setting['description']}]: ") or setting['description']
    
    address = input(f"EEPROM Address [{setting['address']}]: ")
    address = int(address) if address else setting['address']
    
    board_name = input(f"Board Name [{setting['boardName']}]: ") or setting['boardName']
    
    board_id = input(f"Board ID [{setting['boardId']}]: ")
    board_id = int(board_id) if board_id else setting['boardId']
    
    # Display parameter type options
    print("\nParameter Type:")
    for i, param_type in enumerate(SettingParamType, 1):
        print(f"{i}. {param_type.value} - {get_param_type_description(param_type.value)}")
    
    current_type_index = next((i for i, t in enumerate(SettingParamType, 1) if t.value == setting['paramType']), 1)
    type_choice = input(f"Select Parameter Type (number) [{current_type_index}]: ") or current_type_index
    param_type = list(SettingParamType)[int(type_choice)-1].value
    
    default_param = input(f"Default Value [{setting['defaultParam']}]: ") or setting['defaultParam']
    
    # Handle min/max values based on parameter type
    if param_type == SettingParamType.BOOLEAN.value:
        min_param = "0"
        max_param = "1"
        print("Min/Max values automatically set to 0/1 for BOOLEAN type")
    else:
        min_param = input(f"Minimum Value [{setting['minParam']}]: ") or setting['minParam']
        max_param = input(f"Maximum Value [{setting['maxParam']}]: ") or setting['maxParam']
    
    is_private = input(f"Is Private? (y/n) [{'y' if setting['isPrivate'] else 'n'}]: ")
    if is_private:
        is_private = is_private.lower() == 'y'
    else:
        is_private = setting['isPrivate']
    
    # Update the setting
    updated_setting = manager.update_hardware_setting(
        setting_id, hardware_id, name, macro, description, address, board_name, board_id,
        param_type, default_param, min_param, max_param, is_private
    )
    
    print(f"Setting updated successfully")
    
    return updated_setting

def display_hardware_list(hardware_list: List[Dict]) -> None:
    """Display a list of hardware in a formatted way"""
    if not hardware_list:
        print("No hardware found.")
        return
    
    print("\n=== Hardware List ===")
    print(f"{'ID':<5} {'Name':<30} {'Type':<15} {'Rev':<5} {'Description':<50}")
    print("-" * 105)
    
    for machine_hardware in hardware_list:
        if 'hardware' in machine_hardware:
            hw = machine_hardware['hardware']
        else:
            hw = machine_hardware
        print(f"{hw['id']:<5} {hw['name']:<30} {hw['type']:<15} {hw['rev']:<5} {hw.get('description', ''):<50}")
        
        # Display settings if they exist
        if 'settings' in hw and hw['settings']:
            print(f"{'':>5} {'Settings:':<30}")
            print(f"{'':>8} {'ID':<5} {'Name':<25} {'Macro':<20} {'Address':<8} {'Board':<15} {'Type':<10} {'Default':<15}")
            print(f"{'':>8} {'-':<5} {'-':<25} {'-':<20} {'-':<8} {'-':<15} {'-':<10} {'-':<15}")
            
            for setting in hw['settings']:
                print(f"{'':>8} {setting['id']:<5} {setting['name'][:25]:<25} {setting['macro'][:20]:<20} {setting['address']:<8} {setting['boardName'][:15]:<15} {setting['paramType'][:10]:<10} {setting['defaultParam'][:15]:<15}")
            
            # Add a blank line after settings for better readability
            print()
        
        # Display profile values if they exist
        if 'profileValues' in hw and hw['profileValues']:
            print(f"{'':>5} {'Profile Values:':<30}")
            print(f"{'':>8} {'ID':<5} {'Name':<25} {'Macro':<20} {'Type':<10} {'Default':<15} {'Category':<15} {'Security':<8}")
            print(f"{'':>8} {'-':<5} {'-':<25} {'-':<20} {'-':<10} {'-':<15} {'-':<15} {'-':<8}")
            
            for profile_value in hw['profileValues']:
                print(f"{'':>8} {profile_value['id']:<5} {profile_value['name'][:25]:<25} {profile_value['macro'][:20]:<20} {profile_value['paramType'][:10]:<10} {profile_value['defaultValue'][:15]:<15} {profile_value['category'][:15]:<15} {profile_value['securityLevel']:<8}")
            
            # Add a blank line after profile values for better readability
            print()
            
        # Display boards if they exist
        if 'boards' in hw and hw['boards']:
            print(f"{'':>5} {'Boards:':<30}")
            print(f"{'':>8} {'ID':<5} {'Protocol':<10} {'PCB Version':<15} {'Pump Rating':<12} {'Application':<15} {'Type':<10}")
            print(f"{'':>8} {'-':<5} {'-':<10} {'-':<15} {'-':<12} {'-':<15} {'-':<10}")
            
            for board in hw['boards']:
                pcb_version = f"{board['pcbMajor']}.{board['pcbMinor']}.{board['pcbPatch']}"
                print(f"{'':>8} {board['id']:<5} {board['protocolId']:<10} {pcb_version:<15} {board['pumpRating']:<12} {board['applicationId']:<15} {board['typeId']:<10}")
            
            # Add a blank line after boards for better readability
            print()

def display_settings_list(settings_list: List[Dict]) -> None:
    """Display a list of hardware settings in a table format"""
    if not settings_list:
        print("No settings found.")
        return
    
    # Print header
    print(f"{'ID':<5} {'Name':<30} {'Macro':<20} {'Type':<10} {'Default':<15} {'Min':<10} {'Max':<10} {'Address':<8} {'Board':<15} {'Private':<8}")
    print("-" * 130)
    
    # Print each setting
    for setting in settings_list:
        print(f"{setting['id']:<5} {setting['name'][:28]:<30} {setting['macro'][:18]:<20} {setting['paramType']:<10} {setting['defaultParam'][:13]:<15} {setting['minParam'][:8]:<10} {setting['maxParam'][:8]:<10} {setting['address']:<8} {setting['boardName'][:13]:<15} {setting['isPrivate']:<8}")

def display_profile_values_list(profile_values_list: List[Dict]) -> None:
    """Display a list of hardware profile values in a table format"""
    if not profile_values_list:
        print("No profile values found.")
        return
    
    # Print header
    print(f"{'ID':<5} {'Name':<30} {'Macro':<20} {'Type':<10} {'Default':<15} {'Min':<10} {'Max':<10} {'Category':<15} {'Security':<8}")
    print("-" * 120)
    
    # Print each profile value
    for profile_value in profile_values_list:
        print(f"{profile_value['id']:<5} {profile_value['name'][:28]:<30} {profile_value['macro'][:18]:<20} {profile_value['paramType'][:10]:<10} {profile_value['defaultValue'][:13]:<15} {profile_value['minValue'][:8]:<10} {profile_value['maxValue'][:8]:<10} {profile_value['category'][:13]:<15} {profile_value['securityLevel']:<8}")

def interactive_create_hardware_profile_value(manager: HardwareManager, hardware_id: int) -> Dict:
    """Create a hardware profile value interactively"""
    print("\nCreating a new hardware profile value")
    print("-------------------------------------")
    
    name = input("Name: ")
    macro = input("Macro: ")
    description = input("Description: ")
    
    # Parameter type
    print("\nParameter Type:")
    for i, param_type in enumerate(ParamType, 1):
        print(f"{i}. {param_type.value} - {get_param_type_description(param_type.value)}")
    
    type_choice = int(input("\nSelect parameter type (1-5): "))
    param_type = list(ParamType)[type_choice-1].value
    
    default_value = input("Default Value: ")
    
    # Handle min/max values based on parameter type
    if param_type == ParamType.BOOLEAN.value:
        min_value = "0"
        max_value = "1"
        print("Min/Max values automatically set to 0/1 for BOOLEAN type")
    elif param_type == ParamType.STRING.value:
        min_value = ""
        max_value = ""
        print("Min/Max values not used for STRING type")
    else:
        min_value = input("Minimum Value: ")
        max_value = input("Maximum Value: ")
    
    category = input("Category (optional): ")
    security_level = int(input("Security Level (1-5, default 1): ") or "1")
    
    # Create the profile value
    profile_value = manager.create_hardware_profile_value(
        hardware_id=hardware_id,
        name=name,
        macro=macro,
        description=description,
        param_type=param_type,
        default_value=default_value,
        min_value=min_value,
        max_value=max_value,
        category=category,
        security_level=security_level
    )
    
    print(f"\nProfile value created with ID: {profile_value['id']}")
    return profile_value

def interactive_update_hardware_profile_value(manager: HardwareManager, profile_value_id: int) -> Dict:
    """Update a hardware profile value interactively"""
    # Get the current profile value
    profile_value = None
    try:
        # We don't have a direct get method, so we'll need to get the hardware first
        # This is a limitation of the current API
        print("Fetching profile value...")
        
        # For now, we'll just create a new profile value with the same ID
        # In a real implementation, you would fetch the profile value first
        
    except Exception as e:
        print(f"Error fetching profile value: {e}")
        return None
    
    print("\nUpdating hardware profile value")
    print("-------------------------------")
    
    # Get the hardware ID first
    hardware_id = int(input("Hardware ID: "))
    
    name = input("Name: ")
    macro = input("Macro: ")
    description = input("Description: ")
    
    # Parameter type
    print("\nParameter Type:")
    for i, param_type in enumerate(ParamType, 1):
        print(f"{i}. {param_type.value} - {get_param_type_description(param_type.value)}")
    
    type_choice = int(input("\nSelect parameter type (1-5): "))
    param_type = list(ParamType)[type_choice-1].value
    
    default_value = input("Default Value: ")
    
    # Handle min/max values based on parameter type
    if param_type == ParamType.BOOLEAN.value:
        min_value = "0"
        max_value = "1"
        print("Min/Max values automatically set to 0/1 for BOOLEAN type")
    elif param_type == ParamType.STRING.value:
        min_value = ""
        max_value = ""
        print("Min/Max values not used for STRING type")
    else:
        min_value = input("Minimum Value: ")
        max_value = input("Maximum Value: ")
    
    category = input("Category (optional): ")
    security_level = int(input("Security Level (1-5, default 1): ") or "1")
    
    # Update the profile value
    updated_profile_value = manager.update_hardware_profile_value(
        profile_value_id=profile_value_id,
        hardware_id=hardware_id,
        name=name,
        macro=macro,
        description=description,
        param_type=param_type,
        default_value=default_value,
        min_value=min_value,
        max_value=max_value,
        category=category,
        security_level=security_level
    )
    
    print(f"\nProfile value updated with ID: {updated_profile_value['id']}")
    return updated_profile_value

def display_boards_list(boards_list: List[Dict]) -> None:
    """Display a list of hardware boards in a table format"""
    if not boards_list:
        print("No boards found.")
        return
    
    # Print header
    print(f"{'ID':<5} {'Protocol':<10} {'PCB Version':<15} {'Pump Rating':<12} {'Application':<15} {'Type':<10}")
    print("-" * 70)
    
    # Print each board
    for board in boards_list:
        pcb_version = f"{board['pcbMajor']}.{board['pcbMinor']}.{board['pcbPatch']}"
        print(f"{board['id']:<5} {board['protocolId']:<10} {pcb_version:<15} {board['pumpRating']:<12} {board['applicationId']:<15} {board['typeId']:<10}")

def interactive_create_hardware_board(manager: HardwareManager, hardware_id: int) -> Dict:
    """Create a hardware board interactively"""
    print("\nCreating a new hardware board")
    print("-----------------------------")
    
    protocol_id = int(input("Protocol ID: "))
    pcb_major = int(input("PCB Major Version: "))
    pcb_minor = int(input("PCB Minor Version: "))
    pcb_patch = int(input("PCB Patch Version: "))
    pump_rating = int(input("Pump Rating (default 0): ") or "0")
    application_id = int(input("Application ID: "))
    type_id = int(input("Type ID: "))
    
    # Create the board
    board = manager.create_hardware_board(
        hardware_id=hardware_id,
        protocol_id=protocol_id,
        pcb_major=pcb_major,
        pcb_minor=pcb_minor,
        pcb_patch=pcb_patch,
        pump_rating=pump_rating,
        application_id=application_id,
        type_id=type_id
    )
    
    print(f"\nBoard created with ID: {board['id']}")
    return board

def interactive_update_hardware_board(manager: HardwareManager, board_id: int) -> Dict:
    """Update a hardware board interactively"""
    print("\nUpdating hardware board")
    print("----------------------")
    
    # Get the hardware ID first
    hardware_id = int(input("Hardware ID: "))
    
    protocol_id = int(input("Protocol ID: "))
    pcb_major = int(input("PCB Major Version: "))
    pcb_minor = int(input("PCB Minor Version: "))
    pcb_patch = int(input("PCB Patch Version: "))
    pump_rating = int(input("Pump Rating (default 0): ") or "0")
    application_id = int(input("Application ID: "))
    type_id = int(input("Type ID: "))
    
    # Update the board
    board = manager.update_hardware_board(
        board_id=board_id,
        hardware_id=hardware_id,
        protocol_id=protocol_id,
        pcb_major=pcb_major,
        pcb_minor=pcb_minor,
        pcb_patch=pcb_patch,
        pump_rating=pump_rating,
        application_id=application_id,
        type_id=type_id
    )
    
    print(f"\nBoard updated with ID: {board['id']}")
    return board

def main():
    parser = argparse.ArgumentParser(description="Hardware Manager CLI")
    parser.add_argument("--api-key", help="API key for authentication")
    parser.add_argument("--api-endpoint", help="API endpoint URL")
    
    subparsers = parser.add_subparsers(dest="command", help="Command to execute")
    
    # List hardware command
    list_parser = subparsers.add_parser("list", help="List hardware")
    list_parser.add_argument("--machine-id", type=int, help="Filter by machine ID")
    list_parser.add_argument("--model-id", type=int, help="Filter by model ID")
    list_parser.add_argument("--include-settings", action="store_true", help="Include settings in the response")
    list_parser.add_argument("--include-profile-values", action="store_true", help="Include profile values in the response")
    list_parser.add_argument("--include-boards", action="store_true", help="Include boards in the response")
    list_parser.add_argument("--include-all", action="store_true", help="Include settings, profile values, and boards in the response")
    list_parser.add_argument("--all-revisions", action="store_true", help="Show all revisions instead of just the latest")
    
    # Get hardware command
    get_parser = subparsers.add_parser("get", help="Get a specific hardware")
    get_parser.add_argument("id", type=int, help="Hardware ID")
    get_parser.add_argument("--include-settings", action="store_true", help="Include settings in the response")
    get_parser.add_argument("--include-profile-values", action="store_true", help="Include profile values in the response")
    
    # Create hardware command
    create_parser = subparsers.add_parser("create", help="Create a new hardware")
    create_parser.add_argument("--name", help="Hardware name")
    create_parser.add_argument("--type", choices=[t.value for t in HardwareType], help="Hardware type")
    create_parser.add_argument("--rev", default="A", help="Hardware revision")
    create_parser.add_argument("--description", help="Hardware description")
    create_parser.add_argument("--model-id", type=int, help="Model ID")
    
    # Create revision command
    create_revision_parser = subparsers.add_parser("create-revision", help="Create a new revision of an existing hardware")
    create_revision_parser.add_argument("id", type=int, help="Hardware ID to create a revision of")
    
    # Get revisions command
    get_revisions_parser = subparsers.add_parser("get-revisions", help="Get all revisions for a specific hardware")
    get_revisions_parser.add_argument("id", type=int, help="Hardware ID")
    
    # Update hardware command
    update_parser = subparsers.add_parser("update", help="Update an existing hardware")
    update_parser.add_argument("id", type=int, help="Hardware ID")
    update_parser.add_argument("--name", help="Hardware name")
    update_parser.add_argument("--type", choices=[t.value for t in HardwareType], help="Hardware type")
    update_parser.add_argument("--rev", help="Hardware revision")
    update_parser.add_argument("--description", help="Hardware description")
    update_parser.add_argument("--model-id", type=int, help="Model ID")
    
    # Delete hardware command
    delete_parser = subparsers.add_parser("delete", help="Delete a hardware")
    delete_parser.add_argument("id", type=int, help="Hardware ID")
    
    # List settings command
    list_settings_parser = subparsers.add_parser("list-settings", help="List hardware settings")
    list_settings_parser.add_argument("--hardware-id", type=int, required=True, help="Hardware ID")
    
    # Create setting command
    create_setting_parser = subparsers.add_parser("create-setting", help="Create a new hardware setting")
    create_setting_parser.add_argument("--hardware-id", type=int, help="Hardware ID")
    create_setting_parser.add_argument("--name", help="Setting name")
    create_setting_parser.add_argument("--macro", help="Macro name")
    create_setting_parser.add_argument("--description", help="Setting description")
    create_setting_parser.add_argument("--address", type=int, help="EEPROM address")
    create_setting_parser.add_argument("--board-name", help="Board name")
    create_setting_parser.add_argument("--board-id", type=int, help="Board ID")
    create_setting_parser.add_argument("--param-type", choices=[t.value for t in SettingParamType], help="Parameter type")
    create_setting_parser.add_argument("--default-param", help="Default value")
    create_setting_parser.add_argument("--min-param", help="Minimum value")
    create_setting_parser.add_argument("--max-param", help="Maximum value")
    create_setting_parser.add_argument("--is-private", action="store_true", help="Is private setting")
    
    # Update setting command
    update_setting_parser = subparsers.add_parser("update-setting", help="Update an existing hardware setting")
    update_setting_parser.add_argument("id", type=int, help="Setting ID")
    update_setting_parser.add_argument("--hardware-id", type=int, help="Hardware ID")
    update_setting_parser.add_argument("--name", help="Setting name")
    update_setting_parser.add_argument("--macro", help="Macro name")
    update_setting_parser.add_argument("--description", help="Setting description")
    update_setting_parser.add_argument("--address", type=int, help="EEPROM address")
    update_setting_parser.add_argument("--board-name", help="Board name")
    update_setting_parser.add_argument("--board-id", type=int, help="Board ID")
    update_setting_parser.add_argument("--param-type", choices=[t.value for t in SettingParamType], help="Parameter type")
    update_setting_parser.add_argument("--default-param", help="Default value")
    update_setting_parser.add_argument("--min-param", help="Minimum value")
    update_setting_parser.add_argument("--max-param", help="Maximum value")
    update_setting_parser.add_argument("--is-private", action="store_true", help="Is private setting")
    
    # Delete setting command
    delete_setting_parser = subparsers.add_parser("delete-setting", help="Delete a hardware setting")
    delete_setting_parser.add_argument("id", type=int, help="Setting ID")
    
    # List profile values command
    list_profile_values_parser = subparsers.add_parser("list-profile-values", help="List hardware profile values")
    list_profile_values_parser.add_argument("--hardware-id", type=int, required=True, help="Hardware ID")
    
    # Create profile value command
    create_profile_value_parser = subparsers.add_parser("create-profile-value", help="Create a new hardware profile value")
    create_profile_value_parser.add_argument("--hardware-id", type=int, help="Hardware ID")
    create_profile_value_parser.add_argument("--name", help="Profile value name")
    create_profile_value_parser.add_argument("--macro", help="Macro name")
    create_profile_value_parser.add_argument("--description", help="Profile value description")
    create_profile_value_parser.add_argument("--param-type", choices=[t.value for t in ParamType], help="Parameter type")
    create_profile_value_parser.add_argument("--default-value", help="Default value")
    create_profile_value_parser.add_argument("--min-value", help="Minimum value")
    create_profile_value_parser.add_argument("--max-value", help="Maximum value")
    create_profile_value_parser.add_argument("--category", help="Category")
    create_profile_value_parser.add_argument("--security-level", type=int, default=1, help="Security level")
    
    # Update profile value command
    update_profile_value_parser = subparsers.add_parser("update-profile-value", help="Update an existing hardware profile value")
    update_profile_value_parser.add_argument("id", type=int, help="Profile value ID")
    update_profile_value_parser.add_argument("--hardware-id", type=int, help="Hardware ID")
    update_profile_value_parser.add_argument("--name", help="Profile value name")
    update_profile_value_parser.add_argument("--macro", help="Macro name")
    update_profile_value_parser.add_argument("--description", help="Profile value description")
    update_profile_value_parser.add_argument("--param-type", choices=[t.value for t in ParamType], help="Parameter type")
    update_profile_value_parser.add_argument("--default-value", help="Default value")
    update_profile_value_parser.add_argument("--min-value", help="Minimum value")
    update_profile_value_parser.add_argument("--max-value", help="Maximum value")
    update_profile_value_parser.add_argument("--category", help="Category")
    update_profile_value_parser.add_argument("--security-level", type=int, help="Security level")
    
    # Delete profile value command
    delete_profile_value_parser = subparsers.add_parser("delete-profile-value", help="Delete a hardware profile value")
    delete_profile_value_parser.add_argument("id", type=int, help="Profile value ID")
    
    # Add hardware to machine command
    add_to_machine_parser = subparsers.add_parser("add-to-machine", help="Add hardware to a machine")
    add_to_machine_parser.add_argument("--machine-id", type=int, required=True, help="Machine ID")
    add_to_machine_parser.add_argument("--hardware-id", type=int, required=True, help="Hardware ID")
    add_to_machine_parser.add_argument("--from-model", action="store_true", help="Is from model")
    
    # Remove hardware from machine command
    remove_from_machine_parser = subparsers.add_parser("remove-from-machine", help="Remove hardware from a machine")
    remove_from_machine_parser.add_argument("--machine-id", type=int, required=True, help="Machine ID")
    remove_from_machine_parser.add_argument("--hardware-id", type=int, required=True, help="Hardware ID")
    
    # Add hardware to model command
    add_to_model_parser = subparsers.add_parser("add-to-model", help="Add hardware to a model")
    add_to_model_parser.add_argument("--model-id", type=int, required=True, help="Model ID")
    add_to_model_parser.add_argument("--hardware-id", type=int, required=True, help="Hardware ID")
    
    # List models command
    list_models_parser = subparsers.add_parser("list-models", help="List models for a hardware")
    list_models_parser.add_argument("--hardware-id", type=int, required=True, help="Hardware ID")
    
    # Remove hardware from model command
    remove_from_model_parser = subparsers.add_parser("remove-from-model", help="Remove hardware from a model")
    remove_from_model_parser.add_argument("--model-id", type=int, required=True, help="Model ID")
    remove_from_model_parser.add_argument("--hardware-id", type=int, required=True, help="Hardware ID")
    
    # Apply hardware command
    apply_parser = subparsers.add_parser("apply", help="Apply hardware settings and profile values to a machine or all machines")
    apply_parser.add_argument("--hardware-id", type=int, required=True, help="Hardware ID")
    apply_parser.add_argument("--machine-id", type=int, help="Machine ID")
    apply_parser.add_argument("--all-machines", action="store_true", help="Apply to all machines with this hardware")
    
    # List boards command
    list_boards_parser = subparsers.add_parser("list-boards", help="List hardware boards")
    list_boards_parser.add_argument("--hardware-id", type=int, required=True, help="Hardware ID")
    
    # Create board command
    create_board_parser = subparsers.add_parser("create-board", help="Create a new hardware board")
    create_board_parser.add_argument("--hardware-id", type=int, help="Hardware ID")
    create_board_parser.add_argument("--protocol-id", type=int, help="Protocol ID")
    create_board_parser.add_argument("--pcb-major", type=int, help="PCB Major Version")
    create_board_parser.add_argument("--pcb-minor", type=int, help="PCB Minor Version")
    create_board_parser.add_argument("--pcb-patch", type=int, help="PCB Patch Version")
    create_board_parser.add_argument("--pump-rating", type=int, default=0, help="Pump Rating")
    create_board_parser.add_argument("--application-id", type=int, help="Application ID")
    create_board_parser.add_argument("--type-id", type=int, help="Type ID")
    
    # Update board command
    update_board_parser = subparsers.add_parser("update-board", help="Update an existing hardware board")
    update_board_parser.add_argument("id", type=int, help="Board ID")
    update_board_parser.add_argument("--hardware-id", type=int, help="Hardware ID")
    update_board_parser.add_argument("--protocol-id", type=int, help="Protocol ID")
    update_board_parser.add_argument("--pcb-major", type=int, help="PCB Major Version")
    update_board_parser.add_argument("--pcb-minor", type=int, help="PCB Minor Version")
    update_board_parser.add_argument("--pcb-patch", type=int, help="PCB Patch Version")
    update_board_parser.add_argument("--pump-rating", type=int, help="Pump Rating")
    update_board_parser.add_argument("--application-id", type=int, help="Application ID")
    update_board_parser.add_argument("--type-id", type=int, help="Type ID")
    
    # Delete board command
    delete_board_parser = subparsers.add_parser("delete-board", help="Delete a hardware board")
    delete_board_parser.add_argument("id", type=int, help="Board ID")

    # Audit machine command
    audit_parser = subparsers.add_parser("audit-machine", help="Audit machine settings against hardware definitions")
    audit_parser.add_argument("--machine-id", type=int, help="Machine ID to audit")
    audit_parser.add_argument("--serial", help="Machine serial number to audit")
    audit_parser.add_argument("--non-interactive", action="store_true", help="Run audit without interactive prompts")
    
    args = parser.parse_args()
    
    # Initialize the hardware manager
    try:
        manager = HardwareManager(args.api_key, args.api_endpoint)
    except ValueError as e:
        print(f"Error: {e}")
        sys.exit(1)
    
    # Execute the command
    if args.command == "list":
        # If include_all is set, set all include flags
        if args.include_all:
            args.include_settings = True
            args.include_profile_values = True
            args.include_boards = True
            
        hardware_list = manager.list_hardware(
            args.include_settings,
            args.machine_id,
            args.model_id,
            args.all_revisions,
            args.include_profile_values,
            args.include_boards
        )
        display_hardware_list(hardware_list)
    
    elif args.command == "get":
        hardware = manager.get_hardware(args.id, args.include_settings, args.include_profile_values)
        print(json.dumps(hardware, indent=2))
    
    elif args.command == "create":
        # Check if we need to run in interactive mode
        if not args.name or not args.type:
            hardware = interactive_create_hardware(manager)
        else:
            hardware = manager.create_hardware(args.name, args.type, args.rev, args.description)
            print(json.dumps(hardware, indent=2))
            
            # If model_id is provided, associate the hardware with the model
            if args.model_id:
                manager.add_hardware_to_model(args.model_id, hardware['id'])
                print(f"Hardware associated with model ID: {args.model_id}")
    
    elif args.command == "update":
        # Check if we need to run in interactive mode
        if not args.name and not args.type and not args.rev and not args.description and args.model_id is None:
            hardware = interactive_update_hardware(manager, args.id)
        else:
            # Get current values for any missing arguments
            current = manager.get_hardware(args.id)
            name = args.name or current['name']
            hardware_type = args.type or current['type']
            rev = args.rev or current['rev']
            description = args.description if args.description is not None else current.get('description', '')
            
            hardware = manager.update_hardware(args.id, name, hardware_type, rev, description)
            print(json.dumps(hardware, indent=2))
            
            # If model_id is provided, associate the hardware with the model
            if args.model_id:
                manager.add_hardware_to_model(args.model_id, hardware['id'])
                print(f"Hardware associated with model ID: {args.model_id}")
    
    elif args.command == "delete":
        result = manager.delete_hardware(args.id)
        print("Hardware deleted successfully")
    
    elif args.command == "list-settings":
        settings = manager.list_hardware_settings(args.hardware_id)
        display_settings_list(settings)
    
    elif args.command == "create-setting":
        # Check if we need to run in interactive mode
        if not all([args.hardware_id, args.name, args.macro, args.description, args.address, 
                   args.board_name, args.board_id, args.param_type, args.default_param, 
                   args.min_param, args.max_param]):
            if not args.hardware_id:
                args.hardware_id = int(input("Hardware ID: "))
            setting = interactive_create_hardware_setting(manager, args.hardware_id)
        else:
            setting = manager.create_hardware_setting(
                args.hardware_id, args.name, args.macro, args.description, args.address,
                args.board_name, args.board_id, args.param_type, args.default_param,
                args.min_param, args.max_param, args.is_private
            )
            print(json.dumps(setting, indent=2))
    
    elif args.command == "update-setting":
        # Check if we need to run in interactive mode
        if not all([args.hardware_id, args.name, args.macro, args.description, args.address, 
                   args.board_name, args.board_id, args.param_type, args.default_param, 
                   args.min_param, args.max_param]):
            setting = interactive_update_hardware_setting(manager, args.id)
        else:
            setting = manager.update_hardware_setting(
                args.id, args.hardware_id, args.name, args.macro, args.description, args.address,
                args.board_name, args.board_id, args.param_type, args.default_param,
                args.min_param, args.max_param, args.is_private
            )
            print(json.dumps(setting, indent=2))
    
    elif args.command == "delete-setting":
        result = manager.delete_hardware_setting(args.id)
        print("Hardware setting deleted successfully")
    
    elif args.command == "list-profile-values":
        profile_values = manager.list_hardware_profile_values(args.hardware_id)
        display_profile_values_list(profile_values)
    
    elif args.command == "create-profile-value":
        # Check if we need to run in interactive mode
        if not all([args.hardware_id, args.name, args.macro, args.description, args.param_type, 
                   args.default_value, args.min_value, args.max_value]):
            if not args.hardware_id:
                args.hardware_id = int(input("Hardware ID: "))
            profile_value = interactive_create_hardware_profile_value(manager, args.hardware_id)
        else:
            profile_value = manager.create_hardware_profile_value(
                args.hardware_id, args.name, args.macro, args.description, args.param_type,
                args.default_value, args.min_value, args.max_value, args.category, args.security_level
            )
            print(json.dumps(profile_value, indent=2))
    
    elif args.command == "update-profile-value":
        # Check if we need to run in interactive mode
        if not all([args.hardware_id, args.name, args.macro, args.description, args.param_type, 
                   args.default_value, args.min_value, args.max_value]):
            profile_value = interactive_update_hardware_profile_value(manager, args.id)
        else:
            profile_value = manager.update_hardware_profile_value(
                args.id, args.hardware_id, args.name, args.macro, args.description, args.param_type,
                args.default_value, args.min_value, args.max_value, args.category, args.security_level
            )
            print(json.dumps(profile_value, indent=2))
    
    elif args.command == "delete-profile-value":
        result = manager.delete_hardware_profile_value(args.id)
        print("Hardware profile value deleted successfully")
    
    elif args.command == "add-to-machine":
        result = manager.add_hardware_to_machine(args.machine_id, args.hardware_id, args.from_model)
        print("Hardware added to machine successfully")
    
    elif args.command == "remove-from-machine":
        result = manager.remove_hardware_from_machine(args.machine_id, args.hardware_id)
        print("Hardware removed from machine successfully")
    
    elif args.command == "add-to-model":
        result = manager.add_hardware_to_model(args.model_id, args.hardware_id)
        print("Hardware added to model successfully")
    
    elif args.command == "list-models":
        try:
            models = manager.list_models_for_hardware(args.hardware_id)
            if not models:
                print(f"No models found for hardware ID: {args.hardware_id}")
            else:
                print(f"\n=== Models for Hardware ID: {args.hardware_id} ===")
                print(f"{'ID':<5} {'Name':<30} {'Type':<10} {'Rev':<5}")
                print("-" * 50)
                for model in models:
                    print(f"{model['id']:<5} {model['name']:<30} {model['type']:<10} {model.get('rev', ''):<5}")
        except Exception as e:
            print(f"Error: {e}")
            # If the endpoint doesn't exist, provide a helpful message
            print("Note: This endpoint may not be implemented in the API yet.")
    
    elif args.command == "remove-from-model":
        result = manager.remove_hardware_from_model(args.model_id, args.hardware_id)
        print("Hardware removed from model successfully")
    
    elif args.command == "create-revision":
        result = manager.create_revision(args.id)
        print("Hardware revision created successfully")
    
    elif args.command == "get-revisions":
        try:
            revisions = manager.get_revisions(args.id)
            if not revisions:
                print(f"No revisions found for hardware ID: {args.id}")
            else:
                print(f"\n=== Revisions for Hardware ID: {args.id} ===")
                print(f"{'ID':<5} {'Name':<30} {'Rev':<5} {'Type':<10} {'Description':<50}")
                print("-" * 100)
                for revision in revisions:
                    print(f"{revision['id']:<5} {revision['name']:<30} {revision['rev']:<5} {revision['type']:<10} {revision.get('description', ''):<50}")
        except Exception as e:
            print(f"Error: {e}")
    
    elif args.command == "apply":
        result = manager.apply_hardware(args.hardware_id, args.machine_id, args.all_machines)
        print("Hardware applied successfully")
    
    elif args.command == "list-boards":
        boards = manager.list_hardware_boards(args.hardware_id)
        display_boards_list(boards)
    
    elif args.command == "create-board":
        # Check if we need to run in interactive mode
        if not all([args.hardware_id, args.protocol_id, args.pcb_major, args.pcb_minor, args.pcb_patch,
                   args.application_id, args.type_id]):
            if not args.hardware_id:
                args.hardware_id = int(input("Hardware ID: "))
            board = interactive_create_hardware_board(manager, args.hardware_id)
        else:
            board = manager.create_hardware_board(
                args.hardware_id, args.protocol_id, args.pcb_major, args.pcb_minor,
                args.pcb_patch, args.application_id, args.type_id, args.pump_rating
            )
            print(json.dumps(board, indent=2))
    
    elif args.command == "update-board":
        # Check if we need to run in interactive mode
        if not all([args.hardware_id, args.protocol_id, args.pcb_major, args.pcb_minor, args.pcb_patch,
                   args.application_id, args.type_id]):
            board = interactive_update_hardware_board(manager, args.id)
        else:
            board = manager.update_hardware_board(
                args.id, args.hardware_id, args.protocol_id, args.pcb_major, args.pcb_minor,
                args.pcb_patch, args.application_id, args.type_id, args.pump_rating
            )
            print(json.dumps(board, indent=2))
    
    elif args.command == "delete-board":
        result = manager.delete_hardware_board(args.id)
        print("Hardware board deleted successfully")

    elif args.command == "audit-machine":
        # Determine machine ID
        machine_id = args.machine_id

        if not machine_id and args.serial:
            # Find machine by serial number
            machines = manager.list_machines()
            if args.serial in machines:
                machine_id = int(machines[args.serial])
            else:
                # Try matching last 5 digits
                serial_suffix = args.serial[-5:]
                if serial_suffix in machines:
                    machine_id = int(machines[serial_suffix])

        if not machine_id:
            if args.serial:
                print(f"❌ Machine with serial number {args.serial} not found")
                machines = manager.list_machines()
                print(f"Available machines: {list(machines.keys())}")
            else:
                print("❌ Either --machine-id or --serial must be provided")
            sys.exit(1)

        # Run audit
        interactive = not args.non_interactive
        audit_results = manager.audit_machine_settings(machine_id, interactive)

        if "error" in audit_results:
            print(f"❌ Audit failed: {audit_results['error']}")
            sys.exit(1)

    else:
        parser.print_help()


if __name__ == "__main__":
    main()


